# AI绘画解析功能验证指南

## 🎨 核心AI绘画解析功能确认

### ✅ 已确认保留的核心功能

#### 1. **A1111 WebUI 参数解析**
- **正向提示词**: 从 `parameters` 字段提取主要提示词
- **负面提示词**: 解析 "Negative prompt:" 后的内容
- **生成参数**: 解析 "Steps:" 后的所有参数
  - Steps (采样步数)
  - CFG Scale (提示词相关性)
  - Sampler (采样器)
  - Seed (随机种子)
  - Size (图片尺寸)
  - Model hash (模型哈希)
  - Denoising strength (降噪强度)
  - 等等...

#### 2. **ComfyUI 工作流解析**
- **Workflow**: 完整的节点工作流JSON
- **Prompt**: 提示词节点配置
- **参数提取**: 从节点中提取关键参数

#### 3. **模型文件解析**
- **Safetensors 元数据**: 解析 `__metadata__` 字段
- **Kohya SS 训练参数**: 训练配置和参数
- **模型类型识别**: 自动识别模型种类和用途
- **文件信息**: 大小、格式、签名检测

#### 4. **隐写术检测**
- **Alpha通道LSB**: 从图片透明通道提取隐藏数据
- **Stealth Exif**: 检测隐藏的元数据
- **自动回退**: 标准元数据失败时自动尝试隐写术

#### 5. **多格式支持**
- **图片格式**: PNG, JPEG, WEBP, BMP, AVIF
- **模型格式**: PT, PTH, CKPT, SAFETENSORS, BIN
- **元数据来源**: PNG chunks, EXIF, 隐写术

## 🧪 测试步骤

### 测试A1111 WebUI生成的图片
1. **上传PNG图片**: 包含parameters字段的AI生成图片
2. **验证解析结果**:
   - ✅ 提示词 (正向)
   - ✅ 负面提示词
   - ✅ Steps (采样步数)
   - ✅ CFG Scale
   - ✅ Sampler (采样器名称)
   - ✅ Seed (随机种子)
   - ✅ Size (宽x高)
   - ✅ Model hash
   - ✅ 其他参数

### 测试ComfyUI生成的图片
1. **上传包含workflow的图片**
2. **验证解析结果**:
   - ✅ Workflow (JSON格式)
   - ✅ Prompt配置
   - ✅ 节点参数

### 测试模型文件
1. **上传Safetensors模型**
2. **验证解析结果**:
   - ✅ 文件信息 (名称、大小)
   - ✅ 推测模型种类
   - ✅ 常见用途
   - ✅ 训练参数 (如果有)

### 测试隐写术图片
1. **上传包含隐藏数据的图片**
2. **验证解析结果**:
   - ✅ 自动检测隐写术
   - ✅ 提取隐藏的提示词
   - ✅ 显示数据来源为 "Stealth Exif"

## 📊 关键参数说明

### AI绘画核心参数
- **Prompt (提示词)**: 描述想要生成内容的文本
- **Negative Prompt (负面提示词)**: 描述不想要的内容
- **Steps**: 扩散模型的采样步数，通常20-50
- **CFG Scale**: 提示词遵循程度，通常7-15
- **Sampler**: 采样算法，如DPM++、Euler等
- **Seed**: 随机种子，控制生成的随机性
- **Size**: 图片尺寸，如512x512、768x768
- **Model**: 使用的AI模型名称或哈希

### 高级参数
- **Denoising Strength**: 图生图时的降噪强度
- **Clip Skip**: CLIP层跳过数量
- **ENSD**: 噪声种子
- **Hires Fix**: 高分辨率修复设置
- **ControlNet**: 控制网络参数

## 🔍 UI显示验证

### 数据展示检查
1. **分类标签**: 
   - 🔵 提示词 (primary)
   - 🟢 参数 (success) 
   - 🟡 模型 (warning)
   - 🔵 尺寸 (info)

2. **搜索功能**: 
   - 输入 "CFG" 应该找到CFG Scale
   - 输入 "Steps" 应该找到采样步数
   - 输入 "提示词" 应该找到相关内容

3. **复制功能**: 
   - 提示词内容可以一键复制
   - 参数信息可以复制
   - JSON数据可以复制

4. **JSON查看器**: 
   - ComfyUI workflow显示为可展开的JSON
   - Kohya训练参数显示为JSON格式

## ⚠️ 常见问题排查

### 如果解析失败
1. **检查文件格式**: 确保是支持的格式
2. **检查元数据**: 某些图片可能没有元数据
3. **尝试隐写术**: 系统会自动尝试隐写术检测
4. **查看控制台**: 检查是否有错误信息

### 如果显示不完整
1. **检查搜索框**: 可能被搜索过滤了
2. **检查分类**: 使用标签筛选查看
3. **展开JSON**: 点击JSON查看器展开详细信息

## 🎯 预期结果

正确的AI绘画解析应该显示：
- ✅ 完整的提示词信息
- ✅ 所有生成参数
- ✅ 模型相关信息
- ✅ 正确的数据分类和标签
- ✅ 可搜索和复制的内容
- ✅ 美观的现代化界面

**所有原有的AI绘画解析功能都已完整保留，只是界面更加美观易用！** 🎉
