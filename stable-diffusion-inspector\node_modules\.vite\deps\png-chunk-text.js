import {
  __commonJS
} from "./chunk-TWLJ45QX.js";

// node_modules/png-chunk-text/encode.js
var require_encode = __commonJS({
  "node_modules/png-chunk-text/encode.js"(exports, module) {
    module.exports = encode;
    function encode(keyword, content) {
      keyword = String(keyword);
      content = String(content);
      if (!/^[\x00-\xFF]+$/.test(keyword) || !/^[\x00-\xFF]+$/.test(content)) {
        throw new Error("Only Latin-1 characters are permitted in PNG tEXt chunks. You might want to consider base64 encoding and/or zEXt compression");
      }
      if (keyword.length >= 80) {
        throw new Error('Keyword "' + keyword + '" is longer than the 79-character limit imposed by the PNG specification');
      }
      var totalSize = keyword.length + content.length + 1;
      var output = new Uint8Array(totalSize);
      var idx = 0;
      var code;
      for (var i = 0; i < keyword.length; i++) {
        if (!(code = keyword.charCodeAt(i))) {
          throw new Error("0x00 character is not permitted in tEXt keywords");
        }
        output[idx++] = code;
      }
      output[idx++] = 0;
      for (var j = 0; j < content.length; j++) {
        if (!(code = content.charCodeAt(j))) {
          throw new Error("0x00 character is not permitted in tEXt content");
        }
        output[idx++] = code;
      }
      return {
        name: "tEXt",
        data: output
      };
    }
  }
});

// node_modules/png-chunk-text/decode.js
var require_decode = __commonJS({
  "node_modules/png-chunk-text/decode.js"(exports, module) {
    module.exports = decode;
    function decode(data) {
      if (data.data && data.name) {
        data = data.data;
      }
      var naming = true;
      var text = "";
      var name = "";
      for (var i = 0; i < data.length; i++) {
        var code = data[i];
        if (naming) {
          if (code) {
            name += String.fromCharCode(code);
          } else {
            naming = false;
          }
        } else {
          if (code) {
            text += String.fromCharCode(code);
          } else {
            throw new Error("Invalid NULL character found. 0x00 character is not permitted in tEXt content");
          }
        }
      }
      return {
        keyword: name,
        text
      };
    }
  }
});

// node_modules/png-chunk-text/index.js
var require_png_chunk_text = __commonJS({
  "node_modules/png-chunk-text/index.js"(exports) {
    exports.encode = require_encode();
    exports.decode = require_decode();
  }
});
export default require_png_chunk_text();
//# sourceMappingURL=png-chunk-text.js.map
