import {
  __commonJS
} from "./chunk-TWLJ45QX.js";

// node_modules/crc-32/crc32.js
var require_crc32 = __commonJS({
  "node_modules/crc-32/crc32.js"(exports) {
    var CRC32;
    (function(factory) {
      if (typeof DO_NOT_EXPORT_CRC === "undefined") {
        if ("object" === typeof exports) {
          factory(exports);
        } else if ("function" === typeof define && define.amd) {
          define(function() {
            var module2 = {};
            factory(module2);
            return module2;
          });
        } else {
          factory(CRC32 = {});
        }
      } else {
        factory(CRC32 = {});
      }
    })(function(CRC322) {
      CRC322.version = "0.3.0";
      function signed_crc_table() {
        var c = 0, table2 = new Array(256);
        for (var n = 0; n != 256; ++n) {
          c = n;
          c = c & 1 ? -306674912 ^ c >>> 1 : c >>> 1;
          c = c & 1 ? -306674912 ^ c >>> 1 : c >>> 1;
          c = c & 1 ? -306674912 ^ c >>> 1 : c >>> 1;
          c = c & 1 ? -306674912 ^ c >>> 1 : c >>> 1;
          c = c & 1 ? -306674912 ^ c >>> 1 : c >>> 1;
          c = c & 1 ? -306674912 ^ c >>> 1 : c >>> 1;
          c = c & 1 ? -306674912 ^ c >>> 1 : c >>> 1;
          c = c & 1 ? -306674912 ^ c >>> 1 : c >>> 1;
          table2[n] = c;
        }
        return typeof Int32Array !== "undefined" ? new Int32Array(table2) : table2;
      }
      var table = signed_crc_table();
      var use_buffer = typeof Buffer !== "undefined";
      function crc32_bstr(bstr) {
        if (bstr.length > 32768) {
          if (use_buffer)
            return crc32_buf_8(new Buffer(bstr));
        }
        var crc = -1, L = bstr.length - 1;
        for (var i = 0; i < L; ) {
          crc = table[(crc ^ bstr.charCodeAt(i++)) & 255] ^ crc >>> 8;
          crc = table[(crc ^ bstr.charCodeAt(i++)) & 255] ^ crc >>> 8;
        }
        if (i === L)
          crc = crc >>> 8 ^ table[(crc ^ bstr.charCodeAt(i)) & 255];
        return crc ^ -1;
      }
      function crc32_buf(buf) {
        if (buf.length > 1e4)
          return crc32_buf_8(buf);
        for (var crc = -1, i = 0, L = buf.length - 3; i < L; ) {
          crc = crc >>> 8 ^ table[(crc ^ buf[i++]) & 255];
          crc = crc >>> 8 ^ table[(crc ^ buf[i++]) & 255];
          crc = crc >>> 8 ^ table[(crc ^ buf[i++]) & 255];
          crc = crc >>> 8 ^ table[(crc ^ buf[i++]) & 255];
        }
        while (i < L + 3)
          crc = crc >>> 8 ^ table[(crc ^ buf[i++]) & 255];
        return crc ^ -1;
      }
      function crc32_buf_8(buf) {
        for (var crc = -1, i = 0, L = buf.length - 7; i < L; ) {
          crc = crc >>> 8 ^ table[(crc ^ buf[i++]) & 255];
          crc = crc >>> 8 ^ table[(crc ^ buf[i++]) & 255];
          crc = crc >>> 8 ^ table[(crc ^ buf[i++]) & 255];
          crc = crc >>> 8 ^ table[(crc ^ buf[i++]) & 255];
          crc = crc >>> 8 ^ table[(crc ^ buf[i++]) & 255];
          crc = crc >>> 8 ^ table[(crc ^ buf[i++]) & 255];
          crc = crc >>> 8 ^ table[(crc ^ buf[i++]) & 255];
          crc = crc >>> 8 ^ table[(crc ^ buf[i++]) & 255];
        }
        while (i < L + 7)
          crc = crc >>> 8 ^ table[(crc ^ buf[i++]) & 255];
        return crc ^ -1;
      }
      function crc32_str(str) {
        for (var crc = -1, i = 0, L = str.length, c, d; i < L; ) {
          c = str.charCodeAt(i++);
          if (c < 128) {
            crc = crc >>> 8 ^ table[(crc ^ c) & 255];
          } else if (c < 2048) {
            crc = crc >>> 8 ^ table[(crc ^ (192 | c >> 6 & 31)) & 255];
            crc = crc >>> 8 ^ table[(crc ^ (128 | c & 63)) & 255];
          } else if (c >= 55296 && c < 57344) {
            c = (c & 1023) + 64;
            d = str.charCodeAt(i++) & 1023;
            crc = crc >>> 8 ^ table[(crc ^ (240 | c >> 8 & 7)) & 255];
            crc = crc >>> 8 ^ table[(crc ^ (128 | c >> 2 & 63)) & 255];
            crc = crc >>> 8 ^ table[(crc ^ (128 | d >> 6 & 15 | c & 3)) & 255];
            crc = crc >>> 8 ^ table[(crc ^ (128 | d & 63)) & 255];
          } else {
            crc = crc >>> 8 ^ table[(crc ^ (224 | c >> 12 & 15)) & 255];
            crc = crc >>> 8 ^ table[(crc ^ (128 | c >> 6 & 63)) & 255];
            crc = crc >>> 8 ^ table[(crc ^ (128 | c & 63)) & 255];
          }
        }
        return crc ^ -1;
      }
      CRC322.table = table;
      CRC322.bstr = crc32_bstr;
      CRC322.buf = crc32_buf;
      CRC322.str = crc32_str;
    });
  }
});

// node_modules/png-chunks-extract/index.js
var require_png_chunks_extract = __commonJS({
  "node_modules/png-chunks-extract/index.js"(exports, module) {
    var crc32 = require_crc32();
    module.exports = extractChunks;
    var uint8 = new Uint8Array(4);
    var int32 = new Int32Array(uint8.buffer);
    var uint32 = new Uint32Array(uint8.buffer);
    function extractChunks(data) {
      if (data[0] !== 137)
        throw new Error("Invalid .png file header");
      if (data[1] !== 80)
        throw new Error("Invalid .png file header");
      if (data[2] !== 78)
        throw new Error("Invalid .png file header");
      if (data[3] !== 71)
        throw new Error("Invalid .png file header");
      if (data[4] !== 13)
        throw new Error("Invalid .png file header: possibly caused by DOS-Unix line ending conversion?");
      if (data[5] !== 10)
        throw new Error("Invalid .png file header: possibly caused by DOS-Unix line ending conversion?");
      if (data[6] !== 26)
        throw new Error("Invalid .png file header");
      if (data[7] !== 10)
        throw new Error("Invalid .png file header: possibly caused by DOS-Unix line ending conversion?");
      var ended = false;
      var chunks = [];
      var idx = 8;
      while (idx < data.length) {
        uint8[3] = data[idx++];
        uint8[2] = data[idx++];
        uint8[1] = data[idx++];
        uint8[0] = data[idx++];
        var length = uint32[0] + 4;
        var chunk = new Uint8Array(length);
        chunk[0] = data[idx++];
        chunk[1] = data[idx++];
        chunk[2] = data[idx++];
        chunk[3] = data[idx++];
        var name = String.fromCharCode(chunk[0]) + String.fromCharCode(chunk[1]) + String.fromCharCode(chunk[2]) + String.fromCharCode(chunk[3]);
        if (!chunks.length && name !== "IHDR") {
          throw new Error("IHDR header missing");
        }
        if (name === "IEND") {
          ended = true;
          chunks.push({
            name,
            data: new Uint8Array(0)
          });
          break;
        }
        for (var i = 4; i < length; i++) {
          chunk[i] = data[idx++];
        }
        uint8[3] = data[idx++];
        uint8[2] = data[idx++];
        uint8[1] = data[idx++];
        uint8[0] = data[idx++];
        var crcActual = int32[0];
        var crcExpect = crc32.buf(chunk);
        if (crcExpect !== crcActual) {
          throw new Error(
            "CRC values for " + name + " header do not match, PNG file is likely corrupted"
          );
        }
        var chunkData = new Uint8Array(chunk.buffer.slice(4));
        chunks.push({
          name,
          data: chunkData
        });
      }
      if (!ended) {
        throw new Error(".png file ended prematurely: no IEND header was found");
      }
      return chunks;
    }
  }
});
export default require_png_chunks_extract();
//# sourceMappingURL=png-chunks-extract.js.map
