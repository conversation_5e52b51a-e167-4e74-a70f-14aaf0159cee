{"version": 3, "sources": ["../../exifreader/src/dataview.js", "../../exifreader/src/utils.js", "../../exifreader/src/constants.js", "../../exifreader/src/tag-names-utils.js", "../../exifreader/src/byte-order.js", "../../exifreader/src/image-header-tiff.js", "../../exifreader/src/image-header-jpeg.js", "../../exifreader/src/image-header-png.js", "../../exifreader/src/image-header-iso-bmff-utils.js", "../../exifreader/src/image-header-iso-bmff-iloc.js", "../../exifreader/src/image-header-iso-bmff.js", "../../exifreader/src/image-header-heic.js", "../../exifreader/src/image-header-avif.js", "../../exifreader/src/image-header-webp.js", "../../exifreader/src/image-header-gif.js", "../../exifreader/src/xml.js", "../../exifreader/src/image-header.js", "../../exifreader/src/tag-names-common.js", "../../exifreader/src/tag-names-0th-ifd.js", "../../exifreader/src/tag-names-exif-ifd.js", "../../exifreader/src/tag-names-gps-ifd.js", "../../exifreader/src/tag-names-interoperability-ifd.js", "../../exifreader/src/tag-names-mpf-ifd.js", "../../exifreader/src/tag-names-canon-ifd.js", "../../exifreader/src/tag-names.js", "../../exifreader/src/types.js", "../../exifreader/src/tags-helpers.js", "../../exifreader/src/tags.js", "../../exifreader/src/mpf-tags.js", "../../exifreader/src/file-tags.js", "../../exifreader/src/jfif-tags.js", "../../exifreader/src/iptc-tag-names.js", "../../exifreader/src/text-decoder.js", "../../exifreader/src/tag-decoder.js", "../../exifreader/src/iptc-tags.js", "../../exifreader/src/xmp-tag-names.js", "../../exifreader/src/dom-parser.js", "../../exifreader/src/xmp-tags.js", "../../exifreader/src/photoshop-tag-names.js", "../../exifreader/src/photoshop-tags.js", "../../exifreader/src/icc-tag-names.js", "../../exifreader/src/icc-tags.js", "../../exifreader/src/canon-tags.js", "../../exifreader/src/png-file-tags.js", "../../exifreader/src/png-text-tags.js", "../../exifreader/src/png-tags.js", "../../exifreader/src/vp8x-tags.js", "../../exifreader/src/gif-file-tags.js", "../../exifreader/src/thumbnail.js", "../../exifreader/src/errors.js", "../../exifreader/src/exif-reader.js"], "sourcesContent": ["export default class DataView {\n    constructor(buffer) {\n        if (bufferTypeIsUnsupported(buffer)) {\n            throw new Error('DataView: Passed buffer type is unsupported.');\n        }\n\n        this.buffer = buffer;\n        this.byteLength = this.buffer.length;\n    }\n\n    getUint8(offset) {\n        return this.buffer.readUInt8(offset);\n    }\n\n    getUint16(offset, littleEndian) {\n        if (littleEndian) {\n            return this.buffer.readUInt16LE(offset);\n        }\n        return this.buffer.readUInt16BE(offset);\n    }\n\n    getUint32(offset, littleEndian) {\n        if (littleEndian) {\n            return this.buffer.readUInt32LE(offset);\n        }\n        return this.buffer.readUInt32BE(offset);\n    }\n\n    getInt32(offset, littleEndian) {\n        if (littleEndian) {\n            return this.buffer.readInt32LE(offset);\n        }\n        return this.buffer.readInt32BE(offset);\n    }\n}\n\nfunction bufferTypeIsUnsupported(buffer) {\n    return typeof buffer !== 'object'\n        || buffer.length === undefined\n        || buffer.readUInt8 === undefined\n        || buffer.readUInt16LE === undefined\n        || buffer.readUInt16BE === undefined\n        || buffer.readUInt32LE === undefined\n        || buffer.readUInt32BE === undefined\n        || buffer.readInt32LE === undefined\n        || buffer.readInt32BE === undefined;\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport DataViewWrapper from './dataview.js';\n\nexport function getDataView(data, byteOffset, byteLength) {\n    try {\n        return new DataView(data, byteOffset, byteLength);\n    } catch (error) {\n        return new DataViewWrapper(data, byteOffset, byteLength);\n    }\n}\n\nexport function getStringFromDataView(dataView, offset, length) {\n    const chars = [];\n    for (let i = 0; i < length && offset + i < dataView.byteLength; i++) {\n        chars.push(dataView.getUint8(offset + i));\n    }\n    return getStringValueFromArray(chars);\n}\n\nexport function getNullTerminatedStringFromDataView(dataView, offset) {\n    const chars = [];\n    let i = 0;\n    while (offset + i < dataView.byteLength) {\n        const char = dataView.getUint8(offset + i);\n        if (char === 0) {\n            break;\n        }\n        chars.push(char);\n        i++;\n    }\n    return getStringValueFromArray(chars);\n}\n\nexport function getUnicodeStringFromDataView(dataView, offset, length) {\n    const chars = [];\n    for (let i = 0; i < length && offset + i < dataView.byteLength; i += 2) {\n        chars.push(dataView.getUint16(offset + i));\n    }\n    if (chars[chars.length - 1] === 0) {\n        chars.pop();\n    }\n    return getStringValueFromArray(chars);\n}\n\nexport function getPascalStringFromDataView(dataView, offset) {\n    const size = dataView.getUint8(offset);\n    const string = getStringFromDataView(dataView, offset + 1, size);\n    return [size, string];\n}\n\nexport function getStringValueFromArray(charArray) {\n    return charArray.map((charCode) => String.fromCharCode(charCode)).join('');\n}\n\nexport function getCharacterArray(string) {\n    return string.split('').map((character) => character.charCodeAt(0));\n}\n\nexport function objectAssign() {\n    for (let i = 1; i < arguments.length; i++) {\n        for (const property in arguments[i]) {\n            arguments[0][property] = arguments[i][property];\n        }\n    }\n\n    return arguments[0];\n}\n\nexport function deferInit(object, key, initializer) {\n    let initialized = false;\n    Object.defineProperty(object, key, {\n        get() {\n            if (!initialized) {\n                initialized = true;\n                Object.defineProperty(object, key, {\n                    configurable: true,\n                    enumerable: true,\n                    value: initializer.apply(object),\n                    writable: true\n                });\n            }\n            return object[key];\n        },\n        configurable: true,\n        enumerable: true\n    });\n}\n\nexport function getBase64Image(image) {\n    if (typeof btoa !== 'undefined') {\n        if (typeof image === 'string') {\n            // This only happens during the build tests using Node 16+ (npm run test:build).\n            return btoa(image);\n        }\n        // IE11- does not implement reduce on the Uint8Array prototype.\n        return btoa(Array.prototype.reduce.call(new Uint8Array(image), (data, byte) => data + String.fromCharCode(byte), ''));\n    }\n    if (typeof Buffer === 'undefined') {\n        return undefined;\n    }\n    if (typeof Buffer.from !== 'undefined') { // eslint-disable-line no-undef\n        return Buffer.from(image).toString('base64'); // eslint-disable-line no-undef\n    }\n    return (new Buffer(image)).toString('base64'); // eslint-disable-line no-undef\n}\n\nexport function dataUriToBuffer(dataUri) {\n    const data = dataUri.substring(dataUri.indexOf(',') + 1);\n\n    if (dataUri.indexOf(';base64') !== -1) {\n        if (typeof atob !== 'undefined') {\n            return Uint8Array.from(atob(data), (char) => char.charCodeAt(0)).buffer;\n        }\n        if (typeof Buffer === 'undefined') {\n            return undefined;\n        }\n        if (typeof Buffer.from !== 'undefined') { // eslint-disable-line no-undef\n            return Buffer.from(data, 'base64'); // eslint-disable-line no-undef\n        }\n        return new Buffer(data, 'base64'); // eslint-disable-line no-undef\n    }\n\n    const decodedData = decodeURIComponent(data);\n    if (typeof Buffer !== 'undefined') {\n        if (typeof Buffer.from !== 'undefined') { // eslint-disable-line no-undef\n            return Buffer.from(decodedData); // eslint-disable-line no-undef\n        }\n        return new Buffer(decodedData); // eslint-disable-line no-undef\n    }\n    return Uint8Array.from(decodedData, (char) => char.charCodeAt(0)).buffer;\n}\n\nexport function padStart(string, length, character) {\n    const padding = strRepeat(character, length - string.length);\n    return padding + string;\n}\n\nexport function parseFloatRadix(string, radix) {\n    return parseInt(string.replace('.', ''), radix)\n        / Math.pow(radix, (string.split('.')[1] || '').length);\n}\n\nexport function strRepeat(string, num) {\n    return new Array(num + 1).join(string);\n}\n\nexport const COMPRESSION_METHOD_NONE = undefined;\nexport const COMPRESSION_METHOD_DEFLATE = 0;\n\nexport function decompress(dataView, compressionMethod, encoding, returnType = 'string') {\n    if (compressionMethod === COMPRESSION_METHOD_DEFLATE) {\n        if (typeof DecompressionStream === 'function') {\n            const decompressionStream = new DecompressionStream('deflate');\n            const decompressedStream = new Blob([dataView]).stream().pipeThrough(decompressionStream);\n            if (returnType === 'dataview') {\n                return new Response(decompressedStream).arrayBuffer().then((arrayBuffer) => new DataView(arrayBuffer));\n            }\n            return new Response(decompressedStream).arrayBuffer()\n                .then((buffer) => new TextDecoder(encoding).decode(buffer));\n        }\n    }\n    if (compressionMethod !== undefined) {\n        return Promise.reject(`Unknown compression method ${compressionMethod}.`);\n    }\n    return dataView;\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nexport default {\n    USE_FILE: true,\n    USE_JFIF: true,\n    USE_PNG_FILE: true,\n    USE_EXIF: true,\n    USE_IPTC: true,\n    USE_XMP: true,\n    USE_ICC: true,\n    USE_MPF: true,\n    USE_PHOTOSHOP: true,\n    USE_THUMBNAIL: true,\n    USE_TIFF: true,\n    USE_JPEG: true,\n    USE_PNG: true,\n    USE_HEIC: true,\n    USE_AVIF: true,\n    USE_WEBP: true,\n    USE_GIF: true,\n    USE_MAKER_NOTES: true\n};\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nexport function getStringValue(value) {\n    return value.map((charCode) => String.fromCharCode(charCode)).join('');\n}\n\nexport function getEncodedString(value) {\n    if (value.length >= 8) {\n        const encoding = getStringValue(value.slice(0, 8));\n\n        if (encoding === 'ASCII\\x00\\x00\\x00') {\n            return getStringValue(value.slice(8));\n        } else if (encoding === 'JIS\\x00\\x00\\x00\\x00\\x00') {\n            return '[JIS encoded text]';\n        } else if (encoding === 'UNICODE\\x00') {\n            return '[Unicode encoded text]';\n        } else if (encoding === '\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00') {\n            return '[Undefined encoding]';\n        }\n    }\n\n    return 'Undefined';\n}\n\nexport function getCalculatedGpsValue(value) {\n    return (value[0][0] / value[0][1]) + (value[1][0] / value[1][1]) / 60 + (value[2][0] / value[2][1]) / 3600;\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nconst LITTLE_ENDIAN = 0x4949;\nconst BIG_ENDIAN = 0x4d4d;\n\nexport default {\n    BIG_ENDIAN,\n    LITTLE_ENDIAN,\n    getByteOrder\n};\n\nfunction getByteOrder(dataView, tiffHeaderOffset) {\n    if (dataView.getUint16(tiffHeaderOffset) === LITTLE_ENDIAN) {\n        return LITTLE_ENDIAN;\n    } else if (dataView.getUint16(tiffHeaderOffset) === BIG_ENDIAN) {\n        return BIG_ENDIAN;\n    }\n    throw new Error('Illegal byte order value. Faulty image.');\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport ByteOrder from './byte-order.js';\nimport Constants from './constants.js';\n\nexport default {\n    isTiffFile,\n    findTiffOffsets\n};\n\nfunction isTiffFile(dataView) {\n    const MIN_TIFF_DATA_BUFFER_LENGTH = 4;\n\n    return !!dataView && (dataView.byteLength >= MIN_TIFF_DATA_BUFFER_LENGTH) && hasTiffMarker(dataView);\n}\n\nfunction hasTiffMarker(dataView) {\n    const TIFF_ID = 0x2a;\n    const TIFF_ID_OFFSET = 2;\n\n    const littleEndian = dataView.getUint16(0) === ByteOrder.LITTLE_ENDIAN;\n    return dataView.getUint16(TIFF_ID_OFFSET, littleEndian) === TIFF_ID;\n}\n\nfunction findTiffOffsets() {\n    const TIFF_FILE_HEADER_OFFSET = 0;\n\n    if (Constants.USE_EXIF) {\n        return {\n            hasAppMarkers: true,\n            tiffHeaderOffset: TIFF_FILE_HEADER_OFFSET\n        };\n    }\n    return {};\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport {getStringFromDataView} from './utils.js';\nimport Constants from './constants.js';\n\nexport default {\n    isJpegFile,\n    findJpegOffsets\n};\n\nconst MIN_JPEG_DATA_BUFFER_LENGTH = 2;\nconst JPEG_ID = 0xffd8;\nconst JPEG_ID_SIZE = 2;\nconst APP_ID_OFFSET = 4;\nconst APP_MARKER_SIZE = 2;\nconst JFIF_DATA_OFFSET = 2; // From start of APP0 marker.\nconst TIFF_HEADER_OFFSET = 10; // From start of APP1 marker.\nconst IPTC_DATA_OFFSET = 18; // From start of APP13 marker.\nconst XMP_DATA_OFFSET = 33; // From start of APP1 marker.\nconst XMP_EXTENDED_DATA_OFFSET = 79; // From start of APP1 marker including GUID, total length, and offset.\nconst APP2_ICC_DATA_OFFSET = 18; // From start of APP2 marker including marker and chunk/chunk total numbers.\nconst MPF_DATA_OFFSET = 8;\n\nconst APP2_ICC_IDENTIFIER = 'ICC_PROFILE\\0';\nconst ICC_CHUNK_NUMBER_OFFSET = APP_ID_OFFSET + APP2_ICC_IDENTIFIER.length;\nconst ICC_TOTAL_CHUNKS_OFFSET = ICC_CHUNK_NUMBER_OFFSET + 1;\n\nconst APP2_MPF_IDENTIFIER = 'MPF\\0';\n\nconst SOF0_MARKER = 0xffc0;\nconst SOF2_MARKER = 0xffc2;\nconst DHT_MARKER = 0xffc4;\nconst DQT_MARKER = 0xffdb;\nconst DRI_MARKER = 0xffdd;\nconst SOS_MARKER = 0xffda;\n\nconst APP0_MARKER = 0xffe0;\nconst APP1_MARKER = 0xffe1;\nconst APP2_MARKER = 0xffe2;\nconst APP13_MARKER = 0xffed;\nconst APP15_MARKER = 0xffef;\nconst COMMENT_MARKER = 0xfffe;\n\n// Any number of fill bytes can be placed before an app marker. It's actually the first 0xff that is the\n// fill byte and the next 0xff is either another fill byte or the first half of the next app marker.\nconst FILL_BYTE = 0xffff;\n\nconst APP0_JFIF_IDENTIFIER = 'JFIF';\nconst APP1_EXIF_IDENTIFIER = 'Exif';\nconst APP1_XMP_IDENTIFIER = 'http://ns.adobe.com/xap/1.0/\\x00';\nconst APP1_XMP_EXTENDED_IDENTIFIER = 'http://ns.adobe.com/xmp/extension/\\x00';\nconst APP13_IPTC_IDENTIFIER = 'Photoshop 3.0';\n\nfunction isJpegFile(dataView) {\n    return !!dataView && (dataView.byteLength >= MIN_JPEG_DATA_BUFFER_LENGTH) && (dataView.getUint16(0) === JPEG_ID);\n}\n\nfunction findJpegOffsets(dataView) {\n    let appMarkerPosition = JPEG_ID_SIZE;\n    let fieldLength;\n    let sof0DataOffset;\n    let sof2DataOffset;\n    let jfifDataOffset;\n    let tiffHeaderOffset;\n    let iptcDataOffset;\n    let xmpChunks;\n    let iccChunks;\n    let mpfDataOffset;\n\n    while (appMarkerPosition + APP_ID_OFFSET + 5 <= dataView.byteLength) {\n        if (Constants.USE_FILE && isSOF0Marker(dataView, appMarkerPosition)) {\n            fieldLength = dataView.getUint16(appMarkerPosition + APP_MARKER_SIZE);\n            sof0DataOffset = appMarkerPosition + APP_MARKER_SIZE;\n        } else if (Constants.USE_FILE && isSOF2Marker(dataView, appMarkerPosition)) {\n            fieldLength = dataView.getUint16(appMarkerPosition + APP_MARKER_SIZE);\n            sof2DataOffset = appMarkerPosition + APP_MARKER_SIZE;\n        } else if (Constants.USE_JFIF && isApp0JfifMarker(dataView, appMarkerPosition)) {\n            fieldLength = dataView.getUint16(appMarkerPosition + APP_MARKER_SIZE);\n            jfifDataOffset = appMarkerPosition + JFIF_DATA_OFFSET;\n        } else if (Constants.USE_EXIF && isApp1ExifMarker(dataView, appMarkerPosition)) {\n            fieldLength = dataView.getUint16(appMarkerPosition + APP_MARKER_SIZE);\n            tiffHeaderOffset = appMarkerPosition + TIFF_HEADER_OFFSET;\n        } else if (Constants.USE_XMP && isApp1XmpMarker(dataView, appMarkerPosition)) {\n            if (!xmpChunks) {\n                xmpChunks = [];\n            }\n            fieldLength = dataView.getUint16(appMarkerPosition + APP_MARKER_SIZE);\n            xmpChunks.push(getXmpChunkDetails(appMarkerPosition, fieldLength));\n        } else if (Constants.USE_XMP && isApp1ExtendedXmpMarker(dataView, appMarkerPosition)) {\n            if (!xmpChunks) {\n                xmpChunks = [];\n            }\n            fieldLength = dataView.getUint16(appMarkerPosition + APP_MARKER_SIZE);\n            xmpChunks.push(getExtendedXmpChunkDetails(appMarkerPosition, fieldLength));\n        } else if (Constants.USE_IPTC && isApp13PhotoshopMarker(dataView, appMarkerPosition)) {\n            fieldLength = dataView.getUint16(appMarkerPosition + APP_MARKER_SIZE);\n            iptcDataOffset = appMarkerPosition + IPTC_DATA_OFFSET;\n        } else if (Constants.USE_ICC && isApp2ICCMarker(dataView, appMarkerPosition)) {\n            fieldLength = dataView.getUint16(appMarkerPosition + APP_MARKER_SIZE);\n            const iccDataOffset = appMarkerPosition + APP2_ICC_DATA_OFFSET;\n            const iccDataLength = fieldLength - (APP2_ICC_DATA_OFFSET - APP_MARKER_SIZE);\n\n            const iccChunkNumber = dataView.getUint8(appMarkerPosition + ICC_CHUNK_NUMBER_OFFSET);\n            const iccChunksTotal = dataView.getUint8(appMarkerPosition + ICC_TOTAL_CHUNKS_OFFSET);\n            if (!iccChunks) {\n                iccChunks = [];\n            }\n            iccChunks.push({offset: iccDataOffset, length: iccDataLength, chunkNumber: iccChunkNumber, chunksTotal: iccChunksTotal});\n        } else if (Constants.USE_MPF && isApp2MPFMarker(dataView, appMarkerPosition)) {\n            fieldLength = dataView.getUint16(appMarkerPosition + APP_MARKER_SIZE);\n            mpfDataOffset = appMarkerPosition + MPF_DATA_OFFSET;\n        } else if (isAppMarker(dataView, appMarkerPosition)) {\n            fieldLength = dataView.getUint16(appMarkerPosition + APP_MARKER_SIZE);\n        } else if (isFillByte(dataView, appMarkerPosition)) {\n            appMarkerPosition++;\n            continue;\n        } else {\n            break;\n        }\n        appMarkerPosition += APP_MARKER_SIZE + fieldLength;\n    }\n\n    return {\n        hasAppMarkers: appMarkerPosition > JPEG_ID_SIZE,\n        fileDataOffset: sof0DataOffset || sof2DataOffset,\n        jfifDataOffset,\n        tiffHeaderOffset,\n        iptcDataOffset,\n        xmpChunks,\n        iccChunks,\n        mpfDataOffset\n    };\n}\n\nfunction isSOF0Marker(dataView, appMarkerPosition) {\n    return (dataView.getUint16(appMarkerPosition) === SOF0_MARKER);\n}\n\nfunction isSOF2Marker(dataView, appMarkerPosition) {\n    return (dataView.getUint16(appMarkerPosition) === SOF2_MARKER);\n}\n\nfunction isApp2ICCMarker(dataView, appMarkerPosition) {\n    const markerIdLength = APP2_ICC_IDENTIFIER.length;\n\n    return (dataView.getUint16(appMarkerPosition) === APP2_MARKER)\n        && (getStringFromDataView(dataView, appMarkerPosition + APP_ID_OFFSET, markerIdLength) === APP2_ICC_IDENTIFIER);\n}\n\nfunction isApp2MPFMarker(dataView, appMarkerPosition) {\n    const markerIdLength = APP2_MPF_IDENTIFIER.length;\n\n    return (dataView.getUint16(appMarkerPosition) === APP2_MARKER)\n        && (getStringFromDataView(dataView, appMarkerPosition + APP_ID_OFFSET, markerIdLength) === APP2_MPF_IDENTIFIER);\n}\n\nfunction isApp0JfifMarker(dataView, appMarkerPosition) {\n    const markerIdLength = APP0_JFIF_IDENTIFIER.length;\n\n    return (dataView.getUint16(appMarkerPosition) === APP0_MARKER)\n        && (getStringFromDataView(dataView, appMarkerPosition + APP_ID_OFFSET, markerIdLength) === APP0_JFIF_IDENTIFIER)\n        && (dataView.getUint8(appMarkerPosition + APP_ID_OFFSET + markerIdLength) === 0x00);\n}\n\nfunction isApp1ExifMarker(dataView, appMarkerPosition) {\n    const markerIdLength = APP1_EXIF_IDENTIFIER.length;\n\n    return (dataView.getUint16(appMarkerPosition) === APP1_MARKER)\n        && (getStringFromDataView(dataView, appMarkerPosition + APP_ID_OFFSET, markerIdLength) === APP1_EXIF_IDENTIFIER)\n        && (dataView.getUint8(appMarkerPosition + APP_ID_OFFSET + markerIdLength) === 0x00);\n}\n\nfunction isApp1XmpMarker(dataView, appMarkerPosition) {\n    return (dataView.getUint16(appMarkerPosition) === APP1_MARKER)\n        && isXmpIdentifier(dataView, appMarkerPosition);\n}\n\nfunction isXmpIdentifier(dataView, appMarkerPosition) {\n    const markerIdLength = APP1_XMP_IDENTIFIER.length;\n    return getStringFromDataView(dataView, appMarkerPosition + APP_ID_OFFSET, markerIdLength) === APP1_XMP_IDENTIFIER;\n}\n\nfunction isApp1ExtendedXmpMarker(dataView, appMarkerPosition) {\n    return (dataView.getUint16(appMarkerPosition) === APP1_MARKER)\n        && isExtendedXmpIdentifier(dataView, appMarkerPosition);\n}\n\nfunction isExtendedXmpIdentifier(dataView, appMarkerPosition) {\n    const markerIdLength = APP1_XMP_EXTENDED_IDENTIFIER.length;\n    return getStringFromDataView(dataView, appMarkerPosition + APP_ID_OFFSET, markerIdLength) === APP1_XMP_EXTENDED_IDENTIFIER;\n}\n\nfunction getXmpChunkDetails(appMarkerPosition, fieldLength) {\n    return {\n        dataOffset: appMarkerPosition + XMP_DATA_OFFSET,\n        length: fieldLength - (XMP_DATA_OFFSET - APP_MARKER_SIZE)\n    };\n}\n\nfunction getExtendedXmpChunkDetails(appMarkerPosition, fieldLength) {\n    return {\n        dataOffset: appMarkerPosition + XMP_EXTENDED_DATA_OFFSET,\n        length: fieldLength - (XMP_EXTENDED_DATA_OFFSET - APP_MARKER_SIZE)\n    };\n}\n\nfunction isApp13PhotoshopMarker(dataView, appMarkerPosition) {\n    const markerIdLength = APP13_IPTC_IDENTIFIER.length;\n\n    return (dataView.getUint16(appMarkerPosition) === APP13_MARKER)\n        && (getStringFromDataView(dataView, appMarkerPosition + APP_ID_OFFSET, markerIdLength) === APP13_IPTC_IDENTIFIER)\n        && (dataView.getUint8(appMarkerPosition + APP_ID_OFFSET + markerIdLength) === 0x00);\n}\n\nfunction isAppMarker(dataView, appMarkerPosition) {\n    const appMarker = dataView.getUint16(appMarkerPosition);\n    return ((appMarker >= APP0_MARKER) && (appMarker <= APP15_MARKER))\n        || (appMarker === COMMENT_MARKER)\n        || (appMarker === SOF0_MARKER)\n        || (appMarker === SOF2_MARKER)\n        || (appMarker === DHT_MARKER)\n        || (appMarker === DQT_MARKER)\n        || (appMarker === DRI_MARKER)\n        || (appMarker === SOS_MARKER);\n}\n\nfunction isFillByte(dataView, appMarkerPosition) {\n    return dataView.getUint16(appMarkerPosition) === FILL_BYTE;\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\n// Specification: http://www.libpng.org/pub/png/spec/1.2/\n\nimport {getStringFromDataView, getNullTerminatedStringFromDataView} from './utils.js';\nimport Constants from './constants.js';\n\nexport default {\n    isPngFile,\n    findPngOffsets\n};\n\nconst PNG_ID = '\\x89\\x50\\x4e\\x47\\x0d\\x0a\\x1a\\x0a';\nconst PNG_CHUNK_LENGTH_SIZE = 4;\nexport const PNG_CHUNK_TYPE_SIZE = 4;\nexport const PNG_CHUNK_LENGTH_OFFSET = 0;\nexport const PNG_CHUNK_TYPE_OFFSET = PNG_CHUNK_LENGTH_SIZE;\nexport const PNG_CHUNK_DATA_OFFSET = PNG_CHUNK_LENGTH_SIZE + PNG_CHUNK_TYPE_SIZE;\nconst PNG_XMP_PREFIX = 'XML:com.adobe.xmp\\x00';\nexport const TYPE_TEXT = 'tEXt';\nexport const TYPE_ITXT = 'iTXt';\nexport const TYPE_ZTXT = 'zTXt';\nexport const TYPE_PHYS = 'pHYs';\nexport const TYPE_TIME = 'tIME';\nexport const TYPE_EXIF = 'eXIf';\nexport const TYPE_ICCP = 'iCCP';\n\nfunction isPngFile(dataView) {\n    return !!dataView && getStringFromDataView(dataView, 0, PNG_ID.length) === PNG_ID;\n}\n\nfunction findPngOffsets(dataView, async) {\n    const PNG_CRC_SIZE = 4;\n\n    const offsets = {\n        hasAppMarkers: false\n    };\n\n    let offset = PNG_ID.length;\n\n    while (offset + PNG_CHUNK_LENGTH_SIZE + PNG_CHUNK_TYPE_SIZE <= dataView.byteLength) {\n        if (Constants.USE_PNG_FILE && isPngImageHeaderChunk(dataView, offset)) {\n            offsets.hasAppMarkers = true;\n            offsets.pngHeaderOffset = offset + PNG_CHUNK_DATA_OFFSET;\n        } else if (Constants.USE_XMP && isPngXmpChunk(dataView, offset)) {\n            const dataOffset = getPngXmpDataOffset(dataView, offset);\n            if (dataOffset !== undefined) {\n                offsets.hasAppMarkers = true;\n                offsets.xmpChunks = [{\n                    dataOffset,\n                    length: dataView.getUint32(offset + PNG_CHUNK_LENGTH_OFFSET) - (dataOffset - (offset + PNG_CHUNK_DATA_OFFSET))\n                }];\n            }\n        } else if (isPngTextChunk(dataView, offset, async)) {\n            offsets.hasAppMarkers = true;\n            const chunkType = getStringFromDataView(dataView, offset + PNG_CHUNK_TYPE_OFFSET, PNG_CHUNK_TYPE_SIZE);\n            if (!offsets.pngTextChunks) {\n                offsets.pngTextChunks = [];\n            }\n            offsets.pngTextChunks.push({\n                length: dataView.getUint32(offset + PNG_CHUNK_LENGTH_OFFSET),\n                type: chunkType,\n                offset: offset + PNG_CHUNK_DATA_OFFSET\n            });\n        } else if (isPngExifChunk(dataView, offset)) {\n            offsets.hasAppMarkers = true;\n            offsets.tiffHeaderOffset = offset + PNG_CHUNK_DATA_OFFSET;\n        } else if (Constants.USE_ICC && async && isPngIccpChunk(dataView, offset)) {\n            offsets.hasAppMarkers = true;\n            const chunkDataLength = dataView.getUint32(offset + PNG_CHUNK_LENGTH_OFFSET);\n            const iccHeaderOffset = offset + PNG_CHUNK_DATA_OFFSET;\n            const {profileName, compressionMethod, compressedProfileOffset} = parseIccHeader(dataView, iccHeaderOffset);\n            if (!offsets.iccChunks) {\n                offsets.iccChunks = [];\n            }\n            offsets.iccChunks.push({\n                offset: compressedProfileOffset,\n                length: chunkDataLength - (compressedProfileOffset - iccHeaderOffset),\n                chunkNumber: 1,\n                chunksTotal: 1,\n                profileName,\n                compressionMethod\n            });\n        } else if (isPngChunk(dataView, offset)) {\n            offsets.hasAppMarkers = true;\n            if (!offsets.pngChunkOffsets) {\n                offsets.pngChunkOffsets = [];\n            }\n            offsets.pngChunkOffsets.push(offset + PNG_CHUNK_LENGTH_OFFSET);\n        }\n\n        offset += dataView.getUint32(offset + PNG_CHUNK_LENGTH_OFFSET)\n            + PNG_CHUNK_LENGTH_SIZE\n            + PNG_CHUNK_TYPE_SIZE\n            + PNG_CRC_SIZE;\n    }\n\n    return offsets;\n}\n\nfunction isPngImageHeaderChunk(dataView, offset) {\n    const PNG_CHUNK_TYPE_IMAGE_HEADER = 'IHDR';\n    return getStringFromDataView(dataView, offset + PNG_CHUNK_TYPE_OFFSET, PNG_CHUNK_TYPE_SIZE) === PNG_CHUNK_TYPE_IMAGE_HEADER;\n}\n\nfunction isPngXmpChunk(dataView, offset) {\n    return (getStringFromDataView(dataView, offset + PNG_CHUNK_TYPE_OFFSET, PNG_CHUNK_TYPE_SIZE) === TYPE_ITXT)\n        && (getStringFromDataView(dataView, offset + PNG_CHUNK_DATA_OFFSET, PNG_XMP_PREFIX.length) === PNG_XMP_PREFIX);\n}\n\nfunction isPngTextChunk(dataView, offset, async) {\n    const chunkType = getStringFromDataView(dataView, offset + PNG_CHUNK_TYPE_OFFSET, PNG_CHUNK_TYPE_SIZE);\n    return chunkType === TYPE_TEXT || chunkType === TYPE_ITXT || (chunkType === TYPE_ZTXT && async);\n}\n\nfunction isPngExifChunk(dataView, offset) {\n    return getStringFromDataView(dataView, offset + PNG_CHUNK_TYPE_OFFSET, PNG_CHUNK_TYPE_SIZE) === TYPE_EXIF;\n}\n\nfunction isPngIccpChunk(dataView, offset) {\n    return getStringFromDataView(dataView, offset + PNG_CHUNK_TYPE_OFFSET, PNG_CHUNK_TYPE_SIZE) === TYPE_ICCP;\n}\n\nfunction isPngChunk(dataView, offset) {\n    const SUPPORTED_PNG_CHUNK_TYPES = [TYPE_PHYS, TYPE_TIME];\n    const chunkType = getStringFromDataView(dataView, offset + PNG_CHUNK_TYPE_OFFSET, PNG_CHUNK_TYPE_SIZE);\n    return SUPPORTED_PNG_CHUNK_TYPES.includes(chunkType);\n}\n\nfunction getPngXmpDataOffset(dataView, offset) {\n    const COMPRESSION_FLAG_SIZE = 1;\n    const COMPRESSION_METHOD_SIZE = 1;\n\n    offset += PNG_CHUNK_DATA_OFFSET + PNG_XMP_PREFIX.length + COMPRESSION_FLAG_SIZE + COMPRESSION_METHOD_SIZE;\n\n    let numberOfNullSeparators = 0;\n    while (numberOfNullSeparators < 2 && offset < dataView.byteLength) {\n        if (dataView.getUint8(offset) === 0x00) {\n            numberOfNullSeparators++;\n        }\n        offset++;\n    }\n    if (numberOfNullSeparators < 2) {\n        return undefined;\n    }\n    return offset;\n}\n\nfunction parseIccHeader(dataView, offset) {\n    const NULL_SEPARATOR_SIZE = 1;\n    const COMPRESSION_METHOD_SIZE = 1;\n\n    const profileName = getNullTerminatedStringFromDataView(dataView, offset);\n    offset += profileName.length + NULL_SEPARATOR_SIZE;\n\n    const compressionMethod = dataView.getUint8(offset);\n    offset += COMPRESSION_METHOD_SIZE;\n\n    return {\n        profileName,\n        compressionMethod,\n        compressedProfileOffset: offset\n    };\n}\n", "export function get64BitValue(dataView, offset) {\n    // It's a bit tricky to handle 64 bit numbers in JavaScript. Let's\n    // wait until there are real-world examples where it is necessary.\n    return dataView.getUint32(offset + 4);\n}\n", "import {get64BitValue} from './image-header-iso-bmff-utils.js';\n\nexport function parseItemLocationBox(dataView, version, contentOffset, boxLength) {\n    const FLAGS_SIZE = 3;\n\n    const {offsets, sizes} = getItemLocationBoxOffsetsAndSizes(version, contentOffset + FLAGS_SIZE);\n\n    const offsetSize = dataView.getUint8(offsets.offsetSize) >> 4;\n    sizes.item.extent.extentOffset = offsetSize;\n    const lengthSize = dataView.getUint8(offsets.lengthSize) & 0x0f;\n    sizes.item.extent.extentLength = lengthSize;\n    const baseOffsetSize = dataView.getUint8(offsets.baseOffsetSize) >> 4;\n    sizes.item.baseOffset = baseOffsetSize;\n    const indexSize = getIndexSize(dataView, offsets.indexSize, version);\n    sizes.item.extent.extentIndex = indexSize !== undefined ? indexSize : 0;\n    const itemCount = getItemCount(dataView, offsets.itemCount, version);\n\n    return {\n        type: 'iloc',\n        items: getItems(dataView, version, offsets, sizes, offsetSize, lengthSize, indexSize, itemCount),\n        length: boxLength\n    };\n}\n\nfunction getItemLocationBoxOffsetsAndSizes(version, contentOffset) {\n    const sizes = {\n        item: {\n            dataReferenceIndex: 2,\n            extentCount: 2,\n            extent: {}\n        }\n    };\n    if (version < 2) {\n        sizes.itemCount = 2;\n        sizes.item.itemId = 2;\n    } else if (version === 2) {\n        sizes.itemCount = 4;\n        sizes.item.itemId = 4;\n    }\n    if (version === 1 || version === 2) {\n        sizes.item.constructionMethod = 2;\n    } else {\n        sizes.item.constructionMethod = 0;\n    }\n\n    const offsets = {\n        offsetSize: contentOffset,\n        lengthSize: contentOffset,\n        baseOffsetSize: contentOffset + 1,\n        indexSize: contentOffset + 1\n    };\n    offsets.itemCount = contentOffset + 2;\n    offsets.items = offsets.itemCount + sizes.itemCount;\n    offsets.item = {\n        itemId: 0\n    };\n    offsets.item.constructionMethod = offsets.item.itemId + sizes.item.itemId;\n    offsets.item.dataReferenceIndex = offsets.item.constructionMethod + sizes.item.constructionMethod;\n\n    return {offsets, sizes};\n}\n\nfunction getIndexSize(dataView, offset, version) {\n    if (version === 1 || version === 2) {\n        return dataView.getUint8(offset) & 0x0f;\n    }\n    return undefined;\n}\n\nfunction getItemCount(dataView, offset, version) {\n    if (version < 2) {\n        return dataView.getUint16(offset);\n    } else if (version === 2) {\n        return dataView.getUint32(offset);\n    }\n    return undefined;\n}\n\nfunction getItems(dataView, version, offsets, sizes, offsetSize, lengthSize, indexSize, itemCount) {\n    if (itemCount === undefined) {\n        return [];\n    }\n\n    const items = [];\n    let offset = offsets.items;\n\n    for (let i = 0; i < itemCount; i++) {\n        const item = {extents: []};\n        item.itemId = getItemId(dataView, offset, version);\n        offset += sizes.item.itemId;\n        item.constructionMethod = (version === 1) || (version === 2) ? dataView.getUint16(offset) & 0x0f : undefined;\n        offset += sizes.item.constructionMethod;\n        item.dataReferenceIndex = dataView.getUint16(offset);\n        offset += sizes.item.dataReferenceIndex;\n        item.baseOffset = getVariableSizedValue(dataView, offset, sizes.item.baseOffset);\n        offset += sizes.item.baseOffset;\n        item.extentCount = dataView.getUint16(offset);\n        offset += sizes.item.extentCount;\n        for (let j = 0; j < item.extentCount; j++) {\n            const extent = {};\n\n            extent.extentIndex = getExtentIndex(dataView, version, offset, indexSize);\n            offset += sizes.item.extent.extentIndex;\n            extent.extentOffset = getVariableSizedValue(dataView, offset, offsetSize);\n            offset += sizes.item.extent.extentOffset;\n            extent.extentLength = getVariableSizedValue(dataView, offset, lengthSize);\n            offset += sizes.item.extent.extentLength;\n\n            item.extents.push(extent);\n        }\n\n        items.push(item);\n    }\n\n    return items;\n}\n\nfunction getItemId(dataView, offset, version) {\n    if (version < 2) {\n        return dataView.getUint16(offset);\n    } else if (version === 2) {\n        return dataView.getUint32(offset);\n    }\n    return undefined;\n}\n\nfunction getExtentIndex(dataView, version, offset, indexSize) {\n    if ((version === 1 || version === 2) && indexSize > 0) {\n        return getVariableSizedValue(dataView, offset, indexSize);\n    }\n    return undefined;\n}\n\nfunction getVariableSizedValue(dataView, offset, size) {\n    if (size === 4) {\n        return dataView.getUint32(offset);\n    }\n    if (size === 8) {\n        // eslint-disable-next-line no-console\n        console.warn('This file uses an 8-bit offset which is currently not supported by ExifReader. Contact the maintainer to get it fixed.');\n        return get64BitValue(dataView, offset);\n    }\n    return 0;\n}\n", "import Constants from './constants.js';\nimport {getNullTerminatedStringFromDataView, getStringFromDataView} from './utils.js';\n// import {get64BitValue} from './image-header-iso-bmff-utils.js';\nimport {parseItemLocationBox} from './image-header-iso-bmff-iloc.js';\n\n// HEIC and AVIF files are based on the ISO-BMFF format. This file format is\n// built up by boxes. There are boxes and full boxes. All box types have a\n// length (4 or 8 bytes) and a type (4 bytes). Full boxes also have a version\n// (1 byte) and flags (3 bytes). The boxes can be nested. Each box type has its\n// own structure that can be seen in the specification.\n//\n// For metadata we are interested in the meta box. The meta box contains sub\n// boxes. The sub box type iinf has info about which types of metadata are\n// present in the file. The item ID we get from there we then look up in the\n// iloc sub box to get the offset to the real location of the metadata.\n//\n// The ICC profiles is a bit more nested. We have to look in\n// meta > iprp > ipco > colr, and then the whole profile is stored there.\n\n// These are actually 32-bit strings, not random IDs, e.g. \"ftyp\" and \"meta\".\nconst TYPE_FTYP = 0x66747970;\nconst TYPE_IPRP = 0x69707270;\nconst TYPE_META = 0x6d657461;\nconst TYPE_ILOC = 0x696c6f63;\nconst TYPE_IINF = 0x69696e66;\nconst TYPE_INFE = 0x696e6665;\nconst TYPE_IPCO = 0x6970636f;\nconst TYPE_COLR = 0x636f6c72;\n\n// const EXTENSION_TYPE_FDEL = 0x6664656c;\n\nexport const ITEM_INFO_TYPE_EXIF = 0x45786966;\nexport const ITEM_INFO_TYPE_MIME = 0x6d696d65;\nconst ITEM_INFO_TYPE_URI = 0x75726920;\n\n/**\n * Parses a ISO-BMFF box from the provided data view starting at the given offset.\n *\n * @param {DataView} dataView - The DataView to parse.\n * @param {number} offset - The offset at which to start parsing.\n * @returns {Object} The parsed box.\n */\nexport function parseBox(dataView, offset) {\n    const BOX_TYPE_OFFSET = 4;\n    const BOX_MIN_LENGTH = 8;\n    const VERSION_SIZE = 1;\n\n    const {length, contentOffset} = getBoxLength(dataView, offset);\n    if (length < BOX_MIN_LENGTH) {\n        return undefined;\n    }\n\n    const type = dataView.getUint32(offset + BOX_TYPE_OFFSET);\n\n    if (type === TYPE_FTYP) {\n        return parseFileTypeBox(dataView, contentOffset, length);\n    }\n    if (type === TYPE_IPRP) {\n        return parseItemPropertiesBox(dataView, offset, contentOffset, length);\n    }\n    if (type === TYPE_IPCO) {\n        return parseItemPropertyContainerBox(dataView, offset, contentOffset, length);\n    }\n    if (type === TYPE_COLR) {\n        return parseColorInformationBox(dataView, contentOffset, length);\n    }\n\n    // The following are full boxes, also containing version and flags.\n    const version = dataView.getUint8(contentOffset);\n\n    if (type === TYPE_META) {\n        return parseMetadataBox(dataView, offset, contentOffset + VERSION_SIZE, length);\n    }\n    if (type === TYPE_ILOC) {\n        return parseItemLocationBox(dataView, version, contentOffset + VERSION_SIZE, length);\n    }\n    if (type === TYPE_IINF) {\n        return parseItemInformationBox(dataView, offset, version, contentOffset + VERSION_SIZE, length);\n    }\n    if (type === TYPE_INFE) {\n        return parseItemInformationEntryBox(dataView, offset, version, contentOffset + VERSION_SIZE, length);\n    }\n\n    return {\n        // type: getStringFromDataView(dataView, offset + BOX_TYPE_OFFSET, 4),\n        type: undefined,\n        length\n    };\n}\n\n/**\n * @typedef {Object} BoxLength\n * @property {number} length The length of the box including length and type.\n * @property {number} contentOffset\n */\n\n/**\n * @param {DataView} dataView\n * @param {number} offset\n * @returns {BoxLength}\n */\nfunction getBoxLength(dataView, offset) {\n    const BOX_LENGTH_SIZE = 4;\n    const BOX_TYPE_SIZE = 4;\n    const BOX_EXTENDED_SIZE = 8;\n    const BOX_EXTENDED_SIZE_LOW_OFFSET = 12;\n\n    const boxLength = dataView.getUint32(offset);\n    if (extendsToEndOfFile(boxLength)) {\n        return {\n            length: dataView.byteLength - offset,\n            contentOffset: offset + BOX_LENGTH_SIZE + BOX_TYPE_SIZE,\n        };\n    }\n    if (hasExtendedSize(boxLength)) {\n        if (hasEmptyHighBits(dataView, offset)) {\n            // It's a bit tricky to handle 64 bit numbers in JavaScript. Let's\n            // wait until there are real-world examples where it is necessary.\n            return {\n                length: dataView.getUint32(offset + BOX_EXTENDED_SIZE_LOW_OFFSET),\n                contentOffset: offset + BOX_LENGTH_SIZE + BOX_TYPE_SIZE + BOX_EXTENDED_SIZE,\n            };\n        }\n    }\n\n    return {\n        length: boxLength,\n        contentOffset: offset + BOX_LENGTH_SIZE + BOX_TYPE_SIZE,\n    };\n}\n\nfunction extendsToEndOfFile(boxLength) {\n    return boxLength === 0;\n}\n\nfunction hasExtendedSize(boxLength) {\n    return boxLength === 1;\n}\n\nfunction hasEmptyHighBits(dataView, offset) {\n    const BOX_EXTENDED_SIZE_OFFSET = 8;\n    return dataView.getUint32(offset + BOX_EXTENDED_SIZE_OFFSET) === 0;\n}\n\n/**\n * @typedef {Object} Offsets\n * @property {number} tiffHeaderOffset\n * @property {Array<Object>} xmpChunks\n * @property {Array<Object>} iccChunks\n * @property {boolean} hasAppMarkers\n */\n\n/**\n * Finds the offsets of ISO-BMFF-structued data in the provided data view.\n *\n * @param {DataView} dataView - The data view to find offsets in.\n * @returns {Offsets} An object containing the offsets of the TIFF header, XMP chunks, ICC chunks, and a boolean indicating if any of these exist.\n */\nexport function findOffsets(dataView) {\n    if (Constants.USE_EXIF || Constants.USE_XMP || Constants.USE_ICC) {\n        const offsets = {};\n        const metaBox = findMetaBox(dataView);\n\n        if (!metaBox) {\n            return {hasAppMarkers: false};\n        }\n\n        if (Constants.USE_EXIF) {\n            offsets.tiffHeaderOffset = findExifOffset(dataView, metaBox);\n        }\n        if (Constants.USE_XMP) {\n            offsets.xmpChunks = findXmpChunks(metaBox);\n        }\n        if (Constants.USE_ICC) {\n            offsets.iccChunks = findIccChunks(metaBox);\n        }\n        offsets.hasAppMarkers = (offsets.tiffHeaderOffset !== undefined) || (offsets.xmpChunks !== undefined) || (offsets.iccChunks !== undefined);\n        return offsets;\n    }\n\n    return {};\n}\n\nfunction findMetaBox(dataView) {\n    const BOX_LENGTH_SIZE = 4;\n    const BOX_TYPE_SIZE = 4;\n\n    let offset = 0;\n\n    while (offset + BOX_LENGTH_SIZE + BOX_TYPE_SIZE <= dataView.byteLength) {\n        const box = parseBox(dataView, offset);\n\n        if (box === undefined) {\n            break;\n        }\n\n        if (box.type === 'meta') {\n            return box;\n        }\n\n        offset += box.length;\n    }\n\n    return undefined;\n}\n\nfunction findExifOffset(dataView, metaBox) {\n    try {\n        const exifItemId = findIinfExifItemId(metaBox).itemId;\n        const ilocItem = findIlocItem(metaBox, exifItemId);\n        const exifOffset = ilocItem.baseOffset + ilocItem.extents[0].extentOffset;\n        return getTiffHeaderOffset(dataView, exifOffset);\n    } catch (error) {\n        return undefined;\n    }\n}\n\nfunction findIinfExifItemId(metaBox) {\n    return metaBox.subBoxes.find((box) => box.type === 'iinf').itemInfos.find((itemInfo) => itemInfo.itemType === ITEM_INFO_TYPE_EXIF);\n}\n\nfunction findIlocItem(metaBox, itemId) {\n    return metaBox.subBoxes.find((box) => box.type === 'iloc').items.find((item) => item.itemId === itemId);\n}\n\nfunction getTiffHeaderOffset(dataView, exifOffset) {\n    // ISO-BMFF formatted files store the Exif data as an \"Exif block\" where the\n    // first 32 bits is the TIFF header offset.\n    const TIFF_HEADER_OFFSET_SIZE = 4;\n    return exifOffset + TIFF_HEADER_OFFSET_SIZE + dataView.getUint32(exifOffset);\n}\n\nfunction findXmpChunks(metaBox) {\n    try {\n        const xmpItemId = findIinfXmpItemId(metaBox).itemId;\n        const ilocItem = findIlocItem(metaBox, xmpItemId);\n        const ilocItemExtent = findIlocItem(metaBox, xmpItemId).extents[0];\n        return [\n            {\n                dataOffset: ilocItem.baseOffset + ilocItemExtent.extentOffset,\n                length: ilocItemExtent.extentLength,\n            }\n        ];\n    } catch (error) {\n        return undefined;\n    }\n}\n\nfunction findIinfXmpItemId(metaBox) {\n    return metaBox.subBoxes.find((box) => box.type === 'iinf')\n        .itemInfos.find((itemInfo) => itemInfo.itemType === ITEM_INFO_TYPE_MIME && itemInfo.contentType === 'application/rdf+xml');\n}\n\nfunction findIccChunks(metaBox) {\n    // This finds the first ICC chunk, but there could be one for each image\n    // that is embedded in the file. If it turns out we need to match the ICC\n    // chunk to a specific image, we need to check the \"ipma\" in addition to the\n    // \"ipco\" (currently we only extract the \"ipco\" so more code would be\n    // needed).\n    try {\n        const icc = metaBox.subBoxes.find((box) => box.type === 'iprp')\n            .subBoxes.find((box) => box.type === 'ipco')\n            .properties.find((box) => box.type === 'colr')\n            .icc;\n        if (icc) {\n            return [icc];\n        }\n    } catch (error) {\n        // Let it pass through.\n    }\n    return undefined;\n}\n\nfunction parseFileTypeBox(dataView, contentOffset, boxLength) {\n    const MAJOR_BRAND_SIZE = 4;\n    const majorBrand = getStringFromDataView(dataView, contentOffset, MAJOR_BRAND_SIZE);\n\n    return {\n        type: 'ftyp',\n        majorBrand,\n        length: boxLength\n    };\n}\n\nfunction parseItemPropertiesBox(dataView, startOffset, contentOffset, length) {\n    return {\n        type: 'iprp',\n        subBoxes: parseSubBoxes(dataView, contentOffset, length - (contentOffset - startOffset)),\n        length,\n    };\n}\n\nfunction parseItemPropertyContainerBox(dataView, startOffset, contentOffset, length) {\n    return {\n        type: 'ipco',\n        properties: parseSubBoxes(dataView, contentOffset, length - (contentOffset - startOffset)),\n        length,\n    };\n}\n\nfunction parseColorInformationBox(dataView, contentOffset, length) {\n    return {\n        type: 'colr',\n        icc: parseIcc(dataView, contentOffset),\n        length,\n    };\n}\n\nfunction parseIcc(dataView, contentOffset) {\n    const COLOR_TYPE_SIZE = 4;\n\n    const colorType = getStringFromDataView(dataView, contentOffset, COLOR_TYPE_SIZE);\n    if (colorType !== 'prof' && colorType !== 'rICC') {\n        // Support for nclx would require some restructuring for ICC handling.\n        // Probably do it as a separate feature instead of combining with ICC.\n        // Exiftool groups it under QuickTime. The test file test.avif has nclx.\n        return undefined;\n    }\n\n    return {\n        offset: contentOffset + COLOR_TYPE_SIZE,\n        length: dataView.getUint32(contentOffset + COLOR_TYPE_SIZE),\n        chunkNumber: 1,\n        chunksTotal: 1\n    };\n}\n\nfunction parseMetadataBox(dataView, startOffset, contentOffset, length) {\n    const FLAGS_SIZE = 3;\n\n    return {\n        type: 'meta',\n        subBoxes: parseSubBoxes(dataView, contentOffset + FLAGS_SIZE, length - (contentOffset + FLAGS_SIZE - startOffset)),\n        length\n    };\n}\n\n/**\n * @param {DataView} dataView\n * @param {number} offset The offset to start parsing from.\n * @param {number} length The length of all sub boxes combined.\n * @return {Array<Object>}\n */\nfunction parseSubBoxes(dataView, offset, length) {\n    const ACCEPTED_ITEM_INFO_TYPES = [\n        ITEM_INFO_TYPE_EXIF,\n        ITEM_INFO_TYPE_MIME,\n    ];\n\n    const subBoxes = [];\n    let currentOffset = offset;\n    while (currentOffset < offset + length) {\n        const box = parseBox(dataView, currentOffset);\n        if (box === undefined) {\n            break;\n        }\n        if (box.type !== undefined && (box.itemType === undefined || ACCEPTED_ITEM_INFO_TYPES.indexOf(box.itemType) !== -1)) {\n            subBoxes.push(box);\n        }\n        currentOffset += box.length;\n    }\n    return subBoxes;\n}\n\nfunction parseItemInformationBox(dataView, startOffset, version, contentOffset, length) {\n    const {offsets} = getItemInformationBoxOffsetsAndSizes(version, contentOffset);\n\n    return {\n        type: 'iinf',\n        itemInfos: parseSubBoxes(dataView, offsets.itemInfos, length - (offsets.itemInfos - startOffset)),\n        length\n    };\n}\n\nfunction getItemInformationBoxOffsetsAndSizes(version, contentOffset) {\n    const FLAGS_SIZE = 3;\n\n    const offsets = {entryCount: contentOffset + FLAGS_SIZE};\n    const sizes = {};\n\n    if (version === 0) {\n        sizes.entryCount = 2;\n    } else {\n        sizes.entryCount = 4;\n    }\n\n    offsets.itemInfos = offsets.entryCount + sizes.entryCount;\n\n    return {offsets};\n}\n\nfunction parseItemInformationEntryBox(dataView, startOffset, version, contentOffset, length) {\n    const FLAGS_SIZE = 3;\n\n    contentOffset += FLAGS_SIZE;\n    const entry = {type: 'infe', length};\n\n    if (version === 0 || version === 1) {\n        entry.itemId = dataView.getUint16(contentOffset);\n        contentOffset += 2;\n        entry.itemProtectionIndex = dataView.getUint16(contentOffset);\n        contentOffset += 2;\n        entry.itemName = getNullTerminatedStringFromDataView(dataView, contentOffset);\n        contentOffset += entry.itemName.length + 1;\n        // entry.contentType = getNullTerminatedStringFromDataView(dataView, offset);\n        // offset += entry.contentType.length + 1;\n        // Since contentEncoding is optional we need to check the offset against length here.\n        // entry.contentEncoding = getNullTerminatedStringFromDataView(dataView, offset);\n        // offset += entry.contentEncoding.length + 1;\n    }\n    // The following code should be correct but we currently don't need it.\n    // if (version === 1) {\n    //     // Everything here is optional, check the offset against length.\n    //     entry.extensionType = dataView.getUint32(contentOffset);\n    //     contentOffset += 4;\n    //     if (entry.extensionType === EXTENSION_TYPE_FDEL) {\n    //         entry.contentLocation = getNullTerminatedStringFromDataView(dataView, contentOffset);\n    //         contentOffset += entry.contentLocation.length + 1;\n    //         entry.contentMd5 = getNullTerminatedStringFromDataView(dataView, contentOffset);\n    //         contentOffset += entry.contentMd5.length + 1;\n    //         entry.contentLength = get64BitValue(dataView, contentOffset);\n    //         contentOffset += 8;\n    //         entry.transferLength = get64BitValue(dataView, contentOffset);\n    //         contentOffset += 8;\n    //         entry.entryCount = dataView.getUint8(contentOffset);\n    //         contentOffset += 1;\n    //         entry.entries = [];\n    //         for (let i = 0; i < entry.entryCount; i++) {\n    //             entry.entries.push({groupId: dataView.getUint32(contentOffset)});\n    //             contentOffset += 4;\n    //         }\n    //     }\n    // }\n    if (version >= 2) {\n        if (version === 2) {\n            entry.itemId = dataView.getUint16(contentOffset);\n            contentOffset += 2;\n        } else if (version === 3) {\n            entry.itemId = dataView.getUint32(contentOffset);\n            contentOffset += 4;\n        }\n        entry.itemProtectionIndex = dataView.getUint16(contentOffset);\n        contentOffset += 2;\n        // entry.itemTypeAscii = getStringFromDataView(dataView, offset, 4); // For testing.\n        entry.itemType = dataView.getUint32(contentOffset);\n        contentOffset += 4;\n        entry.itemName = getNullTerminatedStringFromDataView(dataView, contentOffset);\n        contentOffset += entry.itemName.length + 1;\n        if (entry.itemType === ITEM_INFO_TYPE_MIME) {\n            entry.contentType = getNullTerminatedStringFromDataView(dataView, contentOffset);\n            contentOffset += entry.contentType.length + 1;\n            if (startOffset + length > contentOffset) {\n                entry.contentEncoding = getNullTerminatedStringFromDataView(dataView, contentOffset);\n                contentOffset += entry.contentEncoding.length + 1;\n            }\n        } else if (entry.itemType === ITEM_INFO_TYPE_URI) {\n            entry.itemUri = getNullTerminatedStringFromDataView(dataView, contentOffset);\n            contentOffset += entry.itemUri.length + 1;\n        }\n    }\n    return entry;\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport {parseBox, findOffsets} from './image-header-iso-bmff.js';\n\nexport default {\n    isHeicFile,\n    findHeicOffsets\n};\n\n/**\n * Checks if the provided data view represents a HEIC/HEIF file.\n *\n * @param {DataView} dataView - The data view to check.\n * @returns {boolean} True if the data view represents a HEIC/HEIF file, false otherwise.\n */\nfunction isHeicFile(dataView) {\n    if (!dataView) {\n        return false;\n    }\n\n    const HEIC_MAJOR_BRANDS = ['heic', 'heix', 'hevc', 'hevx', 'heim', 'heis', 'hevm', 'hevs', 'mif1'];\n\n    try {\n        const headerBox = parseBox(dataView, 0);\n        return headerBox && HEIC_MAJOR_BRANDS.indexOf(headerBox.majorBrand) !== -1;\n    } catch (error) {\n        return false;\n    }\n}\n\n/**\n * Finds the offsets of a HEIC file in the provided data view.\n *\n * @param {DataView} dataView - The data view to find offsets in.\n * @returns {Object} An object containing the offsets of the TIFF header, XMP chunks, ICC chunks, and a boolean indicating if any of these exist.\n */\nfunction findHeicOffsets(dataView) {\n    return findOffsets(dataView);\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\n// Specification:\n// https://aomediacodec.github.io/av1-avif\n\nimport {parseBox, findOffsets} from './image-header-iso-bmff.js';\n\nexport default {\n    isAvifFile,\n    findAvifOffsets\n};\n\n/**\n * Checks if the provided data view represents an AVIF file.\n *\n * @param {DataView} dataView - The data view to check.\n * @returns {boolean} True if the data view represents an AVIF file, false otherwise.\n */\nfunction isAvifFile(dataView) {\n    if (!dataView) {\n        return false;\n    }\n\n    try {\n        const headerBox = parseBox(dataView, 0);\n        return headerBox && headerBox.majorBrand === 'avif';\n    } catch (error) {\n        return false;\n    }\n}\n\n/**\n * Finds the offsets of an AVIF file in the provided data view.\n *\n * @param {DataView} dataView - The data view to find offsets in.\n * @returns {Object} An object containing the offsets of the TIFF header, XMP chunks, ICC chunks, and a boolean indicating if any of these exist.\n */\nfunction findAvifOffsets(dataView) {\n    return findOffsets(dataView);\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport {getStringFromDataView} from './utils.js';\nimport Constants from './constants.js';\n\nexport default {\n    isWebpFile,\n    findOffsets\n};\n\nfunction isWebpFile(dataView) {\n    const RIFF_ID_OFFSET = 0;\n    const RIFF_ID = 'RIFF';\n    const WEBP_MARKER_OFFSET = 8;\n    const WEBP_MARKER = 'WEBP';\n\n    return !!dataView && getStringFromDataView(dataView, RIFF_ID_OFFSET, RIFF_ID.length) === RIFF_ID\n        && getStringFromDataView(dataView, WEBP_MARKER_OFFSET, WEBP_MARKER.length) === WEBP_MARKER;\n}\n\nfunction findOffsets(dataView) {\n    const SUB_CHUNK_START_OFFSET = 12;\n    const CHUNK_SIZE_OFFSET = 4;\n    const EXIF_IDENTIFIER = 'Exif\\x00\\x00';\n    const CHUNK_HEADER_SIZE = 8;\n\n    let offset = SUB_CHUNK_START_OFFSET;\n    let hasAppMarkers = false;\n    let tiffHeaderOffset;\n    let xmpChunks;\n    let iccChunks;\n    let vp8xChunkOffset;\n\n    while (offset + CHUNK_HEADER_SIZE < dataView.byteLength) {\n        const chunkId = getStringFromDataView(dataView, offset, 4);\n        const chunkSize = dataView.getUint32(offset + CHUNK_SIZE_OFFSET, true);\n\n        if (Constants.USE_EXIF && (chunkId === 'EXIF')) {\n            hasAppMarkers = true;\n            if (getStringFromDataView(dataView, offset + CHUNK_HEADER_SIZE, EXIF_IDENTIFIER.length) === EXIF_IDENTIFIER) {\n                tiffHeaderOffset = offset + CHUNK_HEADER_SIZE + EXIF_IDENTIFIER.length;\n            } else {\n                tiffHeaderOffset = offset + CHUNK_HEADER_SIZE;\n            }\n        } else if (Constants.USE_XMP && (chunkId === 'XMP ')) {\n            hasAppMarkers = true;\n            xmpChunks = [{\n                dataOffset: offset + CHUNK_HEADER_SIZE,\n                length: chunkSize\n            }];\n        } else if (Constants.USE_ICC && (chunkId === 'ICCP')) {\n            hasAppMarkers = true;\n            iccChunks = [{\n                offset: offset + CHUNK_HEADER_SIZE,\n                length: chunkSize,\n                chunkNumber: 1,\n                chunksTotal: 1\n            }];\n        } else if (chunkId === 'VP8X') {\n            hasAppMarkers = true;\n            vp8xChunkOffset = offset + CHUNK_HEADER_SIZE;\n        }\n\n        offset += CHUNK_HEADER_SIZE + (chunkSize % 2 === 0 ? chunkSize : chunkSize + 1);\n    }\n\n    return {\n        hasAppMarkers,\n        tiffHeaderOffset,\n        xmpChunks,\n        iccChunks,\n        vp8xChunkOffset\n    };\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport {getStringFromDataView} from './utils.js';\n\nexport default {\n    isGifFile,\n    findOffsets\n};\n\nconst GIF_SIGNATURE_SIZE = 6;\nconst GIF_SIGNATURES = ['GIF87a', 'GIF89a'];\n\nfunction isGifFile(dataView) {\n    return !!dataView && GIF_SIGNATURES.includes(getStringFromDataView(dataView, 0, GIF_SIGNATURE_SIZE));\n}\n\nfunction findOffsets() {\n    return {\n        gifHeaderOffset: 0\n    };\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport {getStringFromDataView} from './utils.js';\n\nexport default {\n    isXMLFile,\n    findOffsets\n};\n\nconst XML_MARKER_OFFSET = 0;\nconst XML_MARKER = '<?xpacket begin';\n\nfunction isXMLFile(dataView) {\n    return !!dataView && getStringFromDataView(dataView, XML_MARKER_OFFSET, XML_MARKER.length) === XML_MARKER;\n}\n\nfunction findOffsets(dataView) {\n    const xmpChunks = [];\n    xmpChunks.push({dataOffset: XML_MARKER_OFFSET, length: dataView.byteLength});\n    return {\n        xmpChunks,\n    };\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport Constants from './constants.js';\nimport Tiff from './image-header-tiff.js';\nimport Jpeg from './image-header-jpeg.js';\nimport Png from './image-header-png.js';\nimport Heic from './image-header-heic.js';\nimport Avif from './image-header-avif.js';\nimport Webp from './image-header-webp.js';\nimport Gif from './image-header-gif.js';\nimport Xml from './xml.js';\nimport {objectAssign} from './utils.js';\n\nexport default {\n    parseAppMarkers\n};\n\nfunction parseAppMarkers(dataView, async) {\n    if (Constants.USE_TIFF && Tiff.isTiffFile(dataView)) {\n        return addFileType(Tiff.findTiffOffsets(), 'tiff', 'TIFF');\n    }\n\n    if (Constants.USE_JPEG && Jpeg.isJpegFile(dataView)) {\n        return addFileType(Jpeg.findJpegOffsets(dataView), 'jpeg', 'JPEG');\n    }\n\n    if (Constants.USE_PNG && Png.isPngFile(dataView)) {\n        return addFileType(Png.findPngOffsets(dataView, async), 'png', 'PNG');\n    }\n\n    if (Constants.USE_HEIC && Heic.isHeicFile(dataView)) {\n        return addFileType(Heic.findHeicOffsets(dataView), 'heic', 'HEIC');\n    }\n\n    if (Constants.USE_AVIF && Avif.isAvifFile(dataView)) {\n        return addFileType(Avif.findAvifOffsets(dataView), 'avif', 'AVIF');\n    }\n\n    if (Constants.USE_WEBP && Webp.isWebpFile(dataView)) {\n        return addFileType(Webp.findOffsets(dataView), 'webp', 'WebP');\n    }\n\n    if (Constants.USE_GIF && Gif.isGifFile(dataView)) {\n        return addFileType(Gif.findOffsets(dataView), 'gif', 'GIF');\n    }\n\n    if (Constants.USE_XMP && Xml.isXMLFile(dataView)) {\n        return addFileType(Xml.findOffsets(dataView), 'xml', 'XML');\n    }\n\n    throw new Error('Invalid image format');\n}\n\nfunction addFileType(offsets, fileType, fileTypeDescription) {\n    return objectAssign({}, offsets, {fileType: {value: fileType, description: fileTypeDescription}});\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nexport default {\n    ApertureValue: (value) => Math.pow(Math.sqrt(2), value[0] / value[1]).toFixed(2),\n    ColorSpace(value) {\n        if (value === 1) {\n            return 'sRGB';\n        } else if (value === 0xffff) {\n            return 'Uncalibrated';\n        }\n        return 'Unknown';\n    },\n    ComponentsConfiguration(value) {\n        return value.map((character) => {\n            if (character === 0x31) {\n                return 'Y';\n            } else if (character === 0x32) {\n                return 'Cb';\n            } else if (character === 0x33) {\n                return 'Cr';\n            } else if (character === 0x34) {\n                return 'R';\n            } else if (character === 0x35) {\n                return 'G';\n            } else if (character === 0x36) {\n                return 'B';\n            }\n        }).join('');\n    },\n    Contrast(value) {\n        if (value === 0) {\n            return 'Normal';\n        } else if (value === 1) {\n            return 'Soft';\n        } else if (value === 2) {\n            return 'Hard';\n        }\n        return 'Unknown';\n    },\n    CustomRendered(value) {\n        if (value === 0) {\n            return 'Normal process';\n        } else if (value === 1) {\n            return 'Custom process';\n        }\n        return 'Unknown';\n    },\n    ExposureMode(value) {\n        if (value === 0) {\n            return 'Auto exposure';\n        } else if (value === 1) {\n            return 'Manual exposure';\n        } else if (value === 2) {\n            return 'Auto bracket';\n        }\n        return 'Unknown';\n    },\n    ExposureProgram(value) {\n        if (value === 0) {\n            return 'Undefined';\n        } else if (value === 1) {\n            return 'Manual';\n        } else if (value === 2) {\n            return 'Normal program';\n        } else if (value === 3) {\n            return 'Aperture priority';\n        } else if (value === 4) {\n            return 'Shutter priority';\n        } else if (value === 5) {\n            return 'Creative program';\n        } else if (value === 6) {\n            return 'Action program';\n        } else if (value === 7) {\n            return 'Portrait mode';\n        } else if (value === 8) {\n            return 'Landscape mode';\n        } else if (value === 9) {\n            return 'Bulb';\n        }\n        return 'Unknown';\n    },\n    ExposureTime(value) {\n        if (value[0] / value[1] > 0.25) {\n            const decimal = value[0] / value[1];\n            if (Number.isInteger(decimal)) {\n                return '' + decimal;\n            }\n            return decimal.toFixed(1);\n        }\n        if (value[0] !== 0) {\n            return `1/${Math.round(value[1] / value[0])}`;\n        }\n        return `0/${value[1]}`;\n    },\n    FNumber: (value) => `f/${value[0] / value[1]}`,\n    FocalLength: (value) => (value[0] / value[1]) + ' mm',\n    FocalPlaneResolutionUnit(value) {\n        if (value === 2) {\n            return 'inches';\n        } else if (value === 3) {\n            return 'centimeters';\n        }\n        return 'Unknown';\n    },\n    LightSource: (value) => {\n        if (value === 1) {\n            return 'Daylight';\n        } else if (value === 2) {\n            return 'Fluorescent';\n        } else if (value === 3) {\n            return 'Tungsten (incandescent light)';\n        } else if (value === 4) {\n            return 'Flash';\n        } else if (value === 9) {\n            return 'Fine weather';\n        } else if (value === 10) {\n            return 'Cloudy weather';\n        } else if (value === 11) {\n            return 'Shade';\n        } else if (value === 12) {\n            return 'Daylight fluorescent (D 5700 – 7100K)';\n        } else if (value === 13) {\n            return 'Day white fluorescent (N 4600 – 5400K)';\n        } else if (value === 14) {\n            return 'Cool white fluorescent (W 3900 – 4500K)';\n        } else if (value === 15) {\n            return 'White fluorescent (WW 3200 – 3700K)';\n        } else if (value === 17) {\n            return 'Standard light A';\n        } else if (value === 18) {\n            return 'Standard light B';\n        } else if (value === 19) {\n            return 'Standard light C';\n        } else if (value === 20) {\n            return 'D55';\n        } else if (value === 21) {\n            return 'D65';\n        } else if (value === 22) {\n            return 'D75';\n        } else if (value === 23) {\n            return 'D50';\n        } else if (value === 24) {\n            return 'ISO studio tungsten';\n        } else if (value === 255) {\n            return 'Other light source';\n        }\n        return 'Unknown';\n    },\n    MeteringMode(value) {\n        if (value === 1) {\n            return 'Average';\n        } else if (value === 2) {\n            return 'CenterWeightedAverage';\n        } else if (value === 3) {\n            return 'Spot';\n        } else if (value === 4) {\n            return 'MultiSpot';\n        } else if (value === 5) {\n            return 'Pattern';\n        } else if (value === 6) {\n            return 'Partial';\n        } else if (value === 255) {\n            return 'Other';\n        }\n        return 'Unknown';\n    },\n    ResolutionUnit(value) {\n        if (value === 2) {\n            return 'inches';\n        }\n        if (value === 3) {\n            return 'centimeters';\n        }\n        return 'Unknown';\n    },\n    Saturation(value) {\n        if (value === 0) {\n            return 'Normal';\n        } else if (value === 1) {\n            return 'Low saturation';\n        } else if (value === 2) {\n            return 'High saturation';\n        }\n        return 'Unknown';\n    },\n    SceneCaptureType(value) {\n        if (value === 0) {\n            return 'Standard';\n        } else if (value === 1) {\n            return 'Landscape';\n        } else if (value === 2) {\n            return 'Portrait';\n        } else if (value === 3) {\n            return 'Night scene';\n        }\n        return 'Unknown';\n    },\n    Sharpness(value) {\n        if (value === 0) {\n            return 'Normal';\n        } else if (value === 1) {\n            return 'Soft';\n        } else if (value === 2) {\n            return 'Hard';\n        }\n        return 'Unknown';\n    },\n    ShutterSpeedValue(value) {\n        const denominator = Math.pow(2, value[0] / value[1]);\n        if (denominator <= 1) {\n            return `${Math.round(1 / denominator)}`;\n        }\n        return `1/${Math.round(denominator)}`;\n    },\n    WhiteBalance(value) {\n        if (value === 0) {\n            return 'Auto white balance';\n        } else if (value === 1) {\n            return 'Manual white balance';\n        }\n        return 'Unknown';\n    },\n    XResolution: (value) => '' + Math.round(value[0] / value[1]),\n    YResolution: (value) => '' + Math.round(value[0] / value[1])\n};\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport TagNamesCommon from './tag-names-common.js';\n\nexport default {\n    0x000b: 'ProcessingSoftware',\n    0x00fe: {\n        name: 'SubfileType',\n        description: (value) => ({\n            0x0: 'Full-resolution image',\n            0x1: 'Reduced-resolution image',\n            0x2: 'Single page of multi-page image',\n            0x3: 'Single page of multi-page reduced-resolution image',\n            0x4: 'Transparency mask',\n            0x5: 'Transparency mask of reduced-resolution image',\n            0x6: 'Transparency mask of multi-page image',\n            0x7: 'Transparency mask of reduced-resolution multi-page image',\n            0x10001: 'Alternate reduced-resolution image',\n            0xffffffff: 'Invalid'\n        })[value] || 'Unknown'\n    },\n    0x00ff: {\n        name: 'OldSubfileType',\n        description: (value) => ({\n            0: 'Full-resolution image',\n            1: 'Reduced-resolution image',\n            2: 'Single page of multi-page image'\n        })[value] || 'Unknown'\n    },\n    0x0100: 'ImageWidth',\n    0x0101: 'ImageLength',\n    0x0102: 'BitsPerSample',\n    0x0103: 'Compression',\n    0x0106: 'PhotometricInterpretation',\n    0x0107: {\n        name: 'Thresholding',\n        description: (value) => ({\n            1: 'No dithering or halftoning',\n            2: 'Ordered dither or halfton',\n            3: 'Randomized dither'\n        })[value] || 'Unknown'\n    },\n    0x0108: 'CellWidth',\n    0x0109: 'CellLength',\n    0x010a: {\n        name: 'FillOrder',\n        description: (value) => ({\n            1: 'Normal',\n            2: 'Reversed'\n        })[value] || 'Unknown'\n    },\n    0x010d: 'DocumentName',\n    0x010e: 'ImageDescription',\n    0x010f: 'Make',\n    0x0110: 'Model',\n    0x0111: 'StripOffsets',\n    0x0112: {\n        name: 'Orientation',\n        description: (value) => {\n            if (value === 1) {\n                return 'top-left';\n            }\n            if (value === 2) {\n                return 'top-right';\n            }\n            if (value === 3) {\n                return 'bottom-right';\n            }\n            if (value === 4) {\n                return 'bottom-left';\n            }\n            if (value === 5) {\n                return 'left-top';\n            }\n            if (value === 6) {\n                return 'right-top';\n            }\n            if (value === 7) {\n                return 'right-bottom';\n            }\n            if (value === 8) {\n                return 'left-bottom';\n            }\n            return 'Undefined';\n        }\n    },\n    0x0115: 'SamplesPerPixel',\n    0x0116: 'RowsPerStrip',\n    0x0117: 'StripByteCounts',\n    0x0118: 'MinSampleValue',\n    0x0119: 'MaxSampleValue',\n    0x011a: {\n        'name': 'XResolution',\n        'description': TagNamesCommon.XResolution\n    },\n    0x011b: {\n        'name': 'YResolution',\n        'description': TagNamesCommon.YResolution\n    },\n    0x011c: 'PlanarConfiguration',\n    0x011d: 'PageName',\n    0x011e: {\n        'name': 'XPosition',\n        'description': (value) => {\n            return '' + Math.round(value[0] / value[1]);\n        }\n    },\n    0x011f: {\n        'name': 'YPosition',\n        'description': (value) => {\n            return '' + Math.round(value[0] / value[1]);\n        }\n    },\n    0x0122: {\n        name: 'GrayResponseUnit',\n        description: (value) => ({\n            1: '0.1',\n            2: '0.001',\n            3: '0.0001',\n            4: '1e-05',\n            5: '1e-06'\n        })[value] || 'Unknown'\n    },\n    0x0128: {\n        name: 'ResolutionUnit',\n        description: TagNamesCommon.ResolutionUnit\n    },\n    0x0129: 'PageNumber',\n    0x012d: 'TransferFunction',\n    0x0131: 'Software',\n    0x0132: 'DateTime',\n    0x013b: 'Artist',\n    0x013c: 'HostComputer',\n    0x013d: 'Predictor',\n    0x013e: {\n        'name': 'WhitePoint',\n        'description': (values) => {\n            return values.map((value) => `${value[0]}/${value[1]}`).join(', ');\n        }\n    },\n    0x013f: {\n        'name': 'PrimaryChromaticities',\n        'description': (values) => {\n            return values.map((value) => `${value[0]}/${value[1]}`).join(', ');\n        }\n    },\n    0x0141: 'HalftoneHints',\n    0x0142: 'TileWidth',\n    0x0143: 'TileLength',\n    0x014a: 'A100DataOffset',\n    0x014c: {\n        name: 'InkSet',\n        description: (value) => ({\n            1: 'CMYK',\n            2: 'Not CMYK'\n        })[value] || 'Unknown'\n    },\n    0x0151: 'TargetPrinter',\n    0x0152: {\n        name: 'ExtraSamples',\n        description: (value) => ({\n            0: 'Unspecified',\n            1: 'Associated Alpha',\n            2: 'Unassociated Alpha',\n        })[value] || 'Unknown'\n    },\n    0x0153: {\n        name: 'SampleFormat',\n        description: (value) => {\n            const formats = {\n                1: 'Unsigned',\n                2: 'Signed',\n                3: 'Float',\n                4: 'Undefined',\n                5: 'Complex int',\n                6: 'Complex float',\n            };\n            if (!Array.isArray(value)) {\n                return 'Unknown';\n            }\n            return value.map((sample) => formats[sample] || 'Unknown').join(', ');\n        }\n    },\n    0x0201: 'JPEGInterchangeFormat',\n    0x0202: 'JPEGInterchangeFormatLength',\n    0x0211: {\n        'name': 'YCbCrCoefficients',\n        'description': (values) => {\n            return values.map((value) => '' + value[0] / value[1]).join('/');\n        }\n    },\n    0x0212: 'YCbCrSubSampling',\n    0x0213: {\n        name: 'YCbCrPositioning',\n        description: (value) => {\n            if (value === 1) {\n                return 'centered';\n            }\n            if (value === 2) {\n                return 'co-sited';\n            }\n            return 'undefined ' + value;\n        }\n    },\n    0x0214: {\n        'name': 'ReferenceBlackWhite',\n        'description': (values) => {\n            return values.map((value) => '' + value[0] / value[1]).join(', ');\n        }\n    },\n    0x02bc: 'ApplicationNotes',\n    0x4746: 'Rating',\n    0x4749: 'RatingPercent',\n    0x8298: {\n        name: 'Copyright',\n        description: (value) => value.join('; ')\n    },\n    0x830e: 'PixelScale',\n    0x83bb: 'IPTC-NAA',\n    0x8480: 'IntergraphMatrix',\n    0x8482: 'ModelTiePoint',\n    0x8546: 'SEMInfo',\n    0x85d8: 'ModelTransform',\n    0x8649: 'PhotoshopSettings',\n    0x8769: 'Exif IFD Pointer',\n    0x8773: 'ICC_Profile',\n    0x87af: 'GeoTiffDirectory',\n    0x87b0: 'GeoTiffDoubleParams',\n    0x87b1: 'GeoTiffAsciiParams',\n    0x8825: 'GPS Info IFD Pointer',\n    0x9c9b: 'XPTitle',\n    0x9c9c: 'XPComment',\n    0x9c9d: 'XPAuthor',\n    0x9c9e: 'XPKeywords',\n    0x9c9f: 'XPSubject',\n    0xa480: 'GDALMetadata',\n    0xa481: 'GDALNoData',\n    0xc4a5: 'PrintIM',\n    0xc613: 'DNGBackwardVersion',\n    0xc614: 'UniqueCameraModel',\n    0xc615: 'LocalizedCameraModel',\n    0xc621: 'ColorMatrix1',\n    0xc622: 'ColorMatrix2',\n    0xc623: 'CameraCalibration1',\n    0xc624: 'CameraCalibration2',\n    0xc625: 'ReductionMatrix1',\n    0xc626: 'ReductionMatrix2',\n    0xc627: 'AnalogBalance',\n    0xc628: 'AsShotNeutral',\n    0xc629: 'AsShotWhiteXY',\n    0xc62a: 'BaselineExposure',\n    0xc62b: 'BaselineNoise',\n    0xc62c: 'BaselineSharpness',\n    0xc62e: 'LinearResponseLimit',\n    0xc62f: 'CameraSerialNumber',\n    0xc630: 'DNGLensInfo',\n    0xc633: 'ShadowScale',\n    0xc635: {\n        name: 'MakerNoteSafety',\n        description: (value) => ({\n            0: 'Unsafe',\n            1: 'Safe'\n        })[value] || 'Unknown'\n    },\n    0xc65a: {\n        name: 'CalibrationIlluminant1',\n        description: TagNamesCommon['LightSource']\n    },\n    0xc65b: {\n        name: 'CalibrationIlluminant2',\n        description: TagNamesCommon['LightSource']\n    },\n    0xc65d: 'RawDataUniqueID',\n    0xc68b: 'OriginalRawFileName',\n    0xc68c: 'OriginalRawFileData',\n    0xc68f: 'AsShotICCProfile',\n    0xc690: 'AsShotPreProfileMatrix',\n    0xc691: 'CurrentICCProfile',\n    0xc692: 'CurrentPreProfileMatrix',\n    0xc6bf: 'ColorimetricReference',\n    0xc6c5: 'SRawType',\n    0xc6d2: 'PanasonicTitle',\n    0xc6d3: 'PanasonicTitle2',\n    0xc6f3: 'CameraCalibrationSig',\n    0xc6f4: 'ProfileCalibrationSig',\n    0xc6f5: 'ProfileIFD',\n    0xc6f6: 'AsShotProfileName',\n    0xc6f8: 'ProfileName',\n    0xc6f9: 'ProfileHueSatMapDims',\n    0xc6fa: 'ProfileHueSatMapData1',\n    0xc6fb: 'ProfileHueSatMapData2',\n    0xc6fc: 'ProfileToneCurve',\n    0xc6fd: {\n        name: 'ProfileEmbedPolicy',\n        description: (value) => ({\n            0: 'Allow Copying',\n            1: 'Embed if Used',\n            2: 'Never Embed',\n            3: 'No Restrictions'\n        })[value] || 'Unknown'\n    },\n    0xc6fe: 'ProfileCopyright',\n    0xc714: 'ForwardMatrix1',\n    0xc715: 'ForwardMatrix2',\n    0xc716: 'PreviewApplicationName',\n    0xc717: 'PreviewApplicationVersion',\n    0xc718: 'PreviewSettingsName',\n    0xc719: 'PreviewSettingsDigest',\n    0xc71a: {\n        name: 'PreviewColorSpace',\n        description: (value) => ({\n            1: 'Gray Gamma 2.2',\n            2: 'sRGB',\n            3: 'Adobe RGB',\n            4: 'ProPhoto RGB'\n        })[value] || 'Unknown'\n    },\n    0xc71b: 'PreviewDateTime',\n    0xc71c: 'RawImageDigest',\n    0xc71d: 'OriginalRawFileDigest',\n    0xc725: 'ProfileLookTableDims',\n    0xc726: 'ProfileLookTableData',\n    0xc763: 'TimeCodes',\n    0xc764: 'FrameRate',\n    0xc772: 'TStop',\n    0xc789: 'ReelName',\n    0xc791: 'OriginalDefaultFinalSize',\n    0xc792: 'OriginalBestQualitySize',\n    0xc793: 'OriginalDefaultCropSize',\n    0xc7a1: 'CameraLabel',\n    0xc7a3: {\n        name: 'ProfileHueSatMapEncoding',\n        description: (value) => ({\n            0: 'Linear',\n            1: 'sRGB'\n        })[value] || 'Unknown'\n    },\n    0xc7a4: {\n        name: 'ProfileLookTableEncoding',\n        description: (value) => ({\n            0: 'Linear',\n            1: 'sRGB'\n        })[value] || 'Unknown'\n    },\n    0xc7a5: 'BaselineExposureOffset',\n    0xc7a6: {\n        name: 'DefaultBlackRender',\n        description: (value) => ({\n            0: 'Auto',\n            1: 'None'\n        })[value] || 'Unknown'\n    },\n    0xc7a7: 'NewRawImageDigest',\n    0xc7a8: 'RawToPreviewGain'\n};\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport {getStringValue, getEncodedString} from './tag-names-utils.js';\nimport TagNamesCommon from './tag-names-common.js';\n\nexport default {\n    0x829a: {\n        'name': 'ExposureTime',\n        'description': TagNamesCommon.ExposureTime\n    },\n    0x829d: {\n        'name': 'FNumber',\n        'description': TagNamesCommon.FNumber\n    },\n    0x8822: {\n        'name': 'ExposureProgram',\n        'description': TagNamesCommon.ExposureProgram\n    },\n    0x8824: 'SpectralSensitivity',\n    0x8827: 'ISOSpeedRatings',\n    0x8828: {\n        'name': 'OECF',\n        'description': () => '[Raw OECF table data]'\n    },\n    0x882a: 'TimeZoneOffset',\n    0x882b: 'SelfTimerMode',\n    0x8830: {\n        name: 'SensitivityType',\n        description: (value) => ({\n            1: 'Standard Output Sensitivity',\n            2: 'Recommended Exposure Index',\n            3: 'ISO Speed',\n            4: 'Standard Output Sensitivity and Recommended Exposure Index',\n            5: 'Standard Output Sensitivity and ISO Speed',\n            6: 'Recommended Exposure Index and ISO Speed',\n            7: 'Standard Output Sensitivity, Recommended Exposure Index and ISO Speed'\n        })[value] || 'Unknown'\n    },\n    0x8831: 'StandardOutputSensitivity',\n    0x8832: 'RecommendedExposureIndex',\n    0x8833: 'ISOSpeed',\n    0x8834: 'ISOSpeedLatitudeyyy',\n    0x8835: 'ISOSpeedLatitudezzz',\n    0x9000: {\n        'name': 'ExifVersion',\n        'description': (value) => getStringValue(value)\n    },\n    0x9003: 'DateTimeOriginal',\n    0x9004: 'DateTimeDigitized',\n    0x9009: 'GooglePlusUploadCode',\n    0x9010: 'OffsetTime',\n    0x9011: 'OffsetTimeOriginal',\n    0x9012: 'OffsetTimeDigitized',\n    0x9101: {\n        'name': 'ComponentsConfiguration',\n        'description': TagNamesCommon.ComponentsConfiguration\n    },\n    0x9102: 'CompressedBitsPerPixel',\n    0x9201: {\n        'name': 'ShutterSpeedValue',\n        'description': TagNamesCommon.ShutterSpeedValue\n    },\n    0x9202: {\n        'name': 'ApertureValue',\n        'description': TagNamesCommon.ApertureValue\n    },\n    0x9203: 'BrightnessValue',\n    0x9204: 'ExposureBiasValue',\n    0x9205: {\n        'name': 'MaxApertureValue',\n        'description': (value) => {\n            return Math.pow(Math.sqrt(2), value[0] / value[1]).toFixed(2);\n        }\n    },\n    0x9206: {\n        'name': 'SubjectDistance',\n        'description': (value) => (value[0] / value[1]) + ' m'\n    },\n    0x9207: {\n        'name': 'MeteringMode',\n        'description': TagNamesCommon.MeteringMode\n    },\n    0x9208: {\n        'name': 'LightSource',\n        description: TagNamesCommon.LightSource\n    },\n    0x9209: {\n        'name': 'Flash',\n        'description': (value) => {\n            if (value === 0x00) {\n                return 'Flash did not fire';\n            } else if (value === 0x01) {\n                return 'Flash fired';\n            } else if (value === 0x05) {\n                return 'Strobe return light not detected';\n            } else if (value === 0x07) {\n                return 'Strobe return light detected';\n            } else if (value === 0x09) {\n                return 'Flash fired, compulsory flash mode';\n            } else if (value === 0x0d) {\n                return 'Flash fired, compulsory flash mode, return light not detected';\n            } else if (value === 0x0f) {\n                return 'Flash fired, compulsory flash mode, return light detected';\n            } else if (value === 0x10) {\n                return 'Flash did not fire, compulsory flash mode';\n            } else if (value === 0x18) {\n                return 'Flash did not fire, auto mode';\n            } else if (value === 0x19) {\n                return 'Flash fired, auto mode';\n            } else if (value === 0x1d) {\n                return 'Flash fired, auto mode, return light not detected';\n            } else if (value === 0x1f) {\n                return 'Flash fired, auto mode, return light detected';\n            } else if (value === 0x20) {\n                return 'No flash function';\n            } else if (value === 0x41) {\n                return 'Flash fired, red-eye reduction mode';\n            } else if (value === 0x45) {\n                return 'Flash fired, red-eye reduction mode, return light not detected';\n            } else if (value === 0x47) {\n                return 'Flash fired, red-eye reduction mode, return light detected';\n            } else if (value === 0x49) {\n                return 'Flash fired, compulsory flash mode, red-eye reduction mode';\n            } else if (value === 0x4d) {\n                return 'Flash fired, compulsory flash mode, red-eye reduction mode, return light not detected';\n            } else if (value === 0x4f) {\n                return 'Flash fired, compulsory flash mode, red-eye reduction mode, return light detected';\n            } else if (value === 0x59) {\n                return 'Flash fired, auto mode, red-eye reduction mode';\n            } else if (value === 0x5d) {\n                return 'Flash fired, auto mode, return light not detected, red-eye reduction mode';\n            } else if (value === 0x5f) {\n                return 'Flash fired, auto mode, return light detected, red-eye reduction mode';\n            }\n            return 'Unknown';\n        }\n    },\n    0x920a: {\n        'name': 'FocalLength',\n        'description': TagNamesCommon.FocalLength\n    },\n    0x9211: 'ImageNumber',\n    0x9212: {\n        name: 'SecurityClassification',\n        description: (value) => ({\n            'C': 'Confidential',\n            'R': 'Restricted',\n            'S': 'Secret',\n            'T': 'Top Secret',\n            'U': 'Unclassified'\n        })[value] || 'Unknown'\n    },\n    0x9213: 'ImageHistory',\n    0x9214: {\n        'name': 'SubjectArea',\n        'description': (value) => {\n            if (value.length === 2) {\n                return `Location; X: ${value[0]}, Y: ${value[1]}`;\n            } else if (value.length === 3) {\n                return `Circle; X: ${value[0]}, Y: ${value[1]}, diameter: ${value[2]}`;\n            } else if (value.length === 4) {\n                return `Rectangle; X: ${value[0]}, Y: ${value[1]}, width: ${value[2]}, height: ${value[3]}`;\n            }\n            return 'Unknown';\n        }\n    },\n    0x927c: {\n        'name': 'MakerNote',\n        'description': () => '[Raw maker note data]'\n    },\n    0x9286: {\n        'name': 'UserComment',\n        'description': getEncodedString\n    },\n    0x9290: 'SubSecTime',\n    0x9291: 'SubSecTimeOriginal',\n    0x9292: 'SubSecTimeDigitized',\n    0x935c: 'ImageSourceData',\n    0x9400: {\n        'name': 'AmbientTemperature',\n        'description': (value) => (value[0] / value[1]) + ' °C'\n    },\n    0x9401: {\n        'name': 'Humidity',\n        'description': (value) => (value[0] / value[1]) + ' %'\n    },\n    0x9402: {\n        'name': 'Pressure',\n        'description': (value) => (value[0] / value[1]) + ' hPa'\n    },\n    0x9403: {\n        'name': 'WaterDepth',\n        'description': (value) => (value[0] / value[1]) + ' m'\n    },\n    0x9404: {\n        'name': 'Acceleration',\n        'description': (value) => (value[0] / value[1]) + ' mGal'\n    },\n    0x9405: {\n        'name': 'CameraElevationAngle',\n        'description': (value) => (value[0] / value[1]) + ' °'\n    },\n    0xa000: {\n        'name': 'FlashpixVersion',\n        'description': (value) => value.map((charCode) => String.fromCharCode(charCode)).join('')\n    },\n    0xa001: {\n        'name': 'ColorSpace',\n        'description': TagNamesCommon.ColorSpace\n    },\n    0xa002: 'PixelXDimension',\n    0xa003: 'PixelYDimension',\n    0xa004: 'RelatedSoundFile',\n    0xa005: 'Interoperability IFD Pointer',\n    0xa20b: 'FlashEnergy',\n    0xa20c: {\n        'name': 'SpatialFrequencyResponse',\n        'description': () => '[Raw SFR table data]'\n    },\n    0xa20e: 'FocalPlaneXResolution',\n    0xa20f: 'FocalPlaneYResolution',\n    0xa210: {\n        'name': 'FocalPlaneResolutionUnit',\n        'description': TagNamesCommon.FocalPlaneResolutionUnit\n    },\n    0xa214: {\n        'name': 'SubjectLocation',\n        'description': ([x, y]) => `X: ${x}, Y: ${y}`\n    },\n    0xa215: 'ExposureIndex',\n    0xa217: {\n        'name': 'SensingMethod',\n        'description': (value) => {\n            if (value === 1) {\n                return 'Undefined';\n            } else if (value === 2) {\n                return 'One-chip color area sensor';\n            } else if (value === 3) {\n                return 'Two-chip color area sensor';\n            } else if (value === 4) {\n                return 'Three-chip color area sensor';\n            } else if (value === 5) {\n                return 'Color sequential area sensor';\n            } else if (value === 7) {\n                return 'Trilinear sensor';\n            } else if (value === 8) {\n                return 'Color sequential linear sensor';\n            }\n            return 'Unknown';\n        }\n    },\n    0xa300: {\n        'name': 'FileSource',\n        'description': (value) => {\n            if (value === 3) {\n                return 'DSC';\n            }\n            return 'Unknown';\n        }\n    },\n    0xa301: {\n        'name': 'SceneType',\n        'description': (value) => {\n            if (value === 1) {\n                return 'A directly photographed image';\n            }\n            return 'Unknown';\n        }\n    },\n    0xa302: {\n        'name': 'CFAPattern',\n        'description': () => '[Raw CFA pattern table data]'\n    },\n    0xa401: {\n        'name': 'CustomRendered',\n        'description': TagNamesCommon.CustomRendered\n    },\n    0xa402: {\n        'name': 'ExposureMode',\n        'description': TagNamesCommon.ExposureMode\n    },\n    0xa403: {\n        'name': 'WhiteBalance',\n        'description': TagNamesCommon.WhiteBalance\n    },\n    0xa404: {\n        'name': 'DigitalZoomRatio',\n        'description': (value) => {\n            if (value[0] === 0) {\n                return 'Digital zoom was not used';\n            }\n            return '' + (value[0] / value[1]);\n        }\n    },\n    0xa405: {\n        'name': 'FocalLengthIn35mmFilm',\n        'description': (value) => {\n            if (value === 0) {\n                return 'Unknown';\n            }\n            return value;\n        }\n    },\n    0xa406: {\n        'name': 'SceneCaptureType',\n        'description': TagNamesCommon.SceneCaptureType\n    },\n    0xa407: {\n        'name': 'GainControl',\n        'description': (value) => {\n            if (value === 0) {\n                return 'None';\n            } else if (value === 1) {\n                return 'Low gain up';\n            } else if (value === 2) {\n                return 'High gain up';\n            } else if (value === 3) {\n                return 'Low gain down';\n            } else if (value === 4) {\n                return 'High gain down';\n            }\n            return 'Unknown';\n        }\n    },\n    0xa408: {\n        'name': 'Contrast',\n        'description': TagNamesCommon.Contrast\n    },\n    0xa409: {\n        'name': 'Saturation',\n        'description': TagNamesCommon.Saturation\n    },\n    0xa40a: {\n        'name': 'Sharpness',\n        'description': TagNamesCommon.Sharpness\n    },\n    0xa40b: {\n        'name': 'DeviceSettingDescription',\n        'description': () => '[Raw device settings table data]'\n    },\n    0xa40c: {\n        'name': 'SubjectDistanceRange',\n        'description': (value) => {\n            if (value === 1) {\n                return 'Macro';\n            } else if (value === 2) {\n                return 'Close view';\n            } else if (value === 3) {\n                return 'Distant view';\n            }\n            return 'Unknown';\n        }\n    },\n    0xa420: 'ImageUniqueID',\n    0xa430: 'CameraOwnerName',\n    0xa431: 'BodySerialNumber',\n    0xa432: {\n        'name': 'LensSpecification',\n        'description': (value) => {\n            const focalLengthFrom = parseFloat((value[0][0] / value[0][1]).toFixed(5));\n            const focalLengthTo = parseFloat((value[1][0] / value[1][1]).toFixed(5));\n            const focalLengths = `${focalLengthFrom}-${focalLengthTo} mm`;\n            if (value[3][1] === 0) {\n                return `${focalLengths} f/?`;\n            }\n            const maxAperture = 1 / ((value[2][1] / value[2][1]) / (value[3][0] / value[3][1]));\n            return `${focalLengths} f/${parseFloat(maxAperture.toFixed(5))}`;\n        }\n    },\n    0xa433: 'LensMake',\n    0xa434: 'LensModel',\n    0xa435: 'LensSerialNumber',\n    0xa460: {\n        name: 'CompositeImage',\n        description: (value) => ({\n            1: 'Not a Composite Image',\n            2: 'General Composite Image',\n            3: 'Composite Image Captured While Shooting',\n        })[value] || 'Unknown'\n    },\n    0xa461: 'SourceImageNumberOfCompositeImage',\n    0xa462: 'SourceExposureTimesOfCompositeImage',\n    0xa500: 'Gamma',\n    0xea1c: 'Padding',\n    0xea1d: 'OffsetSchema',\n    0xfde8: 'OwnerName',\n    0xfde9: 'SerialNumber',\n    0xfdea: 'Lens',\n    0xfe4c: 'RawFile',\n    0xfe4d: 'Converter',\n    0xfe4e: 'WhiteBalance',\n    0xfe51: 'Exposure',\n    0xfe52: 'Shadows',\n    0xfe53: 'Brightness',\n    0xfe54: 'Contrast',\n    0xfe55: 'Saturation',\n    0xfe56: 'Sharpness',\n    0xfe57: 'Smoothness',\n    0xfe58: 'MoireFilter'\n};\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport {getEncodedString, getCalculatedGpsValue} from './tag-names-utils.js';\n\nexport default {\n    0x0000: {\n        'name': 'GPSVersionID',\n        'description': (value) => {\n            if (value[0] === 2 && value[1] === 2 && value[2] === 0 && value[3] === 0) {\n                return 'Version 2.2';\n            }\n            return 'Unknown';\n        }\n    },\n    0x0001: {\n        'name': 'GPSLatitudeRef',\n        'description': (value) => {\n            const ref = value.join('');\n            if (ref === 'N') {\n                return 'North latitude';\n            } else if (ref === 'S') {\n                return 'South latitude';\n            }\n            return 'Unknown';\n        }\n    },\n    0x0002: {\n        'name': 'GPSLatitude',\n        'description': getCalculatedGpsValue\n    },\n    0x0003: {\n        'name': 'GPSLongitudeRef',\n        'description': (value) => {\n            const ref = value.join('');\n            if (ref === 'E') {\n                return 'East longitude';\n            } else if (ref === 'W') {\n                return 'West longitude';\n            }\n            return 'Unknown';\n        }\n    },\n    0x0004: {\n        'name': 'GPSLongitude',\n        'description': getCalculatedGpsValue\n    },\n    0x0005: {\n        'name': 'GPSAltitudeRef',\n        'description': (value) => {\n            if (value === 0) {\n                return 'Sea level';\n            } else if (value === 1) {\n                return 'Sea level reference (negative value)';\n            }\n            return 'Unknown';\n        }\n    },\n    0x0006: {\n        'name': 'GPSAltitude',\n        'description': (value) => {\n            return (value[0] / value[1]) + ' m';\n        }\n    },\n    0x0007: {\n        'name': 'GPSTimeStamp',\n        'description': (values) => {\n            return values.map(([numerator, denominator]) => {\n                const num = numerator / denominator;\n                if (/^\\d(\\.|$)/.test(`${num}`)) {\n                    return `0${num}`;\n                }\n                return num;\n            }).join(':');\n        }\n    },\n    0x0008: 'GPSSatellites',\n    0x0009: {\n        'name': 'GPSStatus',\n        'description': (value) => {\n            const status = value.join('');\n            if (status === 'A') {\n                return 'Measurement in progress';\n            } else if (status === 'V') {\n                return 'Measurement Interoperability';\n            }\n            return 'Unknown';\n        }\n    },\n    0x000a: {\n        'name': 'GPSMeasureMode',\n        'description': (value) => {\n            const mode = value.join('');\n            if (mode === '2') {\n                return '2-dimensional measurement';\n            } else if (mode === '3') {\n                return '3-dimensional measurement';\n            }\n            return 'Unknown';\n        }\n    },\n    0x000b: 'GPSDOP',\n    0x000c: {\n        'name': 'GPSSpeedRef',\n        'description': (value) => {\n            const ref = value.join('');\n            if (ref === 'K') {\n                return 'Kilometers per hour';\n            } else if (ref === 'M') {\n                return 'Miles per hour';\n            } else if (ref === 'N') {\n                return 'Knots';\n            }\n            return 'Unknown';\n        }\n    },\n    0x000d: 'GPSSpeed',\n    0x000e: {\n        'name': 'GPSTrackRef',\n        'description': (value) => {\n            const ref = value.join('');\n            if (ref === 'T') {\n                return 'True direction';\n            } else if (ref === 'M') {\n                return 'Magnetic direction';\n            }\n            return 'Unknown';\n        }\n    },\n    0x000f: 'GPSTrack',\n    0x0010: {\n        'name': 'GPSImgDirectionRef',\n        'description': (value) => {\n            const ref = value.join('');\n            if (ref === 'T') {\n                return 'True direction';\n            } else if (ref === 'M') {\n                return 'Magnetic direction';\n            }\n            return 'Unknown';\n        }\n    },\n    0x0011: 'GPSImgDirection',\n    0x0012: 'GPSMapDatum',\n    0x0013: {\n        'name': 'GPSDestLatitudeRef',\n        'description': (value) => {\n            const ref = value.join('');\n            if (ref === 'N') {\n                return 'North latitude';\n            } else if (ref === 'S') {\n                return 'South latitude';\n            }\n            return 'Unknown';\n        }\n    },\n    0x0014: {\n        'name': 'GPSDestLatitude',\n        'description': (value) => {\n            return (value[0][0] / value[0][1]) + (value[1][0] / value[1][1]) / 60 + (value[2][0] / value[2][1]) / 3600;\n        }\n    },\n    0x0015: {\n        'name': 'GPSDestLongitudeRef',\n        'description': (value) => {\n            const ref = value.join('');\n            if (ref === 'E') {\n                return 'East longitude';\n            } else if (ref === 'W') {\n                return 'West longitude';\n            }\n            return 'Unknown';\n        }\n    },\n    0x0016: {\n        'name': 'GPSDestLongitude',\n        'description': (value) => {\n            return (value[0][0] / value[0][1]) + (value[1][0] / value[1][1]) / 60 + (value[2][0] / value[2][1]) / 3600;\n        }\n    },\n    0x0017: {\n        'name': 'GPSDestBearingRef',\n        'description': (value) => {\n            const ref = value.join('');\n            if (ref === 'T') {\n                return 'True direction';\n            } else if (ref === 'M') {\n                return 'Magnetic direction';\n            }\n            return 'Unknown';\n        }\n    },\n    0x0018: 'GPSDestBearing',\n    0x0019: {\n        'name': 'GPSDestDistanceRef',\n        'description': (value) => {\n            const ref = value.join('');\n            if (ref === 'K') {\n                return 'Kilometers';\n            } else if (ref === 'M') {\n                return 'Miles';\n            } else if (ref === 'N') {\n                return 'Knots';\n            }\n            return 'Unknown';\n        }\n    },\n    0x001a: 'GPSDestDistance',\n    0x001b: {\n        'name': 'GPSProcessingMethod',\n        'description': getEncodedString\n    },\n    0x001c: {\n        'name': 'GPSAreaInformation',\n        'description': getEncodedString\n    },\n    0x001d: 'GPSDateStamp',\n    0x001e: {\n        'name': 'GPSDifferential',\n        'description': (value) => {\n            if (value === 0) {\n                return 'Measurement without differential correction';\n            } else if (value === 1) {\n                return 'Differential correction applied';\n            }\n            return 'Unknown';\n        }\n    },\n    0x001f: 'GPSHPositioningError'\n};\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport {getStringValue} from './tag-names-utils.js';\n\nexport default {\n    0x0001: 'InteroperabilityIndex',\n    0x0002: {\n        name: 'InteroperabilityVersion',\n        description: (value) => getStringValue(value)\n    },\n    0x1000: 'RelatedImageFileFormat',\n    0x1001: 'RelatedImageWidth',\n    0x1002: 'RelatedImageHeight'\n};\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport {getStringValue} from './tag-names-utils.js';\n\nexport default {\n    0xb000: {\n        'name': 'MPFVersion',\n        'description': (value) => getStringValue(value)\n    },\n    0xb001: 'NumberOfImages',\n    0xb002: 'MPEntry',\n    0xb003: 'ImageUIDList',\n    0xb004: 'TotalFrames'\n};\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nexport default {\n    0x0004: {\n        'name': 'ShotInfo',\n        'description': (value) => value\n    },\n};\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport {objectAssign} from './utils.js';\nimport Constants from './constants.js';\nimport TagNames0thIfd from './tag-names-0th-ifd.js';\nimport TagNamesExifIfd from './tag-names-exif-ifd.js';\nimport TagNamesGpsIfd from './tag-names-gps-ifd.js';\nimport TagNamesInteroperabilityIfd from './tag-names-interoperability-ifd.js';\nimport TagNamesMpfIfd from './tag-names-mpf-ifd.js';\nimport TagNamesCanonIfd from './tag-names-canon-ifd.js';\n\nconst tagNames0thExifIfds = objectAssign({}, TagNames0thIfd, TagNamesExifIfd);\n\nexport const IFD_TYPE_0TH = '0th';\nexport const IFD_TYPE_1ST = '1st';\nexport const IFD_TYPE_EXIF = 'exif';\nexport const IFD_TYPE_GPS = 'gps';\nexport const IFD_TYPE_INTEROPERABILITY = 'interoperability';\nexport const IFD_TYPE_MPF = 'mpf';\nexport const IFD_TYPE_CANON = 'canon';\n\nexport default {\n    [IFD_TYPE_0TH]: tagNames0thExifIfds,\n    [IFD_TYPE_1ST]: TagNames0thIfd,\n    [IFD_TYPE_EXIF]: tagNames0thExifIfds,\n    [IFD_TYPE_GPS]: TagNamesGpsIfd,\n    [IFD_TYPE_INTEROPERABILITY]: TagNamesInteroperabilityIfd,\n    [IFD_TYPE_MPF]: Constants.USE_MPF ? TagNamesMpfIfd : {},\n    [IFD_TYPE_CANON]: Constants.USE_MAKER_NOTES ? TagNamesCanonIfd : {},\n};\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport ByteOrder from './byte-order.js';\n\nconst typeSizes = {\n    1: 1, // BYTE\n    2: 1, // ASCII\n    3: 2, // SHORT\n    4: 4, // LONG\n    5: 8, // RATIONAL\n    7: 1, // UNDEFINED\n    9: 4, // SLONG\n    10: 8, // SRATIONAL\n    13: 4 // IFD\n};\n\nconst tagTypes = {\n    'BYTE': 1,\n    'ASCII': 2,\n    'SHORT': 3,\n    'LONG': 4,\n    'RATIONAL': 5,\n    'UNDEFINED': 7,\n    'SLONG': 9,\n    'SRATIONAL': 10,\n    'IFD': 13\n};\n\nexport default {\n    getAsciiValue,\n    getByteAt,\n    getAsciiAt,\n    getShortAt,\n    getLongAt,\n    getRationalAt,\n    getUndefinedAt,\n    getSlongAt,\n    getSrationalAt,\n    getIfdPointerAt,\n    typeSizes,\n    tagTypes,\n    getTypeSize\n};\n\nfunction getAsciiValue(charArray) {\n    return charArray.map((charCode) => String.fromCharCode(charCode));\n}\n\nfunction getByteAt(dataView, offset) {\n    return dataView.getUint8(offset);\n}\n\nfunction getAsciiAt(dataView, offset) {\n    return dataView.getUint8(offset);\n}\n\nfunction getShortAt(dataView, offset, byteOrder) {\n    return dataView.getUint16(offset, byteOrder === ByteOrder.LITTLE_ENDIAN);\n}\n\nfunction getLongAt(dataView, offset, byteOrder) {\n    return dataView.getUint32(offset, byteOrder === ByteOrder.LITTLE_ENDIAN);\n}\n\nfunction getRationalAt(dataView, offset, byteOrder) {\n    return [getLongAt(dataView, offset, byteOrder), getLongAt(dataView, offset + 4, byteOrder)];\n}\n\nfunction getUndefinedAt(dataView, offset) {\n    return getByteAt(dataView, offset);\n}\n\nfunction getSlongAt(dataView, offset, byteOrder) {\n    return dataView.getInt32(offset, byteOrder === ByteOrder.LITTLE_ENDIAN);\n}\n\nfunction getSrationalAt(dataView, offset, byteOrder) {\n    return [getSlongAt(dataView, offset, byteOrder), getSlongAt(dataView, offset + 4, byteOrder)];\n}\n\nfunction getIfdPointerAt(dataView, offset, byteOrder) {\n    return getLongAt(dataView, offset, byteOrder);\n}\n\nfunction getTypeSize(typeName) {\n    if (tagTypes[typeName] === undefined) {\n        throw new Error('No such type found.');\n    }\n\n    return typeSizes[tagTypes[typeName]];\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport Constants from './constants.js';\nimport Types from './types.js';\nimport TagNames, {IFD_TYPE_0TH, IFD_TYPE_1ST} from './tag-names.js';\n\nconst getTagValueAt = {\n    1: Types.getByteAt,\n    2: Types.getAsciiAt,\n    3: Types.getShortAt,\n    4: Types.getLongAt,\n    5: Types.getRationalAt,\n    7: Types.getUndefinedAt,\n    9: Types.getSlongAt,\n    10: Types.getSrationalAt,\n    13: Types.getIfdPointerAt\n};\n\nexport function get0thIfdOffset(dataView, tiffHeaderOffset, byteOrder) {\n    return tiffHeaderOffset + Types.getLongAt(dataView, tiffHeaderOffset + 4, byteOrder);\n}\n\nexport function readIfd(dataView, ifdType, tiffHeaderOffset, offset, byteOrder, includeUnknown) {\n    const FIELD_COUNT_SIZE = Types.getTypeSize('SHORT');\n    const FIELD_SIZE = 12;\n\n    const tags = {};\n    const numberOfFields = getNumberOfFields(dataView, offset, byteOrder);\n\n    offset += FIELD_COUNT_SIZE;\n    for (let fieldIndex = 0; fieldIndex < numberOfFields; fieldIndex++) {\n        if (offset + FIELD_SIZE > dataView.byteLength) {\n            break;\n        }\n\n        const tag = readTag(dataView, ifdType, tiffHeaderOffset, offset, byteOrder, includeUnknown);\n        if (tag !== undefined) {\n            tags[tag.name] = {\n                'id': tag.id,\n                'value': tag.value,\n                'description': tag.description\n            };\n            if (tag.name === 'MakerNote') {\n                tags[tag.name].__offset = tag.__offset;\n            }\n        }\n\n        offset += FIELD_SIZE;\n    }\n\n    if (Constants.USE_THUMBNAIL && (offset < dataView.byteLength - Types.getTypeSize('LONG'))) {\n        const nextIfdOffset = Types.getLongAt(dataView, offset, byteOrder);\n        if (nextIfdOffset !== 0 && ifdType === IFD_TYPE_0TH) {\n            tags['Thumbnail'] = readIfd(dataView, IFD_TYPE_1ST, tiffHeaderOffset, tiffHeaderOffset + nextIfdOffset, byteOrder, includeUnknown);\n        }\n    }\n\n    return tags;\n}\n\nfunction getNumberOfFields(dataView, offset, byteOrder) {\n    if (offset + Types.getTypeSize('SHORT') <= dataView.byteLength) {\n        return Types.getShortAt(dataView, offset, byteOrder);\n    }\n    return 0;\n}\n\nfunction readTag(dataView, ifdType, tiffHeaderOffset, offset, byteOrder, includeUnknown) {\n    const TAG_CODE_IPTC_NAA = 0x83bb;\n    const TAG_TYPE_OFFSET = Types.getTypeSize('SHORT');\n    const TAG_COUNT_OFFSET = TAG_TYPE_OFFSET + Types.getTypeSize('SHORT');\n    const TAG_VALUE_OFFSET = TAG_COUNT_OFFSET + Types.getTypeSize('LONG');\n\n    const tagCode = Types.getShortAt(dataView, offset, byteOrder);\n    const tagType = Types.getShortAt(dataView, offset + TAG_TYPE_OFFSET, byteOrder);\n    const tagCount = Types.getLongAt(dataView, offset + TAG_COUNT_OFFSET, byteOrder);\n    let tagValue;\n    let tagValueOffset;\n\n    if (Types.typeSizes[tagType] === undefined || (!includeUnknown && TagNames[ifdType][tagCode] === undefined)) {\n        return undefined;\n    }\n\n    if (tagValueFitsInOffsetSlot(tagType, tagCount)) {\n        tagValueOffset = offset + TAG_VALUE_OFFSET;\n        tagValue = getTagValue(dataView, tagValueOffset, tagType, tagCount, byteOrder);\n    } else {\n        tagValueOffset = Types.getLongAt(dataView, offset + TAG_VALUE_OFFSET, byteOrder);\n        if (tagValueFitsInDataView(dataView, tiffHeaderOffset, tagValueOffset, tagType, tagCount)) {\n            const forceByteType = tagCode === TAG_CODE_IPTC_NAA;\n            tagValue = getTagValue(dataView, tiffHeaderOffset + tagValueOffset, tagType, tagCount, byteOrder, forceByteType);\n        } else {\n            tagValue = '<faulty value>';\n        }\n    }\n\n    if (tagType === Types.tagTypes['ASCII']) {\n        tagValue = splitNullSeparatedAsciiString(tagValue);\n        tagValue = decodeAsciiValue(tagValue);\n    }\n\n    let tagName = `undefined-${tagCode}`;\n    let tagDescription = tagValue;\n\n    if (TagNames[ifdType][tagCode] !== undefined) {\n        if ((TagNames[ifdType][tagCode]['name'] !== undefined) && (TagNames[ifdType][tagCode]['description'] !== undefined)) {\n            tagName = TagNames[ifdType][tagCode]['name'];\n            try {\n                tagDescription = TagNames[ifdType][tagCode]['description'](tagValue);\n            } catch (error) {\n                tagDescription = getDescriptionFromTagValue(tagValue);\n            }\n        } else if ((tagType === Types.tagTypes['RATIONAL']) || (tagType === Types.tagTypes['SRATIONAL'])) {\n            tagName = TagNames[ifdType][tagCode];\n            tagDescription = '' + (tagValue[0] / tagValue[1]);\n        } else {\n            tagName = TagNames[ifdType][tagCode];\n            tagDescription = getDescriptionFromTagValue(tagValue);\n        }\n    }\n\n    return {\n        id: tagCode,\n        name: tagName,\n        value: tagValue,\n        description: tagDescription,\n        __offset: tagValueOffset\n    };\n}\n\nfunction tagValueFitsInOffsetSlot(tagType, tagCount) {\n    return Types.typeSizes[tagType] * tagCount <= Types.getTypeSize('LONG');\n}\n\nfunction getTagValue(dataView, offset, type, count, byteOrder, forceByteType = false) {\n    let value = [];\n\n    if (forceByteType) {\n        count = count * Types.typeSizes[type];\n        type = Types.tagTypes['BYTE'];\n    }\n    for (let valueIndex = 0; valueIndex < count; valueIndex++) {\n        value.push(getTagValueAt[type](dataView, offset, byteOrder));\n        offset += Types.typeSizes[type];\n    }\n\n    if (type === Types.tagTypes['ASCII']) {\n        value = Types.getAsciiValue(value);\n    } else if (value.length === 1) {\n        value = value[0];\n    }\n\n    return value;\n}\n\nfunction tagValueFitsInDataView(dataView, tiffHeaderOffset, tagValueOffset, tagType, tagCount) {\n    return tiffHeaderOffset + tagValueOffset + Types.typeSizes[tagType] * tagCount <= dataView.byteLength;\n}\n\nfunction splitNullSeparatedAsciiString(string) {\n    const tagValue = [];\n    let i = 0;\n\n    for (let j = 0; j < string.length; j++) {\n        if (string[j] === '\\x00') {\n            i++;\n            continue;\n        }\n        if (tagValue[i] === undefined) {\n            tagValue[i] = '';\n        }\n        tagValue[i] += string[j];\n    }\n\n    return tagValue;\n}\n\nfunction decodeAsciiValue(asciiValue) {\n    try {\n        return asciiValue.map((value) => decodeURIComponent(escape(value)));\n    } catch (error) {\n        return asciiValue;\n    }\n}\n\nfunction getDescriptionFromTagValue(tagValue) {\n    if (tagValue instanceof Array) {\n        return tagValue.join(', ');\n    }\n    return tagValue;\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport {objectAssign} from './utils.js';\nimport ByteOrder from './byte-order.js';\nimport {IFD_TYPE_0TH, IFD_TYPE_EXIF, IFD_TYPE_GPS, IFD_TYPE_INTEROPERABILITY} from './tag-names.js';\nimport {readIfd, get0thIfdOffset} from './tags-helpers.js';\n\nconst EXIF_IFD_POINTER_KEY = 'Exif IFD Pointer';\nconst GPS_INFO_IFD_POINTER_KEY = 'GPS Info IFD Pointer';\nconst INTEROPERABILITY_IFD_POINTER_KEY = 'Interoperability IFD Pointer';\n\nexport default {\n    read,\n};\n\nfunction read(dataView, tiffHeaderOffset, includeUnknown) {\n    const byteOrder = ByteOrder.getByteOrder(dataView, tiffHeaderOffset);\n    let tags = read0thIfd(dataView, tiffHeaderOffset, byteOrder, includeUnknown);\n    tags = readExifIfd(tags, dataView, tiffHeaderOffset, byteOrder, includeUnknown);\n    tags = readGpsIfd(tags, dataView, tiffHeaderOffset, byteOrder, includeUnknown);\n    tags = readInteroperabilityIfd(tags, dataView, tiffHeaderOffset, byteOrder, includeUnknown);\n\n    return {tags, byteOrder};\n}\n\nfunction read0thIfd(dataView, tiffHeaderOffset, byteOrder, includeUnknown) {\n    return readIfd(dataView, IFD_TYPE_0TH, tiffHeaderOffset, get0thIfdOffset(dataView, tiffHeaderOffset, byteOrder), byteOrder, includeUnknown);\n}\n\nfunction readExifIfd(tags, dataView, tiffHeaderOffset, byteOrder, includeUnknown) {\n    if (tags[EXIF_IFD_POINTER_KEY] !== undefined) {\n        return objectAssign(tags, readIfd(dataView, IFD_TYPE_EXIF, tiffHeaderOffset, tiffHeaderOffset + tags[EXIF_IFD_POINTER_KEY].value, byteOrder, includeUnknown));\n    }\n\n    return tags;\n}\n\nfunction readGpsIfd(tags, dataView, tiffHeaderOffset, byteOrder, includeUnknown) {\n    if (tags[GPS_INFO_IFD_POINTER_KEY] !== undefined) {\n        return objectAssign(tags, readIfd(dataView, IFD_TYPE_GPS, tiffHeaderOffset, tiffHeaderOffset + tags[GPS_INFO_IFD_POINTER_KEY].value, byteOrder, includeUnknown));\n    }\n\n    return tags;\n}\n\nfunction readInteroperabilityIfd(tags, dataView, tiffHeaderOffset, byteOrder, includeUnknown) {\n    if (tags[INTEROPERABILITY_IFD_POINTER_KEY] !== undefined) {\n        return objectAssign(tags, readIfd(dataView, IFD_TYPE_INTEROPERABILITY, tiffHeaderOffset, tiffHeaderOffset + tags[INTEROPERABILITY_IFD_POINTER_KEY].value, byteOrder, includeUnknown));\n    }\n\n    return tags;\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport ByteOrder from './byte-order.js';\nimport Types from './types.js';\nimport {IFD_TYPE_MPF} from './tag-names.js';\nimport {deferInit, getBase64Image} from './utils.js';\nimport {readIfd, get0thIfdOffset} from './tags-helpers.js';\n\nexport default {\n    read\n};\n\nconst ENTRY_SIZE = 16;\n\nfunction read(dataView, dataOffset, includeUnknown) {\n    const byteOrder = ByteOrder.getByteOrder(dataView, dataOffset);\n    const tags = readIfd(dataView, IFD_TYPE_MPF, dataOffset, get0thIfdOffset(dataView, dataOffset, byteOrder), byteOrder, includeUnknown);\n    return addMpfImages(dataView, dataOffset, tags, byteOrder);\n}\n\nfunction addMpfImages(dataView, dataOffset, tags, byteOrder) {\n    if (!tags['MPEntry']) {\n        return tags;\n    }\n\n    const images = [];\n    for (let i = 0; i < Math.ceil(tags['MPEntry'].value.length / ENTRY_SIZE); i++) {\n        images[i] = {};\n\n        const attributes = getImageNumberValue(tags['MPEntry'].value, i * ENTRY_SIZE, Types.getTypeSize('LONG'), byteOrder);\n        images[i]['ImageFlags'] = getImageFlags(attributes);\n        images[i]['ImageFormat'] = getImageFormat(attributes);\n        images[i]['ImageType'] = getImageType(attributes);\n\n        const imageSize = getImageNumberValue(tags['MPEntry'].value, i * ENTRY_SIZE + 4, Types.getTypeSize('LONG'), byteOrder);\n        images[i]['ImageSize'] = {\n            value: imageSize,\n            description: '' + imageSize\n        };\n\n        const imageOffset = getImageOffset(i, tags['MPEntry'], byteOrder, dataOffset);\n        images[i]['ImageOffset'] = {\n            value: imageOffset,\n            description: '' + imageOffset\n        };\n\n        const dependentImage1EntryNumber =\n            getImageNumberValue(tags['MPEntry'].value, i * ENTRY_SIZE + 12, Types.getTypeSize('SHORT'), byteOrder);\n        images[i]['DependentImage1EntryNumber'] = {\n            value: dependentImage1EntryNumber,\n            description: '' + dependentImage1EntryNumber\n        };\n\n        const dependentImage2EntryNumber =\n            getImageNumberValue(tags['MPEntry'].value, i * ENTRY_SIZE + 14, Types.getTypeSize('SHORT'), byteOrder);\n        images[i]['DependentImage2EntryNumber'] = {\n            value: dependentImage2EntryNumber,\n            description: '' + dependentImage2EntryNumber\n        };\n\n        images[i].image = dataView.buffer.slice(imageOffset, imageOffset + imageSize);\n        deferInit(images[i], 'base64', function () {\n            return getBase64Image(this.image);\n        });\n    }\n\n    tags['Images'] = images;\n\n    return tags;\n}\n\nfunction getImageNumberValue(entries, offset, size, byteOrder) {\n    if (byteOrder === ByteOrder.LITTLE_ENDIAN) {\n        let value = 0;\n        for (let i = 0; i < size; i++) {\n            value += entries[offset + i] << (8 * i);\n        }\n        return value;\n    }\n\n    let value = 0;\n    for (let i = 0; i < size; i++) {\n        value += entries[offset + i] << (8 * (size - 1 - i));\n    }\n    return value;\n}\n\nfunction getImageFlags(attributes) {\n    const flags = [\n        (attributes >> 31) & 0x1,\n        (attributes >> 30) & 0x1,\n        (attributes >> 29) & 0x1\n    ];\n\n    const flagsDescription = [];\n\n    if (flags[0]) {\n        flagsDescription.push('Dependent Parent Image');\n    }\n    if (flags[1]) {\n        flagsDescription.push('Dependent Child Image');\n    }\n    if (flags[2]) {\n        flagsDescription.push('Representative Image');\n    }\n\n    return {\n        value: flags,\n        description: flagsDescription.join(', ') || 'None'\n    };\n}\n\nfunction getImageFormat(attributes) {\n    const imageFormat = attributes >> 24 & 0x7;\n    return {\n        value: imageFormat,\n        description: imageFormat === 0 ? 'JPEG' : 'Unknown'\n    };\n}\n\nfunction getImageType(attributes) {\n    const type = attributes & 0xffffff;\n    const descriptions = {\n        0x30000: 'Baseline MP Primary Image',\n        0x10001: 'Large Thumbnail (VGA equivalent)',\n        0x10002: 'Large Thumbnail (Full HD equivalent)',\n        0x20001: 'Multi-Frame Image (Panorama)',\n        0x20002: 'Multi-Frame Image (Disparity)',\n        0x20003: 'Multi-Frame Image (Multi-Angle)',\n        0x0: 'Undefined',\n    };\n\n    return {\n        value: type,\n        description: descriptions[type] || 'Unknown'\n    };\n}\n\nfunction getImageOffset(imageIndex, mpEntry, byteOrder, dataOffset) {\n    if (isFirstIndividualImage(imageIndex)) {\n        return 0;\n    }\n    return getImageNumberValue(mpEntry.value, imageIndex * ENTRY_SIZE + 8, Types.getTypeSize('LONG'), byteOrder) + dataOffset;\n}\n\nfunction isFirstIndividualImage(imageIndex) {\n    return imageIndex === 0;\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport Types from './types.js';\n\nexport default {\n    read\n};\n\nfunction read(dataView, fileDataOffset) {\n    const length = getLength(dataView, fileDataOffset);\n    const numberOfColorComponents = getNumberOfColorComponents(dataView, fileDataOffset, length);\n    return {\n        'Bits Per Sample': getDataPrecision(dataView, fileDataOffset, length),\n        'Image Height': getImageHeight(dataView, fileDataOffset, length),\n        'Image Width': getImageWidth(dataView, fileDataOffset, length),\n        'Color Components': numberOfColorComponents,\n        'Subsampling': numberOfColorComponents && getSubsampling(dataView, fileDataOffset, numberOfColorComponents.value, length)\n    };\n}\n\nfunction getLength(dataView, fileDataOffset) {\n    return Types.getShortAt(dataView, fileDataOffset);\n}\n\nfunction getDataPrecision(dataView, fileDataOffset, length) {\n    const OFFSET = 2;\n    const SIZE = 1;\n\n    if (OFFSET + SIZE > length) {\n        return undefined;\n    }\n\n    const value = Types.getByteAt(dataView, fileDataOffset + OFFSET);\n    return {\n        value,\n        description: '' + value\n    };\n}\n\nfunction getImageHeight(dataView, fileDataOffset, length) {\n    const OFFSET = 3;\n    const SIZE = 2;\n\n    if (OFFSET + SIZE > length) {\n        return undefined;\n    }\n\n    const value = Types.getShortAt(dataView, fileDataOffset + OFFSET);\n    return {\n        value,\n        description: `${value}px`\n    };\n}\n\nfunction getImageWidth(dataView, fileDataOffset, length) {\n    const OFFSET = 5;\n    const SIZE = 2;\n\n    if (OFFSET + SIZE > length) {\n        return undefined;\n    }\n\n    const value = Types.getShortAt(dataView, fileDataOffset + OFFSET);\n    return {\n        value,\n        description: `${value}px`\n    };\n}\n\nfunction getNumberOfColorComponents(dataView, fileDataOffset, length) {\n    const OFFSET = 7;\n    const SIZE = 1;\n\n    if (OFFSET + SIZE > length) {\n        return undefined;\n    }\n\n    const value = Types.getByteAt(dataView, fileDataOffset + OFFSET);\n    return {\n        value,\n        description: '' + value\n    };\n}\n\nfunction getSubsampling(dataView, fileDataOffset, numberOfColorComponents, length) {\n    const OFFSET = 8;\n    const SIZE = 3 * numberOfColorComponents;\n\n    if (OFFSET + SIZE > length) {\n        return undefined;\n    }\n\n    const components = [];\n\n    for (let i = 0; i < numberOfColorComponents; i++) {\n        const componentOffset = fileDataOffset + OFFSET + i * 3;\n        components.push([\n            Types.getByteAt(dataView, componentOffset),\n            Types.getByteAt(dataView, componentOffset + 1),\n            Types.getByteAt(dataView, componentOffset + 2)\n        ]);\n    }\n\n    return {\n        value: components,\n        description: components.length > 1 ? getComponentIds(components) + getSamplingType(components) : ''\n    };\n}\n\nfunction getComponentIds(components) {\n    const ids = {\n        0x01: 'Y',\n        0x02: 'Cb',\n        0x03: 'Cr',\n        0x04: 'I',\n        0x05: 'Q',\n    };\n\n    return components.map((compontent) => ids[compontent[0]]).join('');\n}\n\nfunction getSamplingType(components) {\n    const types = {\n        0x11: '4:4:4 (1 1)',\n        0x12: '4:4:0 (1 2)',\n        0x14: '4:4:1 (1 4)',\n        0x21: '4:2:2 (2 1)',\n        0x22: '4:2:0 (2 2)',\n        0x24: '4:2:1 (2 4)',\n        0x41: '4:1:1 (4 1)',\n        0x42: '4:1:0 (4 2)'\n    };\n\n    if (components.length === 0 || components[0][1] === undefined || types[components[0][1]] === undefined) {\n        return '';\n    }\n\n    return types[components[0][1]];\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport Types from './types.js';\n\nexport default {\n    read\n};\n\nfunction read(dataView, jfifDataOffset) {\n    const length = getLength(dataView, jfifDataOffset);\n    const thumbnailWidth = getThumbnailWidth(dataView, jfifDataOffset, length);\n    const thumbnailHeight = getThumbnailHeight(dataView, jfifDataOffset, length);\n    const tags = {\n        'JFIF Version': getVersion(dataView, jfifDataOffset, length),\n        'Resolution Unit': getResolutionUnit(dataView, jfifDataOffset, length),\n        'XResolution': getXResolution(dataView, jfifDataOffset, length),\n        'YResolution': getYResolution(dataView, jfifDataOffset, length),\n        'JFIF Thumbnail Width': thumbnailWidth,\n        'JFIF Thumbnail Height': thumbnailHeight\n    };\n\n    if (thumbnailWidth !== undefined && thumbnailHeight !== undefined) {\n        const thumbnail = getThumbnail(dataView, jfifDataOffset, 3 * thumbnailWidth.value * thumbnailHeight.value, length);\n        if (thumbnail) {\n            tags['JFIF Thumbnail'] = thumbnail;\n        }\n    }\n\n    for (const tagName in tags) {\n        if (tags[tagName] === undefined) {\n            delete tags[tagName];\n        }\n    }\n\n    return tags;\n}\n\nfunction getLength(dataView, jfifDataOffset) {\n    return Types.getShortAt(dataView, jfifDataOffset);\n}\n\nfunction getVersion(dataView, jfifDataOffset, length) {\n    const OFFSET = 7;\n    const SIZE = 2;\n\n    if (OFFSET + SIZE > length) {\n        return undefined;\n    }\n\n    const majorVersion = Types.getByteAt(dataView, jfifDataOffset + OFFSET);\n    const minorVersion = Types.getByteAt(dataView, jfifDataOffset + OFFSET + 1);\n    return {\n        value: majorVersion * 0x100 + minorVersion,\n        description: majorVersion + '.' + minorVersion\n    };\n}\n\nfunction getResolutionUnit(dataView, jfifDataOffset, length) {\n    const OFFSET = 9;\n    const SIZE = 1;\n\n    if (OFFSET + SIZE > length) {\n        return undefined;\n    }\n\n    const value = Types.getByteAt(dataView, jfifDataOffset + OFFSET);\n    return {\n        value,\n        description: getResolutionUnitDescription(value)\n    };\n}\n\nfunction getResolutionUnitDescription(value) {\n    if (value === 0) {\n        return 'None';\n    }\n    if (value === 1) {\n        return 'inches';\n    }\n    if (value === 2) {\n        return 'cm';\n    }\n    return 'Unknown';\n}\n\nfunction getXResolution(dataView, jfifDataOffset, length) {\n    const OFFSET = 10;\n    const SIZE = 2;\n\n    if (OFFSET + SIZE > length) {\n        return undefined;\n    }\n\n    const value = Types.getShortAt(dataView, jfifDataOffset + OFFSET);\n    return {\n        value,\n        description: '' + value\n    };\n}\n\nfunction getYResolution(dataView, jfifDataOffset, length) {\n    const OFFSET = 12;\n    const SIZE = 2;\n\n    if (OFFSET + SIZE > length) {\n        return undefined;\n    }\n\n    const value = Types.getShortAt(dataView, jfifDataOffset + OFFSET);\n    return {\n        value,\n        description: '' + value\n    };\n}\n\nfunction getThumbnailWidth(dataView, jfifDataOffset, length) {\n    const OFFSET = 14;\n    const SIZE = 1;\n\n    if (OFFSET + SIZE > length) {\n        return undefined;\n    }\n\n    const value = Types.getByteAt(dataView, jfifDataOffset + OFFSET);\n    return {\n        value,\n        description: `${value}px`\n    };\n}\n\nfunction getThumbnailHeight(dataView, jfifDataOffset, length) {\n    const OFFSET = 15;\n    const SIZE = 1;\n\n    if (OFFSET + SIZE > length) {\n        return undefined;\n    }\n\n    const value = Types.getByteAt(dataView, jfifDataOffset + OFFSET);\n    return {\n        value,\n        description: `${value}px`\n    };\n}\n\nfunction getThumbnail(dataView, jfifDataOffset, thumbnailLength, length) {\n    const OFFSET = 16;\n\n    if (thumbnailLength === 0 || OFFSET + thumbnailLength > length) {\n        return undefined;\n    }\n\n    const value = dataView.buffer.slice(jfifDataOffset + OFFSET, jfifDataOffset + OFFSET + thumbnailLength);\n    return {\n        value,\n        description: '<24-bit RGB pixel data>'\n    };\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport {getStringValue} from './tag-names-utils.js';\n\nexport default {\n    'iptc': {\n        0x0100: {\n            'name': 'Model Version',\n            'description': (value) => {\n                return ((value[0] << 8) + value[1]).toString();\n            }\n        },\n        0x0105: {\n            'name': 'Destination',\n            'repeatable': true\n        },\n        0x0114: {\n            'name': 'File Format',\n            'description': (value) => {\n                return ((value[0] << 8) + value[1]).toString();\n            }\n        },\n        0x0116: {\n            'name': 'File Format Version',\n            'description': (value) => {\n                return ((value[0] << 8) + value[1]).toString();\n            }\n        },\n        0x011e: 'Service Identifier',\n        0x0128: 'Envelope Number',\n        0x0132: 'Product ID',\n        0x013c: 'Envelope Priority',\n        0x0146: {\n            'name': 'Date Sent',\n            'description': getCreationDate\n        },\n        0x0150: {\n            'name': 'Time Sent',\n            'description': getCreationTime\n        },\n        0x015a: {\n            'name': 'Coded Character Set',\n            'description': getEncodingName,\n            'encoding_name': getEncodingName,\n        },\n        0x0164: 'UNO',\n        0x0178: {\n            'name': 'ARM Identifier',\n            'description': (value) => {\n                return ((value[0] << 8) + value[1]).toString();\n            }\n        },\n        0x017a: {\n            'name': 'ARM Version',\n            'description': (value) => {\n                return ((value[0] << 8) + value[1]).toString();\n            }\n        },\n        0x0200: {\n            'name': 'Record Version',\n            'description': (value) => {\n                return ((value[0] << 8) + value[1]).toString();\n            }\n        },\n        0x0203: 'Object Type Reference',\n        0x0204: 'Object Attribute Reference',\n        0x0205: 'Object Name',\n        0x0207: 'Edit Status',\n        0x0208: {\n            'name': 'Editorial Update',\n            'description': (value) => {\n                if (getStringValue(value) === '01') {\n                    return 'Additional Language';\n                }\n                return 'Unknown';\n            }\n        },\n        0x020a: 'Urgency',\n        0x020c: {\n            'name': 'Subject Reference',\n            'repeatable': true,\n            'description': (value) => {\n                const parts = getStringValue(value).split(':');\n                return parts[2] + (parts[3] ? '/' + parts[3] : '') + (parts[4] ? '/' + parts[4] : '');\n            }\n        },\n        0x020f: 'Category',\n        0x0214: {\n            'name': 'Supplemental Category',\n            'repeatable': true\n        },\n        0x0216: 'Fixture Identifier',\n        0x0219: {\n            'name': 'Keywords',\n            'repeatable': true\n        },\n        0x021a: {\n            'name': 'Content Location Code',\n            'repeatable': true\n        },\n        0x021b: {\n            'name': 'Content Location Name',\n            'repeatable': true\n        },\n        0x021e: 'Release Date',\n        0x0223: 'Release Time',\n        0x0225: 'Expiration Date',\n        0x0226: 'Expiration Time',\n        0x0228: 'Special Instructions',\n        0x022a: {\n            'name': 'Action Advised',\n            'description': (value) => {\n                const string = getStringValue(value);\n                if (string === '01') {\n                    return 'Object Kill';\n                } else if (string === '02') {\n                    return 'Object Replace';\n                } else if (string === '03') {\n                    return 'Object Append';\n                } else if (string === '04') {\n                    return 'Object Reference';\n                }\n                return 'Unknown';\n            }\n        },\n        0x022d: {\n            'name': 'Reference Service',\n            'repeatable': true\n        },\n        0x022f: {\n            'name': 'Reference Date',\n            'repeatable': true\n        },\n        0x0232: {\n            'name': 'Reference Number',\n            'repeatable': true\n        },\n        0x0237: {\n            'name': 'Date Created',\n            'description': getCreationDate\n        },\n        0x023c: {\n            'name': 'Time Created',\n            'description': getCreationTime\n        },\n        0x023e: {\n            'name': 'Digital Creation Date',\n            'description': getCreationDate\n        },\n        0x023f: {\n            'name': 'Digital Creation Time',\n            'description': getCreationTime\n        },\n        0x0241: 'Originating Program',\n        0x0246: 'Program Version',\n        0x024b: {\n            'name': 'Object Cycle',\n            'description': (value) => {\n                const string = getStringValue(value);\n                if (string === 'a') {\n                    return 'morning';\n                } else if (string === 'p') {\n                    return 'evening';\n                } else if (string === 'b') {\n                    return 'both';\n                }\n                return 'Unknown';\n            }\n        },\n        0x0250: {\n            'name': 'By-line',\n            'repeatable': true\n        },\n        0x0255: {\n            'name': 'By-line Title',\n            'repeatable': true\n        },\n        0x025a: 'City',\n        0x025c: 'Sub-location',\n        0x025f: 'Province/State',\n        0x0264: 'Country/Primary Location Code',\n        0x0265: 'Country/Primary Location Name',\n        0x0267: 'Original Transmission Reference',\n        0x0269: 'Headline',\n        0x026e: 'Credit',\n        0x0273: 'Source',\n        0x0274: 'Copyright Notice',\n        0x0276: {\n            'name': 'Contact',\n            'repeatable': true\n        },\n        0x0278: 'Caption/Abstract',\n        0x027a: {\n            'name': 'Writer/Editor',\n            'repeatable': true\n        },\n        0x027d: {\n            'name': 'Rasterized Caption',\n            'description': (value) => value\n        },\n        0x0282: 'Image Type',\n        0x0283: {\n            'name': 'Image Orientation',\n            'description': (value) => {\n                const string = getStringValue(value);\n                if (string === 'P') {\n                    return 'Portrait';\n                } else if (string === 'L') {\n                    return 'Landscape';\n                } else if (string === 'S') {\n                    return 'Square';\n                }\n                return 'Unknown';\n            }\n        },\n        0x0287: 'Language Identifier',\n        0x0296: {\n            'name': 'Audio Type',\n            'description': (value) => {\n                const stringValue = getStringValue(value);\n                const character0 = stringValue.charAt(0);\n                const character1 = stringValue.charAt(1);\n                let description = '';\n\n                if (character0 === '1') {\n                    description += 'Mono';\n                } else if (character0 === '2') {\n                    description += 'Stereo';\n                }\n\n                if (character1 === 'A') {\n                    description += ', actuality';\n                } else if (character1 === 'C') {\n                    description += ', question and answer session';\n                } else if (character1 === 'M') {\n                    description += ', music, transmitted by itself';\n                } else if (character1 === 'Q') {\n                    description += ', response to a question';\n                } else if (character1 === 'R') {\n                    description += ', raw sound';\n                } else if (character1 === 'S') {\n                    description += ', scener';\n                } else if (character1 === 'V') {\n                    description += ', voicer';\n                } else if (character1 === 'W') {\n                    description += ', wrap';\n                }\n\n                if (description !== '') {\n                    return description;\n                }\n                return stringValue;\n            }\n        },\n        0x0297: {\n            'name': 'Audio Sampling Rate',\n            'description': (value) => parseInt(getStringValue(value), 10) + ' Hz'\n        },\n        0x0298: {\n            'name': 'Audio Sampling Resolution',\n            'description': (value) => {\n                const bits = parseInt(getStringValue(value), 10);\n                return bits + (bits === 1 ? ' bit' : ' bits');\n            }\n        },\n        0x0299: {\n            'name': 'Audio Duration',\n            'description': (value) => {\n                const duration = getStringValue(value);\n                if (duration.length >= 6) {\n                    return duration.substr(0, 2) + ':' + duration.substr(2, 2) + ':' + duration.substr(4, 2);\n                }\n                return duration;\n            }\n        },\n        0x029a: 'Audio Outcue',\n        0x02ba: 'Short Document ID',\n        0x02bb: 'Unique Document ID',\n        0x02bc: 'Owner ID',\n        0x02c8: {\n            'name': (value) => {\n                if (value.length === 2) {\n                    return 'ObjectData Preview File Format';\n                }\n                return 'Record 2 destination';\n            },\n            'description': (value) => {\n                if (value.length === 2) {\n                    const intValue = (value[0] << 8) + value[1];\n                    if (intValue === 0) {\n                        return 'No ObjectData';\n                    } else if (intValue === 1) {\n                        return 'IPTC-NAA Digital Newsphoto Parameter Record';\n                    } else if (intValue === 2) {\n                        return 'IPTC7901 Recommended Message Format';\n                    } else if (intValue === 3) {\n                        return 'Tagged Image File Format (Adobe/Aldus Image data)';\n                    } else if (intValue === 4) {\n                        return 'Illustrator (Adobe Graphics data)';\n                    } else if (intValue === 5) {\n                        return 'AppleSingle (Apple Computer Inc)';\n                    } else if (intValue === 6) {\n                        return 'NAA 89-3 (ANPA 1312)';\n                    } else if (intValue === 7) {\n                        return 'MacBinary II';\n                    } else if (intValue === 8) {\n                        return 'IPTC Unstructured Character Oriented File Format (UCOFF)';\n                    } else if (intValue === 9) {\n                        return 'United Press International ANPA 1312 variant';\n                    } else if (intValue === 10) {\n                        return 'United Press International Down-Load Message';\n                    } else if (intValue === 11) {\n                        return 'JPEG File Interchange (JFIF)';\n                    } else if (intValue === 12) {\n                        return 'Photo-CD Image-Pac (Eastman Kodak)';\n                    } else if (intValue === 13) {\n                        return 'Microsoft Bit Mapped Graphics File [*.BMP]';\n                    } else if (intValue === 14) {\n                        return 'Digital Audio File [*.WAV] (Microsoft & Creative Labs)';\n                    } else if (intValue === 15) {\n                        return 'Audio plus Moving Video [*.AVI] (Microsoft)';\n                    } else if (intValue === 16) {\n                        return 'PC DOS/Windows Executable Files [*.COM][*.EXE]';\n                    } else if (intValue === 17) {\n                        return 'Compressed Binary File [*.ZIP] (PKWare Inc)';\n                    } else if (intValue === 18) {\n                        return 'Audio Interchange File Format AIFF (Apple Computer Inc)';\n                    } else if (intValue === 19) {\n                        return 'RIFF Wave (Microsoft Corporation)';\n                    } else if (intValue === 20) {\n                        return 'Freehand (Macromedia/Aldus)';\n                    } else if (intValue === 21) {\n                        return 'Hypertext Markup Language \"HTML\" (The Internet Society)';\n                    } else if (intValue === 22) {\n                        return 'MPEG 2 Audio Layer 2 (Musicom), ISO/IEC';\n                    } else if (intValue === 23) {\n                        return 'MPEG 2 Audio Layer 3, ISO/IEC';\n                    } else if (intValue === 24) {\n                        return 'Portable Document File (*.PDF) Adobe';\n                    } else if (intValue === 25) {\n                        return 'News Industry Text Format (NITF)';\n                    } else if (intValue === 26) {\n                        return 'Tape Archive (*.TAR)';\n                    } else if (intValue === 27) {\n                        return 'Tidningarnas Telegrambyrå NITF version (TTNITF DTD)';\n                    } else if (intValue === 28) {\n                        return 'Ritzaus Bureau NITF version (RBNITF DTD)';\n                    } else if (intValue === 29) {\n                        return 'Corel Draw [*.CDR]';\n                    }\n                    return `Unknown format ${intValue}`;\n                }\n                return getStringValue(value);\n            }\n        },\n        0x02c9: {\n            'name': 'ObjectData Preview File Format Version',\n            'description': (value, tags) => {\n                // Format ID, Version ID, Version Description\n                const formatVersions = {\n                    '00': {'00': '1'},\n                    '01': {'01': '1', '02': '2', '03': '3', '04': '4'},\n                    '02': {'04': '4'},\n                    '03': {'01': '5.0', '02': '6.0'},\n                    '04': {'01': '1.40'},\n                    '05': {'01': '2'},\n                    '06': {'01': '1'},\n                    '11': {'01': '1.02'},\n                    '20': {'01': '3.1', '02': '4.0', '03': '5.0', '04': '5.5'},\n                    '21': {'02': '2.0'}\n                };\n                const stringValue = getStringValue(value);\n\n                if (tags['ObjectData Preview File Format']) {\n                    const objectDataPreviewFileFormat = getStringValue(tags['ObjectData Preview File Format'].value);\n                    if (formatVersions[objectDataPreviewFileFormat]\n                        && formatVersions[objectDataPreviewFileFormat][stringValue]) {\n                        return formatVersions[objectDataPreviewFileFormat][stringValue];\n                    }\n                }\n\n                return stringValue;\n            }\n        },\n        0x02ca: 'ObjectData Preview Data',\n        0x070a: {\n            'name': 'Size Mode',\n            'description': (value) => {\n                return (value[0]).toString();\n            }\n        },\n        0x0714: {\n            'name': 'Max Subfile Size',\n            'description': (value) => {\n                let n = 0;\n                for (let i = 0; i < value.length; i++) {\n                    n = (n << 8) + value[i];\n                }\n                return n.toString();\n            }\n        },\n        0x075a: {\n            'name': 'ObjectData Size Announced',\n            'description': (value) => {\n                let n = 0;\n                for (let i = 0; i < value.length; i++) {\n                    n = (n << 8) + value[i];\n                }\n                return n.toString();\n            }\n        },\n        0x075f: {\n            'name': 'Maximum ObjectData Size',\n            'description': (value) => {\n                let n = 0;\n                for (let i = 0; i < value.length; i++) {\n                    n = (n << 8) + value[i];\n                }\n                return n.toString();\n            }\n        }\n    }\n};\n\nfunction getCreationDate(value) {\n    const date = getStringValue(value);\n\n    if (date.length >= 8) {\n        return date.substr(0, 4) + '-' + date.substr(4, 2) + '-' + date.substr(6, 2);\n    }\n\n    return date;\n}\n\nfunction getCreationTime(value) {\n    const time = getStringValue(value);\n    let parsedTime = time;\n\n    if (time.length >= 6) {\n        parsedTime = time.substr(0, 2) + ':' + time.substr(2, 2) + ':' + time.substr(4, 2);\n        if (time.length === 11) {\n            parsedTime += time.substr(6, 1) + time.substr(7, 2) + ':' + time.substr(9, 2);\n        }\n    }\n\n    return parsedTime;\n}\n\nfunction getEncodingName(value) {\n    const string = getStringValue(value);\n    if (string === '\\x1b%G') {\n        return 'UTF-8';\n    } else if (string === '\\x1b%5') {\n        return 'Windows-1252';\n    } else if (string === '\\x1b%/G') {\n        return 'UTF-8 Level 1';\n    } else if (string === '\\x1b%/H') {\n        return 'UTF-8 Level 2';\n    } else if (string === '\\x1b%/I') {\n        return 'UTF-8 Level 3';\n    } else if (string === '\\x1B/A') {\n        return 'ISO-8859-1';\n    } else if (string === '\\x1B/B') {\n        return 'ISO-8859-2';\n    } else if (string === '\\x1B/C') {\n        return 'ISO-8859-3';\n    } else if (string === '\\x1B/D') {\n        return 'ISO-8859-4';\n    } else if (string === '\\x1B/@') {\n        return 'ISO-8859-5';\n    } else if (string === '\\x1B/G') {\n        return 'ISO-8859-6';\n    } else if (string === '\\x1B/F') {\n        return 'ISO-8859-7';\n    } else if (string === '\\x1B/H') {\n        return 'ISO-8859-8';\n    }\n    return 'Unknown';\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nexport default {\n    get\n};\n\nfunction get() {\n    if (typeof TextDecoder !== 'undefined') {\n        return TextDecoder;\n    }\n\n    return undefined;\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport TextDecoder from './text-decoder.js';\n\nconst TAG_HEADER_SIZE = 5;\n\nexport default {\n    decode,\n    TAG_HEADER_SIZE\n};\n\nfunction decode(encoding, tagValue) {\n    const Decoder = TextDecoder.get();\n    if ((typeof Decoder !== 'undefined') && (encoding !== undefined)) {\n        try {\n            return new Decoder(encoding).decode(tagValue instanceof DataView ? tagValue.buffer : Uint8Array.from(tagValue));\n        } catch (error) {\n            // Pass through and fall back to ASCII decoding.\n        }\n    }\n\n    const stringValue = tagValue.map((charCode) => String.fromCharCode(charCode)).join('');\n    return decodeAsciiValue(stringValue);\n}\n\nfunction decodeAsciiValue(asciiValue) {\n    try {\n        return decodeURIComponent(escape(asciiValue));\n    } catch (error) {\n        return asciiValue;\n    }\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport IptcTagNames from './iptc-tag-names.js';\nimport TagDecoder from './tag-decoder.js';\n\nconst BYTES_8BIM = 0x3842494d;\nconst BYTES_8BIM_SIZE = 4;\nconst RESOURCE_BLOCK_HEADER_SIZE = BYTES_8BIM_SIZE + 8;\nconst NAA_RESOURCE_BLOCK_TYPE = 0x0404;\nconst TAG_HEADER_SIZE = 5;\n\nexport default {\n    read\n};\n\nfunction read(dataView, dataOffset, includeUnknown) {\n    try {\n        if (Array.isArray(dataView)) {\n            return parseTags(new DataView(Uint8Array.from(dataView).buffer), {size: dataView.length}, 0, includeUnknown);\n        }\n        const {naaBlock, dataOffset: newDataOffset} = getNaaResourceBlock(dataView, dataOffset);\n        return parseTags(dataView, naaBlock, newDataOffset, includeUnknown);\n    } catch (error) {\n        return {};\n    }\n}\n\nfunction getNaaResourceBlock(dataView, dataOffset) {\n    while (dataOffset + RESOURCE_BLOCK_HEADER_SIZE <= dataView.byteLength) {\n        const resourceBlock = getResourceBlock(dataView, dataOffset);\n        if (isNaaResourceBlock(resourceBlock)) {\n            return {naaBlock: resourceBlock, dataOffset: dataOffset + RESOURCE_BLOCK_HEADER_SIZE};\n        }\n        dataOffset += RESOURCE_BLOCK_HEADER_SIZE + resourceBlock.size + getBlockPadding(resourceBlock);\n    }\n    throw new Error('No IPTC NAA resource block.');\n}\n\nfunction getResourceBlock(dataView, dataOffset) {\n    const RESOURCE_BLOCK_SIZE_OFFSET = 10;\n\n    if (dataView.getUint32(dataOffset, false) !== BYTES_8BIM) {\n        throw new Error('Not an IPTC resource block.');\n    }\n\n    return {\n        type: dataView.getUint16(dataOffset + BYTES_8BIM_SIZE),\n        size: dataView.getUint16(dataOffset + RESOURCE_BLOCK_SIZE_OFFSET)\n    };\n}\n\nfunction isNaaResourceBlock(resourceBlock) {\n    return resourceBlock.type === NAA_RESOURCE_BLOCK_TYPE;\n}\n\nfunction getBlockPadding(resourceBlock) {\n    if (resourceBlock.size % 2 !== 0) {\n        return 1;\n    }\n    return 0;\n}\n\nfunction parseTags(dataView, naaBlock, dataOffset, includeUnknown) {\n    const tags = {};\n    let encoding = undefined;\n\n    const endOfBlockOffset = dataOffset + naaBlock['size'];\n\n    while ((dataOffset < endOfBlockOffset) && (dataOffset < dataView.byteLength)) {\n        const {tag, tagSize} = readTag(dataView, dataOffset, tags, encoding, includeUnknown);\n\n        if (tag === null) {\n            break;\n        }\n\n        if (tag) {\n            if ('encoding' in tag) {\n                encoding = tag.encoding;\n            }\n\n            if ((tags[tag.name] === undefined) || (tag['repeatable'] === undefined)) {\n                tags[tag.name] = {\n                    id: tag.id,\n                    value: tag.value,\n                    description: tag.description\n                };\n            } else {\n                if (!(tags[tag.name] instanceof Array)) {\n                    tags[tag.name] = [{\n                        id: tags[tag.name].id,\n                        value: tags[tag.name].value,\n                        description: tags[tag.name].description\n                    }];\n                }\n                tags[tag.name].push({\n                    id: tag.id,\n                    value: tag.value,\n                    description: tag.description\n                });\n            }\n        }\n\n        dataOffset += TAG_HEADER_SIZE + tagSize;\n    }\n\n    return tags;\n}\n\nfunction readTag(dataView, dataOffset, tags, encoding, includeUnknown) {\n    const TAG_CODE_OFFSET = 1;\n    const TAG_SIZE_OFFSET = 3;\n\n    if (leadByteIsMissing(dataView, dataOffset)) {\n        return {tag: null, tagSize: 0};\n    }\n\n    const tagCode = dataView.getUint16(dataOffset + TAG_CODE_OFFSET);\n    const tagSize = dataView.getUint16(dataOffset + TAG_SIZE_OFFSET);\n\n    if (!includeUnknown && !IptcTagNames['iptc'][tagCode]) {\n        return {tag: undefined, tagSize};\n    }\n\n    const tagValue = getTagValue(dataView, dataOffset + TAG_HEADER_SIZE, tagSize);\n\n    const tag = {\n        id: tagCode,\n        name: getTagName(IptcTagNames['iptc'][tagCode], tagCode, tagValue),\n        value: tagValue,\n        description: getTagDescription(IptcTagNames['iptc'][tagCode], tagValue, tags, encoding)\n    };\n    if (tagIsRepeatable(tagCode)) {\n        tag['repeatable'] = true;\n    }\n    if (tagContainsEncoding(tagCode)) {\n        tag['encoding'] = IptcTagNames['iptc'][tagCode]['encoding_name'](tagValue);\n    }\n\n    return {tag, tagSize};\n}\n\nfunction leadByteIsMissing(dataView, dataOffset) {\n    const TAG_LEAD_BYTE = 0x1c;\n    return dataView.getUint8(dataOffset) !== TAG_LEAD_BYTE;\n}\n\nfunction getTagValue(dataView, offset, size) {\n    const value = [];\n\n    for (let valueIndex = 0; valueIndex < size; valueIndex++) {\n        value.push(dataView.getUint8(offset + valueIndex));\n    }\n\n    return value;\n}\n\nfunction getTagName(tag, tagCode, tagValue) {\n    if (!tag) {\n        return `undefined-${tagCode}`;\n    }\n    if (tagIsName(tag)) {\n        return tag;\n    }\n    if (hasDynamicName(tag)) {\n        return tag['name'](tagValue);\n    }\n    return tag['name'];\n}\n\nfunction tagIsName(tag) {\n    return typeof tag === 'string';\n}\n\nfunction hasDynamicName(tag) {\n    return typeof (tag['name']) === 'function';\n}\n\nfunction getTagDescription(tag, tagValue, tags, encoding) {\n    if (hasDescriptionProperty(tag)) {\n        try {\n            return tag['description'](tagValue, tags);\n        } catch (error) {\n            // Fall through to next handler.\n        }\n    }\n    if (tagValueIsText(tag, tagValue)) {\n        return TagDecoder.decode(encoding, tagValue);\n    }\n    return tagValue;\n}\n\nfunction tagValueIsText(tag, tagValue) {\n    return tag && tagValue instanceof Array;\n}\n\nfunction hasDescriptionProperty(tag) {\n    return tag && tag['description'] !== undefined;\n}\n\nfunction tagIsRepeatable(tagCode) {\n    return IptcTagNames['iptc'][tagCode] && IptcTagNames['iptc'][tagCode]['repeatable'];\n}\n\nfunction tagContainsEncoding(tagCode) {\n    return IptcTagNames['iptc'][tagCode] && IptcTagNames['iptc'][tagCode]['encoding_name'] !== undefined;\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport TagNamesCommon from './tag-names-common.js';\n\nexport default {\n    'tiff:Orientation'(value) {\n        if (value === '1') {\n            return 'Horizontal (normal)';\n        }\n        if (value === '2') {\n            return 'Mirror horizontal';\n        }\n        if (value === '3') {\n            return 'Rotate 180';\n        }\n        if (value === '4') {\n            return 'Mirror vertical';\n        }\n        if (value === '5') {\n            return 'Mirror horizontal and rotate 270 CW';\n        }\n        if (value === '6') {\n            return 'Rotate 90 CW';\n        }\n        if (value === '7') {\n            return 'Mirror horizontal and rotate 90 CW';\n        }\n        if (value === '8') {\n            return 'Rotate 270 CW';\n        }\n        return value;\n    },\n    'tiff:ResolutionUnit': (value) => TagNamesCommon.ResolutionUnit(parseInt(value, 10)),\n    'tiff:XResolution': (value) => fraction(TagNamesCommon.XResolution, value),\n    'tiff:YResolution': (value) => fraction(TagNamesCommon.YResolution, value),\n    'exif:ApertureValue': (value) => fraction(TagNamesCommon.ApertureValue, value),\n    'exif:GPSLatitude': calculateGPSValue,\n    'exif:GPSLongitude': calculateGPSValue,\n    'exif:FNumber': (value) => fraction(TagNamesCommon.FNumber, value),\n    'exif:FocalLength': (value) => fraction(TagNamesCommon.FocalLength, value),\n    'exif:FocalPlaneResolutionUnit': (value) => TagNamesCommon.FocalPlaneResolutionUnit(parseInt(value, 10)),\n    'exif:ColorSpace': (value) => TagNamesCommon.ColorSpace(parseNumber(value)),\n    'exif:ComponentsConfiguration'(value, description) {\n        if (/^\\d, \\d, \\d, \\d$/.test(description)) {\n            const numbers = description.split(', ').map((number) => number.charCodeAt(0));\n            return TagNamesCommon.ComponentsConfiguration(numbers);\n        }\n        return description;\n    },\n    'exif:Contrast': (value) => TagNamesCommon.Contrast(parseInt(value, 10)),\n    'exif:CustomRendered': (value) => TagNamesCommon.CustomRendered(parseInt(value, 10)),\n    'exif:ExposureMode': (value) => TagNamesCommon.ExposureMode(parseInt(value, 10)),\n    'exif:ExposureProgram': (value) => TagNamesCommon.ExposureProgram(parseInt(value, 10)),\n    'exif:ExposureTime'(value) {\n        if (isFraction(value)) {\n            return TagNamesCommon.ExposureTime(value.split('/').map((number) => parseInt(number, 10)));\n        }\n        return value;\n    },\n    'exif:MeteringMode': (value) => TagNamesCommon.MeteringMode(parseInt(value, 10)),\n    'exif:Saturation': (value) => TagNamesCommon.Saturation(parseInt(value, 10)),\n    'exif:SceneCaptureType': (value) => TagNamesCommon.SceneCaptureType(parseInt(value, 10)),\n    'exif:Sharpness': (value) => TagNamesCommon.Sharpness(parseInt(value, 10)),\n    'exif:ShutterSpeedValue': (value) => fraction(TagNamesCommon.ShutterSpeedValue, value),\n    'exif:WhiteBalance': (value) => TagNamesCommon.WhiteBalance(parseInt(value, 10))\n};\n\nfunction fraction(func, value) {\n    if (isFraction(value)) {\n        return func(value.split('/'));\n    }\n    return value;\n}\n\nfunction parseNumber(value) {\n    if (value.substring(0, 2) === '0x') {\n        return parseInt(value.substring(2), 16);\n    }\n    return parseInt(value, 10);\n}\n\nfunction isFraction(value) {\n    return /^-?\\d+\\/-?\\d+$/.test(value);\n}\n\nfunction calculateGPSValue(value) {\n    const [degreesString, minutesString] = value.split(',');\n    if ((degreesString !== undefined) && (minutesString !== undefined)) {\n        const degrees = parseFloat(degreesString);\n        const minutes = parseFloat(minutesString);\n        const ref = minutesString.charAt(minutesString.length - 1);\n        if ((!Number.isNaN(degrees)) && (!Number.isNaN(minutes))) {\n            return '' + (degrees + minutes / 60) + ref;\n        }\n    }\n    return value;\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nexport default {\n    get\n};\n\nfunction get() {\n    if (typeof DOMParser !== 'undefined') {\n        return new DOMParser();\n    }\n    try {\n        // eslint-disable-next-line no-undef\n        const {DOMParser, onErrorStopParsing} = __non_webpack_require__('@xmldom/xmldom');\n        return new DOMParser({onError: onErrorStopParsing});\n    } catch (error) {\n        return undefined;\n    }\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport {getStringFromDataView, objectAssign} from './utils.js';\nimport XmpTagNames from './xmp-tag-names.js';\nimport DOMParser from './dom-parser.js';\n\nexport default {\n    read\n};\n\nfunction read(dataView, chunks) {\n    const tags = {};\n\n    if (typeof dataView === 'string') {\n        readTags(tags, dataView);\n        return tags;\n    }\n\n    const [standardXmp, extendedXmp] = extractCompleteChunks(dataView, chunks);\n\n    const hasStandardTags = readTags(tags, standardXmp);\n\n    if (extendedXmp) {\n        const hasExtendedTags = readTags(tags, extendedXmp);\n\n        if (!hasStandardTags && !hasExtendedTags) {\n            // Some writers are not spec-compliant in that they split an XMP\n            // metadata tree over both the standard XMP block and the extended\n            // XMP block. If we failed parsing both of the XMPs in the regular\n            // way, we try to combine them to see if that works better.\n            delete tags._raw;\n            readTags(tags, combineChunks(dataView, chunks));\n        }\n    }\n\n    return tags;\n}\n\n// The first chunk is always the regular XMP document. Then there is something\n// called extended XMP. The extended XMP is also a single XMP document but it\n// can be divided into multiple chunks that need to be combined into one.\nfunction extractCompleteChunks(dataView, chunks) {\n    if (chunks.length === 0) {\n        return [];\n    }\n\n    const completeChunks = [combineChunks(dataView, chunks.slice(0, 1))];\n    if (chunks.length > 1) {\n        completeChunks.push(combineChunks(dataView, chunks.slice(1)));\n    }\n\n    return completeChunks;\n}\n\nfunction combineChunks(dataView, chunks) {\n    const totalLength = chunks.reduce((size, chunk) => size + chunk.length, 0);\n    const combinedChunks = new Uint8Array(totalLength);\n    let offset = 0;\n\n    for (let i = 0; i < chunks.length; i++) {\n        const chunk = chunks[i];\n        const slice = dataView.buffer.slice(chunk.dataOffset, chunk.dataOffset + chunk.length);\n        combinedChunks.set(new Uint8Array(slice), offset);\n        offset += chunk.length;\n    }\n\n    return new DataView(combinedChunks.buffer);\n}\n\nfunction readTags(tags, chunkDataView) {\n    try {\n        const {doc, raw} = getDocument(chunkDataView);\n        tags._raw = (tags._raw || '') + raw;\n        const rdf = getRDF(doc);\n\n        objectAssign(tags, parseXMPObject(convertToObject(rdf, true)));\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\n\nfunction getDocument(chunkDataView) {\n    const domParser = DOMParser.get();\n    if (!domParser) {\n        console.warn('Warning: DOMParser is not available. It is needed to be able to parse XMP tags.'); // eslint-disable-line no-console\n        throw new Error();\n    }\n\n    const xmlString = typeof chunkDataView === 'string' ? chunkDataView : getStringFromDataView(chunkDataView, 0, chunkDataView.byteLength);\n    const doc = domParser.parseFromString(trimXmlSource(xmlString), 'application/xml');\n\n    if (doc.documentElement.nodeName === 'parsererror') {\n        throw new Error(doc.documentElement.textContent);\n    }\n\n    return {\n        doc,\n        raw: xmlString,\n    };\n}\n\nfunction trimXmlSource(xmlSource) {\n    return xmlSource.replace(/^.+(<\\?xpacket begin)/, '$1').replace(/(<\\?xpacket end=\".*\"\\?>).+$/, '$1');\n}\n\nfunction getRDF(node) {\n    for (let i = 0; i < node.childNodes.length; i++) {\n        if (node.childNodes[i].tagName === 'x:xmpmeta') {\n            return getRDF(node.childNodes[i]);\n        }\n        if (node.childNodes[i].tagName === 'rdf:RDF') {\n            return node.childNodes[i];\n        }\n    }\n\n    throw new Error();\n}\n\nfunction convertToObject(node, isTopNode = false) {\n    const childNodes = getChildNodes(node);\n\n    if (hasTextOnlyContent(childNodes)) {\n        if (isTopNode) {\n            return {};\n        }\n        return getTextValue(childNodes[0]);\n    }\n\n    return getElementsFromNodes(childNodes);\n}\n\nfunction getChildNodes(node) {\n    const elements = [];\n\n    for (let i = 0; i < node.childNodes.length; i++) {\n        elements.push(node.childNodes[i]);\n    }\n\n    return elements;\n}\n\nfunction hasTextOnlyContent(nodes) {\n    return (nodes.length === 1) && (nodes[0].nodeName === '#text');\n}\n\nfunction getTextValue(node) {\n    return node.nodeValue;\n}\n\nfunction getElementsFromNodes(nodes) {\n    const elements = {};\n\n    nodes.forEach((node) => {\n        if (isElement(node)) {\n            const nodeElement = getElementFromNode(node);\n\n            if (elements[node.nodeName] !== undefined) {\n                if (!Array.isArray(elements[node.nodeName])) {\n                    elements[node.nodeName] = [elements[node.nodeName]];\n                }\n                elements[node.nodeName].push(nodeElement);\n            } else {\n                elements[node.nodeName] = nodeElement;\n            }\n        }\n    });\n\n    return elements;\n}\n\nfunction isElement(node) {\n    return (node.nodeName) && (node.nodeName !== '#text');\n}\n\nfunction getElementFromNode(node) {\n    return {\n        attributes: getAttributes(node),\n        value: convertToObject(node)\n    };\n}\n\nfunction getAttributes(element) {\n    const attributes = {};\n\n    for (let i = 0; i < element.attributes.length; i++) {\n        attributes[element.attributes[i].nodeName] = decodeURIComponent(escape(element.attributes[i].value));\n    }\n\n    return attributes;\n}\n\nfunction parseXMPObject(xmpObject) {\n    const tags = {};\n\n    if (typeof xmpObject === 'string') {\n        return xmpObject;\n    }\n\n    for (const nodeName in xmpObject) {\n        let nodes = xmpObject[nodeName];\n\n        if (!Array.isArray(nodes)) {\n            nodes = [nodes];\n        }\n\n        nodes.forEach((node) => {\n            objectAssign(tags, parseNodeAttributesAsTags(node.attributes));\n            if (typeof node.value === 'object') {\n                objectAssign(tags, parseNodeChildrenAsTags(node.value));\n            }\n        });\n    }\n\n    return tags;\n}\n\nfunction parseNodeAttributesAsTags(attributes) {\n    const tags = {};\n\n    for (const name in attributes) {\n        try {\n            if (isTagAttribute(name)) {\n                tags[getLocalName(name)] = {\n                    value: attributes[name],\n                    attributes: {},\n                    description: getDescription(attributes[name], name)\n                };\n            }\n        } catch (error) {\n            // Keep going and try to parse the rest of the tags.\n        }\n    }\n\n    return tags;\n}\n\nfunction isTagAttribute(name) {\n    return (name !== 'rdf:parseType') && (!isNamespaceDefinition(name));\n}\n\nfunction isNamespaceDefinition(name) {\n    return name.split(':')[0] === 'xmlns';\n}\n\nfunction getLocalName(name) {\n    if (/^MicrosoftPhoto(_\\d+_)?:Rating$/i.test(name)) {\n        return 'RatingPercent';\n    }\n    return name.split(':')[1];\n}\n\nfunction getDescription(value, name = undefined) {\n    if (Array.isArray(value)) {\n        const arrayDescription = getDescriptionOfArray(value);\n        if ((name) && (typeof XmpTagNames[name] === 'function')) {\n            return XmpTagNames[name](value, arrayDescription);\n        }\n        return arrayDescription;\n    }\n    if (typeof value === 'object') {\n        return getDescriptionOfObject(value);\n    }\n\n    try {\n        if ((name) && (typeof XmpTagNames[name] === 'function')) {\n            return XmpTagNames[name](value);\n        }\n        return decodeURIComponent(escape(value));\n    } catch (error) {\n        return value;\n    }\n}\n\nfunction getDescriptionOfArray(value) {\n    return value.map((item) => {\n        if (item.value !== undefined) {\n            return getDescription(item.value);\n        }\n        return getDescription(item);\n    }).join(', ');\n}\n\nfunction getDescriptionOfObject(value) {\n    const descriptions = [];\n\n    for (const key in value) {\n        descriptions.push(`${getClearTextKey(key)}: ${getDescription(value[key].value)}`);\n    }\n\n    return descriptions.join('; ');\n}\n\nfunction getClearTextKey(key) {\n    if (key === 'CiAdrCity') {\n        return 'CreatorCity';\n    }\n    if (key === 'CiAdrCtry') {\n        return 'CreatorCountry';\n    }\n    if (key === 'CiAdrExtadr') {\n        return 'CreatorAddress';\n    }\n    if (key === 'CiAdrPcode') {\n        return 'CreatorPostalCode';\n    }\n    if (key === 'CiAdrRegion') {\n        return 'CreatorRegion';\n    }\n    if (key === 'CiEmailWork') {\n        return 'CreatorWorkEmail';\n    }\n    if (key === 'CiTelWork') {\n        return 'CreatorWorkPhone';\n    }\n    if (key === 'CiUrlWork') {\n        return 'CreatorWorkUrl';\n    }\n    return key;\n}\n\nfunction parseNodeChildrenAsTags(children) {\n    const tags = {};\n\n    for (const name in children) {\n        try {\n            if (!isNamespaceDefinition(name)) {\n                tags[getLocalName(name)] = parseNodeAsTag(children[name], name);\n            }\n        } catch (error) {\n            // Keep going and try to parse the rest of the tags.\n        }\n    }\n\n    return tags;\n}\n\nfunction parseNodeAsTag(node, name) {\n    if (isDuplicateTag(node)) {\n        return parseNodeAsDuplicateTag(node, name);\n    }\n    if (isEmptyResourceTag(node)) {\n        return {value: '', attributes: {}, description: ''};\n    }\n    if (hasNestedSimpleRdfDescription(node)) {\n        return parseNodeAsSimpleRdfDescription(node, name);\n    }\n    if (hasNestedStructureRdfDescription(node)) {\n        return parseNodeAsStructureRdfDescription(node, name);\n    }\n    if (isCompactStructure(node)) {\n        return parseNodeAsCompactStructure(node, name);\n    }\n    if (isArray(node)) {\n        return parseNodeAsArray(node, name);\n    }\n    return parseNodeAsSimpleValue(node, name);\n}\n\nfunction isEmptyResourceTag(node) {\n    return (node.attributes['rdf:parseType'] === 'Resource')\n        && (typeof node.value === 'string')\n        && (node.value.trim() === '');\n}\n\nfunction isDuplicateTag(node) {\n    return Array.isArray(node);\n}\n\nfunction parseNodeAsDuplicateTag(node, name) {\n    return parseNodeAsSimpleValue(node[node.length - 1], name);\n}\n\nfunction hasNestedSimpleRdfDescription(node) {\n    return ((node.attributes['rdf:parseType'] === 'Resource') && (node.value['rdf:value'] !== undefined))\n        || ((node.value['rdf:Description'] !== undefined) && (node.value['rdf:Description'].value['rdf:value'] !== undefined));\n}\n\nfunction parseNodeAsSimpleRdfDescription(node, name) {\n    const attributes = parseNodeAttributes(node);\n\n    if (node.value['rdf:Description'] !== undefined) {\n        node = node.value['rdf:Description'];\n    }\n\n    objectAssign(attributes, parseNodeAttributes(node), parseNodeChildrenAsAttributes(node));\n\n    const value = parseRdfValue(node);\n\n    return {\n        value,\n        attributes,\n        description: getDescription(value, name)\n    };\n}\n\nfunction parseNodeAttributes(node) {\n    const attributes = {};\n\n    for (const name in node.attributes) {\n        if ((name !== 'rdf:parseType') && (name !== 'rdf:resource') && (!isNamespaceDefinition(name))) {\n            attributes[getLocalName(name)] = node.attributes[name];\n        }\n    }\n\n    return attributes;\n}\n\nfunction parseNodeChildrenAsAttributes(node) {\n    const attributes = {};\n\n    for (const name in node.value) {\n        if ((name !== 'rdf:value') && (!isNamespaceDefinition(name))) {\n            attributes[getLocalName(name)] = node.value[name].value;\n        }\n    }\n\n    return attributes;\n}\n\nfunction parseRdfValue(node) {\n    return getURIValue(node.value['rdf:value']) || node.value['rdf:value'].value;\n}\n\nfunction hasNestedStructureRdfDescription(node) {\n    return (node.attributes['rdf:parseType'] === 'Resource')\n        || ((node.value['rdf:Description'] !== undefined) && (node.value['rdf:Description'].value['rdf:value'] === undefined));\n}\n\nfunction parseNodeAsStructureRdfDescription(node, name) {\n    const tag = {\n        value: {},\n        attributes: {}\n    };\n\n    if (node.value['rdf:Description'] !== undefined) {\n        objectAssign(tag.value, parseNodeAttributesAsTags(node.value['rdf:Description'].attributes));\n        objectAssign(tag.attributes, parseNodeAttributes(node));\n        node = node.value['rdf:Description'];\n    }\n\n    objectAssign(tag.value, parseNodeChildrenAsTags(node.value));\n\n    tag.description = getDescription(tag.value, name);\n\n    return tag;\n}\n\nfunction isCompactStructure(node) {\n    return (Object.keys(node.value).length === 0)\n        && (node.attributes['xml:lang'] === undefined)\n        && (node.attributes['rdf:resource'] === undefined);\n}\n\nfunction parseNodeAsCompactStructure(node, name) {\n    const value = parseNodeAttributesAsTags(node.attributes);\n\n    return {\n        value,\n        attributes: {},\n        description: getDescription(value, name)\n    };\n}\n\nfunction isArray(node) {\n    return getArrayChild(node.value) !== undefined;\n}\n\nfunction getArrayChild(value) {\n    return value['rdf:Bag'] || value['rdf:Seq'] || value['rdf:Alt'];\n}\n\nfunction parseNodeAsArray(node, name) {\n    let items = getArrayChild(node.value).value['rdf:li'];\n    const attributes = parseNodeAttributes(node);\n    const value = [];\n\n    if (items === undefined) {\n        items = [];\n    } else if (!Array.isArray(items)) {\n        items = [items];\n    }\n\n    items.forEach((item) => {\n        value.push(parseArrayValue(item));\n    });\n\n    return {\n        value,\n        attributes,\n        description: getDescription(value, name)\n    };\n}\n\nfunction parseArrayValue(item) {\n    if (hasNestedSimpleRdfDescription(item)) {\n        return parseNodeAsSimpleRdfDescription(item);\n    }\n    if (hasNestedStructureRdfDescription(item)) {\n        return parseNodeAsStructureRdfDescription(item).value;\n    }\n    if (isCompactStructure(item)) {\n        return parseNodeAsCompactStructure(item).value;\n    }\n\n    return parseNodeAsSimpleValue(item);\n}\n\nfunction parseNodeAsSimpleValue(node, name) {\n    const value = getURIValue(node) || parseXMPObject(node.value);\n\n    return {\n        value,\n        attributes: parseNodeAttributes(node),\n        description: getDescription(value, name)\n    };\n}\n\nfunction getURIValue(node) {\n    return node.attributes && node.attributes['rdf:resource'];\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport Types from './types.js';\nimport {\n    getPascalStringFromDataView,\n    padStart,\n    parseFloatRadix,\n    strRepeat\n} from './utils.js';\n\n// export const OsTypeKeys = {\n//     OBJC: 'Objc',\n//     DOUB: 'doub',\n//     UNTF: 'UntF',\n//     TEXT: 'TEXT',\n//     BOOL: 'bool',\n//     ENUM: 'enum',\n//     LONG: 'long'\n// };\n\nexport const PathRecordTypes = {\n    CLOSED_SUBPATH_LENGTH: 0,\n    CLOSED_SUBPATH_BEZIER_LINKED: 1,\n    CLOSED_SUBPATH_BEZIER_UNLINKED: 2,\n    OPEN_SUBPATH_LENGTH: 3,\n    OPEN_SUBPATH_BEZIER_LINKED: 4,\n    OPEN_SUBPATH_BEZIER_UNLINKED: 5,\n    FILL_RULE: 6,\n    CLIPBOARD: 7,\n    INITIAL_FILL_RULE: 8\n};\n\nconst PATH_RECORD_SIZE = 24;\n\nexport default {\n    // 0x0425: {\n    //     name: 'CaptionDigest',\n    //     description(dataView) {\n    //         let description = '';\n    //         for (let i = 0; i < dataView.byteLength; i++) {\n    //             const byte = dataView.getUint8(i);\n    //             description += padStart(byte.toString(16), 2, '0');\n    //         }\n    //         return description;\n    //     }\n    // },\n    // Commented out for now to lower bundle size until someone asks for it.\n    // 0x043a: {\n    //     name: 'PrintInformation',\n    //     description: parseDescriptor\n    // },\n    // 0x043b: {\n    //     name: 'PrintStyle',\n    //     description: parseDescriptor\n    // },\n    0x07d0: {\n        name: 'PathInformation',\n        description: pathResource\n    },\n    0x0bb7: {\n        name: 'ClippingPathName',\n        description(dataView) {\n            const [, string] = getPascalStringFromDataView(dataView, 0);\n            return string;\n        }\n    },\n};\n\n// function parseDescriptor(dataView) {\n//     const DESCRIPTOR_VERSION_SIZE = 4;\n//     // This is a unicode string terminated with null. Unsure about the format though since in my example image it starts with 0x0000.\n//     const UNCLEAR_CLASS_ID_NAME_PART_SIZE = 6;\n//     let offset = 0;\n//     offset += DESCRIPTOR_VERSION_SIZE + UNCLEAR_CLASS_ID_NAME_PART_SIZE;\n//     const [classId, classIdSize] = getBlockValue(dataView, offset);\n//     offset += classIdSize;\n//     const numItems = Types.getLongAt(dataView, offset);\n//     offset += 4;\n//     const descriptor = {[classId]: {}};\n//     for (let i = 0; i < numItems; i++) {\n//         const [itemKey, itemKeySize] = getBlockValue(dataView, offset);\n//         offset += itemKeySize;\n//         const osTypeKey = getStringFromDataView(dataView, offset, 4);\n//         offset += 4;\n//         try {\n//             const {itemValue, itemSize} = getItemValue(dataView, offset, osTypeKey);\n//             offset += itemSize;\n//             descriptor[classId][ITEM_KEY_TERMS[itemKey] || itemKey] = itemValue;\n//         } catch (error) {\n//             // We can't recover from unknown OS type key since we don't know\n//             // where the next one starts.\n//             break;\n//         }\n//     }\n//     return JSON.stringify(descriptor);\n// }\n\n// function getBlockValue(dataView, offset, unicode = false) {\n//     const length = (unicode ? 2 : 1) * Types.getLongAt(dataView, offset) || 4;\n//     offset += 4;\n//     const value = (unicode ? getUnicodeStringFromDataView : getStringFromDataView)(dataView, offset, length);\n//     offset += length;\n//     return [value, 4 + length];\n// }\n\n// function getItemValue(dataView, offset, osTypeKey) {\n//     // Not all OSType keys are implemented yet because they are missing in the example image.\n//     if (osTypeKey === OsTypeKeys.OBJC) {\n//         const [classIdName, classIdNameSize] = getBlockValue(dataView, offset, true);\n//         offset += classIdNameSize;\n//         const [classId, classIdSize] = getBlockValue(dataView, offset);\n//         offset += classIdSize;\n//         const _offset = Types.getLongAt(dataView, offset);\n//         return {\n//             itemValue: {[classIdName]: {[classId]: _offset}},\n//             itemSize: classIdNameSize + classIdSize + 4\n//         };\n//     }\n//     if (osTypeKey === OsTypeKeys.DOUB) {\n//         return {\n//             itemValue: parseDouble(dataView, offset),\n//             itemSize: 8\n//         };\n//     }\n//     if (osTypeKey === OsTypeKeys.UNTF) {\n//         const unit = getStringFromDataView(dataView, offset, 4);\n//         return {\n//             itemValue: {unit, value: parseDouble(dataView, offset + 4)},\n//             itemSize: unit.length + 8\n//         };\n//     }\n//     if (osTypeKey === OsTypeKeys.TEXT) {\n//         const length = 2 * Types.getLongAt(dataView, offset);\n//         offset += 4;\n//         const text = getUnicodeStringFromDataView(dataView, offset, length);\n//         return {\n//             itemValue: text,\n//             itemSize: 4 + length\n//         };\n//     }\n//     if (osTypeKey === OsTypeKeys.BOOL) {\n//         return {\n//             itemValue: Types.getByteAt(dataView, offset) === 1,\n//             itemSize: 1\n//         };\n//     }\n//     if (osTypeKey === OsTypeKeys.ENUM) {\n//         const [type, typeSize] = getBlockValue(dataView, offset);\n//         offset += typeSize;\n//         const [enumName, enumSize] = getBlockValue(dataView, offset);\n//         return {\n//             itemValue: {[type]: enumName},\n//             itemSize: typeSize + enumSize\n//         };\n//     }\n//     if (osTypeKey === OsTypeKeys.LONG) {\n//         return {\n//             itemValue: Types.getLongAt(dataView, offset),\n//             itemSize: 4\n//         };\n//     }\n//     throw new Error(`Unknown OS type key: ${osTypeKey}`);\n// }\n\n// function parseDouble(dataView, offset) {\n//     const BIAS = 1023;\n//     const sign = (Types.getByteAt(dataView, offset) & parseInt('10000000', 2)) === 0 ? 1 : -1;\n//     const exponent = ((Types.getShortAt(dataView, offset) & parseInt('0111111111110000', 2)) >> 4) - BIAS;\n//     const fractionHigh = padStart((Types.getLongAt(dataView, offset) & parseInt('00000000000011111111111111111111', 2)).toString(2), 20, '0');\n//     const fractionLow = padStart(Types.getLongAt(dataView, offset + 4).toString(2), 32, '0');\n//     const fraction = parseFloatRadix('1.' + fractionHigh + fractionLow, 2);\n//     return sign * fraction * Math.pow(2, exponent);\n// }\n\nfunction pathResource(dataView) {\n    const TYPE_SIZE = 2;\n    const types = {};\n    const paths = [];\n\n    for (let offset = 0; offset < dataView.byteLength; offset += TYPE_SIZE + PATH_RECORD_SIZE) {\n        const type = Types.getShortAt(dataView, offset);\n        if (PATH_RECORD_TYPES[type]) {\n            if (!types[type]) {\n                types[type] = PATH_RECORD_TYPES[type].description;\n            }\n            paths.push({\n                type,\n                path: PATH_RECORD_TYPES[type].path(dataView, offset + TYPE_SIZE)\n            });\n        }\n    }\n    return JSON.stringify({types, paths});\n}\n\nconst PATH_RECORD_TYPES = {\n    [PathRecordTypes.CLOSED_SUBPATH_LENGTH]: {\n        description: 'Closed subpath length',\n        path: (dataView, offset) => [Types.getShortAt(dataView, offset)]\n    },\n    [PathRecordTypes.CLOSED_SUBPATH_BEZIER_LINKED]: {\n        description: 'Closed subpath Bezier knot, linked',\n        path: parseBezierKnot\n    },\n    [PathRecordTypes.CLOSED_SUBPATH_BEZIER_UNLINKED]: {\n        description: 'Closed subpath Bezier knot, unlinked',\n        path: parseBezierKnot\n    },\n    [PathRecordTypes.OPEN_SUBPATH_LENGTH]: {\n        description: 'Open subpath length',\n        path: (dataView, offset) => [Types.getShortAt(dataView, offset)]\n    },\n    [PathRecordTypes.OPEN_SUBPATH_BEZIER_LINKED]: {\n        description: 'Open subpath Bezier knot, linked',\n        path: parseBezierKnot\n    },\n    [PathRecordTypes.OPEN_SUBPATH_BEZIER_UNLINKED]: {\n        description: 'Open subpath Bezier knot, unlinked',\n        path: parseBezierKnot\n    },\n    [PathRecordTypes.FILL_RULE]: {\n        description: 'Path fill rule',\n        path: () => []\n    },\n    [PathRecordTypes.INITIAL_FILL_RULE]: {\n        description: 'Initial fill rule',\n        path: (dataView, offset) => [Types.getShortAt(dataView, offset)]\n    },\n    [PathRecordTypes.CLIPBOARD]: {\n        description: 'Clipboard',\n        path: parseClipboard\n    }\n};\n\nfunction parseBezierKnot(dataView, offset) {\n    const PATH_POINT_SIZE = 8;\n    const path = [];\n    for (let i = 0; i < PATH_RECORD_SIZE; i += PATH_POINT_SIZE) {\n        path.push(parsePathPoint(dataView, offset + i));\n    }\n    return path;\n}\n\nfunction parsePathPoint(dataView, offset) {\n    const vertical = getFixedPointNumber(dataView, offset, 8);\n    const horizontal = getFixedPointNumber(dataView, offset + 4, 8);\n    return [horizontal, vertical];\n}\n\nfunction parseClipboard(dataView, offset) {\n    return [\n        [\n            getFixedPointNumber(dataView, offset, 8), // Top\n            getFixedPointNumber(dataView, offset + 4, 8), // Left\n            getFixedPointNumber(dataView, offset + 8, 8), // Botton\n            getFixedPointNumber(dataView, offset + 12, 8), // Right\n        ],\n        getFixedPointNumber(dataView, offset + 16, 8) // Resolution\n    ];\n}\n\nfunction getFixedPointNumber(dataView, offset, binaryPoint) {\n    const number = Types.getLongAt(dataView, offset);\n\n    const sign = (number >>> 31) === 0 ? 1 : -1;\n    const integer = (number & 0x7f000000) >>> (32 - binaryPoint);\n    const fraction = number & parseInt(strRepeat('1', 32 - binaryPoint), 2);\n\n    return sign * parseFloatRadix(integer.toString(2) + '.' + padStart(fraction.toString(2), 32 - binaryPoint, '0'), 2);\n}\n\n// Item key terminology: https://psd-tools.readthedocs.io/en/latest/reference/psd_tools.terminology.html\n// Are these correct? There are collisions that are commented out. A lot of code for little gain?\n// const ITEM_KEY_TERMS = {\n//     'A   ': 'A',\n//     'Adjs': 'Adjustment',\n//     'Algd': 'Aligned',\n//     'Algn': 'Alignment',\n//     'AllE': 'AllExcept',\n//     'All ': 'AllPS',\n//     'AlTl': 'AllToolOptions',\n//     'AChn': 'AlphaChannelOptions',\n//     'AlpC': 'AlphaChannels',\n//     'AmbB': 'AmbientBrightness',\n//     'AmbC': 'AmbientColor',\n//     'Amnt': 'Amount',\n//     'AmMx': 'AmplitudeMax',\n//     'AmMn': 'AmplitudeMin',\n//     'Anch': 'Anchor',\n//     'Angl': 'Angle',\n//     'Ang1': 'Angle1',\n//     'Ang2': 'Angle2',\n//     'Ang3': 'Angle3',\n//     'Ang4': 'Angle4',\n//     'AntA': 'AntiAlias',\n//     'Appe': 'Append',\n//     'Aply': 'Apply',\n//     'Ar  ': 'Area',\n//     'Arrw': 'Arrowhead',\n//     'As  ': 'As',\n//     'Asst': 'AssetBin',\n//     'AssC': 'AssumedCMYK',\n//     'AssG': 'AssumedGray',\n//     'AssR': 'AssumedRGB',\n//     'At  ': 'At',\n//     'Auto': 'Auto',\n//     'AuCo': 'AutoContrast',\n//     'Atrs': 'AutoErase',\n//     'AtKr': 'AutoKern',\n//     'AtUp': 'AutoUpdate',\n//     'Axis': 'Axis',\n//     'B   ': 'B',\n//     'Bckg': 'Background',\n//     'BckC': 'BackgroundColor',\n//     'BckL': 'BackgroundLevel',\n//     'Bwd ': 'Backward',\n//     'Blnc': 'Balance',\n//     'Bsln': 'BaselineShift',\n//     'BpWh': 'BeepWhenDone',\n//     'BgnR': 'BeginRamp',\n//     'BgnS': 'BeginSustain',\n//     'bvlD': 'BevelDirection',\n//     'ebbl': 'BevelEmboss',\n//     'bvlS': 'BevelStyle',\n//     'bvlT': 'BevelTechnique',\n//     'BgNH': 'BigNudgeH',\n//     'BgNV': 'BigNudgeV',\n//     'BtDp': 'BitDepth',\n//     'Blck': 'Black',\n//     'BlcC': 'BlackClip',\n//     'Blcn': 'BlackGeneration',\n//     'BlcG': 'BlackGenerationCurve',\n//     'BlcI': 'BlackIntensity',\n//     'BlcL': 'BlackLevel',\n//     // 'BlcL': 'BlackLimit',\n//     'Bld ': 'Bleed',\n//     'Blnd': 'BlendRange',\n//     'Bl  ': 'Blue',\n//     'BlBl': 'BlueBlackPoint',\n//     'blueFloat': 'BlueFloat',\n//     'BlGm': 'BlueGamma',\n//     'BlWh': 'BlueWhitePoint',\n//     'BlX ': 'BlueX',\n//     'BlY ': 'BlueY',\n//     'blur': 'Blur',\n//     'BlrM': 'BlurMethod',\n//     'BlrQ': 'BlurQuality',\n//     'Bk  ': 'Book',\n//     'BrdT': 'BorderThickness',\n//     'Btom': 'Bottom',\n//     'Brgh': 'Brightness',\n//     'BrsD': 'BrushDetail',\n//     'BrsS': 'BrushSize',\n//     'BrsT': 'BrushType',\n//     'Brsh': 'Brushes',\n//     'BmpA': 'BumpAmplitude',\n//     'BmpC': 'BumpChannel',\n//     'By  ': 'By',\n//     'Byln': 'Byline',\n//     'BylT': 'BylineTitle',\n//     'BytO': 'ByteOrder',\n//     'CMYS': 'CMYKSetup',\n//     'CchP': 'CachePrefs',\n//     'Clcl': 'Calculation',\n//     'Clbr': 'CalibrationBars',\n//     'Cptn': 'Caption',\n//     'CptW': 'CaptionWriter',\n//     'Ctgr': 'Category',\n//     'ClSz': 'CellSize',\n//     'Cntr': 'Center',\n//     'CntC': 'CenterCropMarks',\n//     'ChlA': 'ChalkArea',\n//     'Chnl': 'Channel',\n//     'ChMx': 'ChannelMatrix',\n//     'ChnN': 'ChannelName',\n//     'Chns': 'Channels',\n//     'ChnI': 'ChannelsInterleaved',\n//     'ChAm': 'CharcoalAmount',\n//     'ChrA': 'CharcoalArea',\n//     'Ckmt': 'ChokeMatte',\n//     'ChFX': 'ChromeFX',\n//     'City': 'City',\n//     'ClrA': 'ClearAmount',\n//     'ClPt': 'ClippingPath',\n//     'ClpP': 'ClippingPathEPS',\n//     'ClpF': 'ClippingPathFlatness',\n//     'ClpI': 'ClippingPathIndex',\n//     'Clpg': 'ClippingPathInfo',\n//     'ClnS': 'CloneSource',\n//     'Clsp': 'ClosedSubpath',\n//     'Clr ': 'Color',\n//     'Clrh': 'ColorChannels',\n//     'ClrC': 'ColorCorrection',\n//     'ClrI': 'ColorIndicates',\n//     'ClMg': 'ColorManagement',\n//     'Clrr': 'ColorPickerPrefs',\n//     'ClrS': 'ColorSpace',\n//     'ClrT': 'ColorTable',\n//     'Clrz': 'Colorize',\n//     'Clrs': 'Colors',\n//     'ClrL': 'ColorsList',\n//     'ClmW': 'ColumnWidth',\n//     'CmdK': 'CommandKey',\n//     'Cmpn': 'Compensation',\n//     'Cmpr': 'Compression',\n//     'Cncv': 'Concavity',\n//     'Cndt': 'Condition',\n//     'Cnst': 'Constant',\n//     // 'Cnst': 'Constrain',\n//     'CnsP': 'ConstrainProportions',\n//     'Cfov': 'ConstructionFOV',\n//     'Cntg': 'Contiguous',\n//     'Cntn': 'Continue',\n//     'Cnty': 'Continuity',\n//     'ShpC': 'ContourType',\n//     // 'Cntr': 'Contrast',\n//     'Cnvr': 'Convert',\n//     'Cpy ': 'Copy',\n//     'Cpyr': 'Copyright',\n//     'CprN': 'CopyrightNotice',\n//     'CrnC': 'CornerCropMarks',\n//     'Cnt ': 'Count',\n//     'CntN': 'CountryName',\n//     'CrcB': 'CrackBrightness',\n//     'CrcD': 'CrackDepth',\n//     'CrcS': 'CrackSpacing',\n//     'blfl': 'CreateLayersFromLayerFX',\n//     'Crdt': 'Credit',\n//     'Crss': 'Crossover',\n//     'Crnt': 'Current',\n//     'CrnH': 'CurrentHistoryState',\n//     'CrnL': 'CurrentLight',\n//     'CrnT': 'CurrentToolOptions',\n//     'Crv ': 'Curve',\n//     'CrvF': 'CurveFile',\n//     'Cstm': 'Custom',\n//     'CstF': 'CustomForced',\n//     'CstM': 'CustomMatte',\n//     'CstP': 'CustomPalette',\n//     'Cyn ': 'Cyan',\n//     'DCS ': 'DCS',\n//     'DPXf': 'DPXFormat',\n//     'DrkI': 'DarkIntensity',\n//     'Drkn': 'Darkness',\n//     'DtCr': 'DateCreated',\n//     'Dt ': 'Datum',\n//     'Dfnt': 'Definition',\n//     'Dnst': 'Density',\n//     'Dpth': 'Depth',\n//     'Dstl': 'DestBlackMax',\n//     'DstB': 'DestBlackMin',\n//     'Dstt': 'DestWhiteMax',\n//     'DstW': 'DestWhiteMin',\n//     'DstM': 'DestinationMode',\n//     'Dtl ': 'Detail',\n//     'Dmtr': 'Diameter',\n//     'DffD': 'DiffusionDither',\n//     'Drct': 'Direction',\n//     'DrcB': 'DirectionBalance',\n//     'DspF': 'DisplaceFile',\n//     'DspM': 'DisplacementMap',\n//     'DspP': 'DisplayPrefs',\n//     'Dstn': 'Distance',\n//     // 'Dstr': 'Distortion',\n//     // 'Dstr': 'Distribution',\n//     'Dthr': 'Dither',\n//     'DthA': 'DitherAmount',\n//     'Dthp': 'DitherPreserve',\n//     'Dthq': 'DitherQuality',\n//     'DocI': 'DocumentID',\n//     'DtGn': 'DotGain',\n//     'DtGC': 'DotGainCurves',\n//     'DrSh': 'DropShadow',\n//     'Dplc': 'Duplicate',\n//     'DnmC': 'DynamicColorSliders',\n//     'Edg ': 'Edge',\n//     'EdgB': 'EdgeBrightness',\n//     'EdgF': 'EdgeFidelity',\n//     'EdgI': 'EdgeIntensity',\n//     'EdgS': 'EdgeSimplicity',\n//     'EdgT': 'EdgeThickness',\n//     'EdgW': 'EdgeWidth',\n//     'Effc': 'Effect',\n//     'EmbC': 'EmbedCMYK',\n//     'EmbG': 'EmbedGray',\n//     'EmbL': 'EmbedLab',\n//     'EmbP': 'EmbedProfiles',\n//     'EmbR': 'EmbedRGB',\n//     'EmlD': 'EmulsionDown',\n//     'EGst': 'EnableGestures',\n//     'enab': 'Enabled',\n//     'Encd': 'Encoding',\n//     'End ': 'End',\n//     'EndA': 'EndArrowhead',\n//     'EndR': 'EndRamp',\n//     'EndS': 'EndSustain',\n//     'Engn': 'Engine',\n//     'ErsT': 'EraseToHistory',\n//     'ErsK': 'EraserKind',\n//     'ExcP': 'ExactPoints',\n//     'Expr': 'Export',\n//     'ExpC': 'ExportClipboard',\n//     'Exps': 'Exposure',\n//     'Extd': 'Extend',\n//     'EQlt': 'ExtendedQuality',\n//     'Extn': 'Extension',\n//     'ExtQ': 'ExtensionsQuery',\n//     'ExtD': 'ExtrudeDepth',\n//     'ExtM': 'ExtrudeMaskIncomplete',\n//     'ExtR': 'ExtrudeRandom',\n//     'ExtS': 'ExtrudeSize',\n//     'ExtF': 'ExtrudeSolidFace',\n//     'ExtT': 'ExtrudeType',\n//     'EyDr': 'EyeDropperSample',\n//     'FxCm': 'FPXCompress',\n//     'FxQl': 'FPXQuality',\n//     'FxSz': 'FPXSize',\n//     'FxVw': 'FPXView',\n//     'FdT ': 'FadeTo',\n//     'FdtS': 'FadeoutSteps',\n//     'FlOf': 'Falloff',\n//     'Fthr': 'Feather',\n//     'FbrL': 'FiberLength',\n//     'File': 'File',\n//     'FlCr': 'FileCreator',\n//     'FlIn': 'FileInfo',\n//     'FilR': 'FileReference',\n//     'FlSP': 'FileSavePrefs',\n//     'FlTy': 'FileType',\n//     'flst': 'FilesList',\n//     'Fl  ': 'Fill',\n//     'FlCl': 'FillColor',\n//     'FlNt': 'FillNeutral',\n//     'FlPd': 'FilterLayerPersistentData',\n//     'FlRs': 'FilterLayerRandomSeed',\n//     'Fngr': 'Fingerpainting',\n//     'FlrC': 'FlareCenter',\n//     'Fltn': 'Flatness',\n//     'Fltt': 'Flatten',\n//     'FlpV': 'FlipVertical',\n//     'Fcs ': 'Focus',\n//     'Fldr': 'Folders',\n//     'FntD': 'FontDesignAxes',\n//     'FntV': 'FontDesignAxesVectors',\n//     'FntN': 'FontName',\n//     'Scrp': 'FontScript',\n//     'FntS': 'FontStyleName',\n//     'FntT': 'FontTechnology',\n//     'FrcC': 'ForcedColors',\n//     'FrgC': 'ForegroundColor',\n//     'FrgL': 'ForegroundLevel',\n//     'Fmt ': 'Format',\n//     'Fwd ': 'Forward',\n//     'FrFX': 'FrameFX',\n//     'FrmW': 'FrameWidth',\n//     'FTcs': 'FreeTransformCenterState',\n//     'Frqn': 'Frequency',\n//     'From': 'From',\n//     'FrmB': 'FromBuiltin',\n//     'FrmM': 'FromMode',\n//     'FncK': 'FunctionKey',\n//     'Fzns': 'Fuzziness',\n//     'GCR ': 'GCR',\n//     'GFPT': 'GIFColorFileType',\n//     'GFCL': 'GIFColorLimit',\n//     'GFEC': 'GIFExportCaption',\n//     'GFMI': 'GIFMaskChannelIndex',\n//     'GFMV': 'GIFMaskChannelInverted',\n//     'GFPF': 'GIFPaletteFile',\n//     'GFPL': 'GIFPaletteType',\n//     'GFCS': 'GIFRequiredColorSpaceType',\n//     'GFIT': 'GIFRowOrderType',\n//     'GFTC': 'GIFTransparentColor',\n//     'GFTB': 'GIFTransparentIndexBlue',\n//     'GFTG': 'GIFTransparentIndexGreen',\n//     'GFTR': 'GIFTransparentIndexRed',\n//     'GFBM': 'GIFUseBestMatch',\n//     'Gmm ': 'Gamma',\n//     'GmtW': 'GamutWarning',\n//     'GnrP': 'GeneralPrefs',\n//     'gblA': 'GlobalAngle',\n//     'gagl': 'GlobalLightingAngle',\n//     'Glos': 'Gloss',\n//     'GlwA': 'GlowAmount',\n//     'GlwT': 'GlowTechnique',\n//     'Grad': 'Gradient',\n//     'Grdf': 'GradientFill',\n//     // 'Grn ': 'Grain',\n//     'Grnt': 'GrainType',\n//     'Grns': 'Graininess',\n//     'Gry ': 'Gray',\n//     'GrBh': 'GrayBehavior',\n//     'GrSt': 'GraySetup',\n//     'Grn ': 'Green',\n//     'GrnB': 'GreenBlackPoint',\n//     'greenFloat': 'GreenFloat',\n//     'GrnG': 'GreenGamma',\n//     'GrnW': 'GreenWhitePoint',\n//     'GrnX': 'GreenX',\n//     'GrnY': 'GreenY',\n//     'GrdC': 'GridColor',\n//     'Grds': 'GridCustomColor',\n//     'GrdM': 'GridMajor',\n//     'Grdn': 'GridMinor',\n//     'GrdS': 'GridStyle',\n//     'Grdt': 'GridUnits',\n//     'Grup': 'Group',\n//     'GrtW': 'GroutWidth',\n//     'GrwS': 'GrowSelection',\n//     'Gdes': 'Guides',\n//     'GdsC': 'GuidesColor',\n//     'Gdss': 'GuidesCustomColor',\n//     'GdPr': 'GuidesPrefs',\n//     'GdsS': 'GuidesStyle',\n//     'GttW': 'GutterWidth',\n//     'HlfF': 'HalftoneFile',\n//     'HlfS': 'HalftoneScreen',\n//     'HlSz': 'HalftoneSize',\n//     'Hlfp': 'HalftoneSpec',\n//     'Hrdn': 'Hardness',\n//     'HCdH': 'HasCmdHPreference',\n//     'Hdr ': 'Header',\n//     'Hdln': 'Headline',\n//     'Hght': 'Height',\n//     'HghA': 'HighlightArea',\n//     'hglC': 'HighlightColor',\n//     'HghL': 'HighlightLevels',\n//     'hglM': 'HighlightMode',\n//     'hglO': 'HighlightOpacity',\n//     'HghS': 'HighlightStrength',\n//     'HstB': 'HistoryBrushSource',\n//     'HstP': 'HistoryPrefs',\n//     'HsSS': 'HistoryStateSource',\n//     'HsSt': 'HistoryStates',\n//     'Hrzn': 'Horizontal',\n//     'HrzS': 'HorizontalScale',\n//     'HstN': 'HostName',\n//     'HstV': 'HostVersion',\n//     'H   ': 'Hue',\n//     'ICCE': 'ICCEngine',\n//     'ICCt': 'ICCSetupName',\n//     'Idnt': 'ID',\n//     'Idle': 'Idle',\n//     'ImgB': 'ImageBalance',\n//     'Impr': 'Import',\n//     'Imps': 'Impressionist',\n//     'In  ': 'In',\n//     'c@#^': 'Inherits',\n//     'InkC': 'InkColors',\n//     'Inks': 'Inks',\n//     'IrGl': 'InnerGlow',\n//     'glwS': 'InnerGlowSource',\n//     'IrSh': 'InnerShadow',\n//     'Inpt': 'Input',\n//     'kIBP': 'InputBlackPoint',\n//     'Inmr': 'InputMapRange',\n//     'Inpr': 'InputRange',\n//     'kIWP': 'InputWhitePoint',\n//     'Intn': 'Intensity',\n//     'Inte': 'Intent',\n//     'IntH': 'InterfaceBevelHighlight',\n//     'Intv': 'InterfaceBevelShadow',\n//     'IntB': 'InterfaceBlack',\n//     'Intd': 'InterfaceBorder',\n//     'Intk': 'InterfaceButtonDarkShadow',\n//     'Intt': 'InterfaceButtonDownFill',\n//     'InBF': 'InterfaceButtonUpFill',\n//     'ICBL': 'InterfaceColorBlue2',\n//     'ICBH': 'InterfaceColorBlue32',\n//     'ICGL': 'InterfaceColorGreen2',\n//     'ICGH': 'InterfaceColorGreen32',\n//     'ICRL': 'InterfaceColorRed2',\n//     'ICRH': 'InterfaceColorRed32',\n//     'IntI': 'InterfaceIconFillActive',\n//     'IntF': 'InterfaceIconFillDimmed',\n//     'Intc': 'InterfaceIconFillSelected',\n//     'Intm': 'InterfaceIconFrameActive',\n//     // 'Intr': 'InterfaceIconFrameDimmed',\n//     'IntS': 'InterfaceIconFrameSelected',\n//     'IntP': 'InterfacePaletteFill',\n//     'IntR': 'InterfaceRed',\n//     'IntT': 'InterfaceToolTipBackground',\n//     'ITTT': 'InterfaceToolTipText',\n//     'ITBg': 'InterfaceTransparencyBackground',\n//     'ITFg': 'InterfaceTransparencyForeground',\n//     'IntW': 'InterfaceWhite',\n//     // 'Intr': 'Interlace',\n//     'IntC': 'InterlaceCreateType',\n//     'IntE': 'InterlaceEliminateType',\n//     // 'Intr': 'Interpolation',\n//     'IntM': 'InterpolationMethod',\n//     'Invr': 'Invert',\n//     'InvM': 'InvertMask',\n//     'InvS': 'InvertSource2',\n//     'InvT': 'InvertTexture',\n//     'IsDr': 'IsDirty',\n//     'ItmI': 'ItemIndex',\n//     'JPEQ': 'JPEGQuality',\n//     'Krng': 'Kerning',\n//     'Kywd': 'Keywords',\n//     'Knd ': 'Kind',\n//     'LTnm': 'LUTAnimation',\n//     'LZWC': 'LZWCompression',\n//     'Lbls': 'Labels',\n//     'Lnds': 'Landscape',\n//     'LstT': 'LastTransform',\n//     'Lyr ': 'Layer',\n//     'Lefx': 'LayerEffects',\n//     'lfxv': 'LayerFXVisible',\n//     'LyrI': 'LayerID',\n//     'LyrN': 'LayerName',\n//     'Lyrs': 'Layers',\n//     'Ldng': 'Leading',\n//     'Left': 'Left',\n//     'lSNs': 'LegacySerialString',\n//     // 'Lngt': 'Length',\n//     'Lns ': 'Lens',\n//     'Lvl ': 'Level',\n//     'Lvls': 'Levels',\n//     'LgDr': 'LightDark',\n//     'LghD': 'LightDirection',\n//     'LghI': 'LightIntensity',\n//     'LghP': 'LightPosition',\n//     'LghS': 'LightSource',\n//     'LghT': 'LightType',\n//     'LghG': 'LightenGrout',\n//     'Lght': 'Lightness',\n//     'Line': 'Line',\n//     'lnkE': 'LinkEnable',\n//     'LnkL': 'LinkedLayerIDs',\n//     'Lald': 'LocalLightingAltitude',\n//     'lagl': 'LocalLightingAngle',\n//     'LclR': 'LocalRange',\n//     'Lctn': 'Location',\n//     'Log ': 'Log',\n//     'kLog': 'Logarithmic',\n//     'LwCs': 'LowerCase',\n//     'Lmnc': 'Luminance',\n//     'Mgnt': 'Magenta',\n//     'MkVs': 'MakeVisible',\n//     'Mfov': 'ManipulationFOV',\n//     'MpBl': 'MapBlack',\n//     'Mpng': 'Mapping',\n//     'MpgS': 'MappingShape',\n//     'Mtrl': 'Material',\n//     'Mtrx': 'Matrix',\n//     'MttC': 'MatteColor',\n//     'Mxm ': 'Maximum',\n//     'MxmS': 'MaximumStates',\n//     'MmrU': 'MemoryUsagePercent',\n//     'Mrge': 'Merge',\n//     'Mrgd': 'Merged',\n//     'Msge': 'Message',\n//     'Mthd': 'Method',\n//     'MztT': 'MezzotintType',\n//     'Mdpn': 'Midpoint',\n//     'MdtL': 'MidtoneLevels',\n//     'Mnm ': 'Minimum',\n//     'MsmC': 'MismatchCMYK',\n//     'MsmG': 'MismatchGray',\n//     'MsmR': 'MismatchRGB',\n//     'Md  ': 'Mode',\n//     'Mnch': 'Monochromatic',\n//     'MvT ': 'MoveTo',\n//     'Nm  ': 'Name',\n//     'Ngtv': 'Negative',\n//     'Nw  ': 'New',\n//     'Nose': 'Noise',\n//     'NnIm': 'NonImageData',\n//     'NnLn': 'NonLinear',\n//     'null': 'Null',\n//     'Nm L': 'NumLights',\n//     'Nmbr': 'Number',\n//     'NCch': 'NumberOfCacheLevels',\n//     'NC64': 'NumberOfCacheLevels64',\n//     'NmbO': 'NumberOfChannels',\n//     'NmbC': 'NumberOfChildren',\n//     'NmbD': 'NumberOfDocuments',\n//     'NmbG': 'NumberOfGenerators',\n//     // 'NmbL': 'NumberOfLayers',\n//     // 'NmbL': 'NumberOfLevels',\n//     'NmbP': 'NumberOfPaths',\n//     'NmbR': 'NumberOfRipples',\n//     'NmbS': 'NumberOfSiblings',\n//     'ObjN': 'ObjectName',\n//     'Ofst': 'Offset',\n//     'Sftt': 'OldSmallFontType',\n//     'On  ': 'On',\n//     'Opct': 'Opacity',\n//     'Optm': 'Optimized',\n//     'Ornt': 'Orientation',\n//     'OrgH': 'OriginalHeader',\n//     'OrgT': 'OriginalTransmissionReference',\n//     'OthC': 'OtherCursors',\n//     'OrGl': 'OuterGlow',\n//     'Otpt': 'Output',\n//     'kOBP': 'OutputBlackPoint',\n//     'kOWP': 'OutputWhitePoint',\n//     'OvrC': 'OverprintColors',\n//     'OvrO': 'OverrideOpen',\n//     'ObrP': 'OverridePrinter',\n//     'Ovrd': 'OverrideSave',\n//     'PNGf': 'PNGFilter',\n//     'PGIT': 'PNGInterlaceType',\n//     'PMpf': 'PageFormat',\n//     'PgNm': 'PageNumber',\n//     'PgPs': 'PagePosition',\n//     'PgSt': 'PageSetup',\n//     'PnCK': 'PaintCursorKind',\n//     'PntT': 'PaintType',\n//     'PntC': 'PaintingCursors',\n//     'Plt ': 'Palette',\n//     'PltF': 'PaletteFile',\n//     'PprB': 'PaperBrightness',\n//     'PrIn': 'ParentIndex',\n//     'PrNm': 'ParentName',\n//     'Path': 'Path',\n//     'PthC': 'PathContents',\n//     'PthN': 'PathName',\n//     'Pttn': 'Pattern',\n//     'Pncl': 'PencilWidth',\n//     'Prsp': 'PerspectiveIndex',\n//     'Phsp': 'Phosphors',\n//     'PckI': 'PickerID',\n//     'Pckr': 'PickerKind',\n//     'PPSz': 'PixelPaintSize',\n//     'Pltf': 'Platform',\n//     'PlgF': 'PluginFolder',\n//     'PlgP': 'PluginPrefs',\n//     'Pts ': 'Points',\n//     'Pstn': 'Position',\n//     'PstS': 'PostScriptColor',\n//     'Pstr': 'Posterization',\n//     'PrdC': 'PredefinedColors',\n//     'PrfB': 'PreferBuiltin',\n//     'Prfr': 'Preferences',\n//     'PrsA': 'PreserveAdditional',\n//     'PrsL': 'PreserveLuminosity',\n//     'PrsT': 'PreserveTransparency',\n//     'Prs ': 'Pressure',\n//     'Prvw': 'Preview',\n//     'PrvK': 'PreviewCMYK',\n//     'PrvF': 'PreviewFullSize',\n//     'PrvI': 'PreviewIcon',\n//     'PrvM': 'PreviewMacThumbnail',\n//     'PrvW': 'PreviewWinThumbnail',\n//     'PrvQ': 'PreviewsQuery',\n//     'PMps': 'PrintSettings',\n//     'PrfS': 'ProfileSetup',\n//     'PrvS': 'ProvinceState',\n//     'Qlty': 'Quality',\n//     'QucM': 'QuickMask',\n//     'RGBS': 'RGBSetup',\n//     'Rds ': 'Radius',\n//     'RndS': 'RandomSeed',\n//     'Rt  ': 'Ratio',\n//     'Rcnf': 'RecentFiles',\n//     'Rd  ': 'Red',\n//     'RdBl': 'RedBlackPoint',\n//     'redFloat': 'RedFloat',\n//     'RdGm': 'RedGamma',\n//     'RdWh': 'RedWhitePoint',\n//     'RdX ': 'RedX',\n//     'RdY ': 'RedY',\n//     'RgsM': 'RegistrationMarks',\n//     'Rltv': 'Relative',\n//     'Rlf ': 'Relief',\n//     'Rfid': 'RenderFidelity',\n//     'Rsmp': 'Resample',\n//     'RWOZ': 'ResizeWindowsOnZoom',\n//     'Rslt': 'Resolution',\n//     'RsrI': 'ResourceID',\n//     'Rspn': 'Response',\n//     'RtnH': 'RetainHeader',\n//     'Rvrs': 'Reverse',\n//     'Rght': 'Right',\n//     'RplM': 'RippleMagnitude',\n//     'RplS': 'RippleSize',\n//     'Rtt ': 'Rotate',\n//     'Rndn': 'Roundness',\n//     'RlrH': 'RulerOriginH',\n//     'RlrV': 'RulerOriginV',\n//     'RlrU': 'RulerUnits',\n//     // 'Strt': 'Saturation',\n//     'SvAn': 'SaveAndClose',\n//     'SvCm': 'SaveComposite',\n//     'PltL': 'SavePaletteLocations',\n//     'SvPt': 'SavePaths',\n//     'SvPy': 'SavePyramids',\n//     'Svng': 'Saving',\n//     'Scl ': 'Scale',\n//     'SclH': 'ScaleHorizontal',\n//     'SclV': 'ScaleVertical',\n//     'Scln': 'Scaling',\n//     'Scns': 'Scans',\n//     'ScrD': 'ScratchDisks',\n//     'ScrF': 'ScreenFile',\n//     'ScrT': 'ScreenType',\n//     'Sprt': 'Separations',\n//     'SrlS': 'SerialString',\n//     // 'ShdI': 'ShadingIntensity',\n//     'ShdN': 'ShadingNoise',\n//     'ShdS': 'ShadingShape',\n//     'sdwC': 'ShadowColor',\n//     // 'ShdI': 'ShadowIntensity',\n//     'ShdL': 'ShadowLevels',\n//     'sdwM': 'ShadowMode',\n//     'sdwO': 'ShadowOpacity',\n//     'Shp ': 'Shape',\n//     'Shrp': 'Sharpness',\n//     'ShrE': 'ShearEd',\n//     'ShrP': 'ShearPoints',\n//     'ShrS': 'ShearSt',\n//     'ShfK': 'ShiftKey',\n//     'ShKT': 'ShiftKeyToolSwitch',\n//     'ShrN': 'ShortNames',\n//     'ShwE': 'ShowEnglishFontNames',\n//     'SwMC': 'ShowMenuColors',\n//     'ShwT': 'ShowToolTips',\n//     'ShTr': 'ShowTransparency',\n//     'Sz  ': 'SizeKey',\n//     'Skew': 'Skew',\n//     'Sfts': 'SmallFontType',\n//     'SmBM': 'SmartBlurMode',\n//     'SmBQ': 'SmartBlurQuality',\n//     'Smoo': 'Smooth',\n//     'Smth': 'Smoothness',\n//     'SnpI': 'SnapshotInitial',\n//     'SfCl': 'SoftClip',\n//     'Sftn': 'Softness',\n//     'SoFi': 'SolidFill',\n//     'Srce': 'Source',\n//     'Src2': 'Source2',\n//     'SrcM': 'SourceMode',\n//     'Spcn': 'Spacing',\n//     'SpcI': 'SpecialInstructions',\n//     'SphM': 'SpherizeMode',\n//     'Spot': 'Spot',\n//     'SprR': 'SprayRadius',\n//     'SqrS': 'SquareSize',\n//     'Srcl': 'SrcBlackMax',\n//     'SrcB': 'SrcBlackMin',\n//     'Srcm': 'SrcWhiteMax',\n//     'SrcW': 'SrcWhiteMin',\n//     // 'Strt': 'Start',\n//     'StrA': 'StartArrowhead',\n//     'Stte': 'State',\n//     'srgh': 'Strength',\n//     'srgR': 'StrengthRatio',\n//     'Strg': 'Strength_PLUGIN',\n//     'StDt': 'StrokeDetail',\n//     'SDir': 'StrokeDirection',\n//     'StrL': 'StrokeLength',\n//     'StrP': 'StrokePressure',\n//     'StrS': 'StrokeSize',\n//     'StrW': 'StrokeWidth',\n//     'Styl': 'Style',\n//     'Stys': 'Styles',\n//     'StlC': 'StylusIsColor',\n//     'StlO': 'StylusIsOpacity',\n//     'StlP': 'StylusIsPressure',\n//     'StlS': 'StylusIsSize',\n//     'SbpL': 'SubPathList',\n//     'SplC': 'SupplementalCategories',\n//     'SstI': 'SystemInfo',\n//     'SstP': 'SystemPalette',\n//     // 'null': 'Target',\n//     'Trgp': 'TargetPath',\n//     'TrgP': 'TargetPathIndex',\n//     // 'Lngt': 'TermLength',\n//     'Txt ': 'Text',\n//     // 'TxtC': 'TextClickPoint',\n//     'TxtD': 'TextData',\n//     'TxtS': 'TextStyle',\n//     'Txtt': 'TextStyleRange',\n//     'Txtr': 'Texture',\n//     // 'TxtC': 'TextureCoverage',\n//     'TxtF': 'TextureFile',\n//     'TxtT': 'TextureType',\n//     'Thsh': 'Threshold',\n//     'TlNm': 'TileNumber',\n//     'TlOf': 'TileOffset',\n//     'TlSz': 'TileSize',\n//     'Ttl ': 'Title',\n//     'T   ': 'To',\n//     'TBl ': 'ToBuiltin',\n//     'ToLk': 'ToLinked',\n//     'TMd ': 'ToMode',\n//     'TglO': 'ToggleOthers',\n//     'Tlrn': 'Tolerance',\n//     'Top ': 'Top',\n//     'TtlL': 'TotalLimit',\n//     'Trck': 'Tracking',\n//     'TrnF': 'TransferFunction',\n//     // 'TrnS': 'TransferSpec',\n//     'Trns': 'Transparency',\n//     // 'TrnG': 'TransparencyGrid',\n//     'TrnC': 'TransparencyGridColors',\n//     // 'TrnG': 'TransparencyGridSize',\n//     'TrnP': 'TransparencyPrefs',\n//     // 'TrnS': 'TransparencyShape',\n//     'TrnI': 'TransparentIndex',\n//     'TrnW': 'TransparentWhites',\n//     'Twst': 'Twist',\n//     'Type': 'Type',\n//     'UC  ': 'UCA',\n//     'URL ': 'URL',\n//     'UndA': 'UndefinedArea',\n//     'Undl': 'Underline',\n//     'UntP': 'UnitsPrefs',\n//     'Untl': 'Untitled',\n//     'UppY': 'UpperY',\n//     'Urgn': 'Urgency',\n//     'AcrS': 'UseAccurateScreens',\n//     'AdPl': 'UseAdditionalPlugins',\n//     'UsCc': 'UseCacheForHistograms',\n//     'UsCr': 'UseCurves',\n//     'UsDf': 'UseDefault',\n//     'uglg': 'UseGlobalAngle',\n//     'UsIC': 'UseICCProfile',\n//     'UsMs': 'UseMask',\n//     'UsrM': 'UserMaskEnabled',\n//     'Usrs': 'UserMaskLinked',\n//     'Usng': 'Using',\n//     'Vl  ': 'Value',\n//     'Vrnc': 'Variance',\n//     'Vct0': 'Vector0',\n//     'Vct1': 'Vector1',\n//     'VctC': 'VectorColor',\n//     'VrsF': 'VersionFix',\n//     'VrsM': 'VersionMajor',\n//     'VrsN': 'VersionMinor',\n//     'Vrtc': 'Vertical',\n//     'VrtS': 'VerticalScale',\n//     'Vdlp': 'VideoAlpha',\n//     'Vsbl': 'Visible',\n//     'WtcS': 'WatchSuspension',\n//     'watr': 'Watermark',\n//     'Wvtp': 'WaveType',\n//     'WLMx': 'WavelengthMax',\n//     'WLMn': 'WavelengthMin',\n//     'WbdP': 'WebdavPrefs',\n//     'Wtdg': 'WetEdges',\n//     'What': 'What',\n//     'WhtC': 'WhiteClip',\n//     'WhtI': 'WhiteIntensity',\n//     'WhHi': 'WhiteIsHigh',\n//     'WhtL': 'WhiteLevel',\n//     'WhtP': 'WhitePoint',\n//     'WhPt': 'WholePath',\n//     'Wdth': 'Width',\n//     'WndM': 'WindMethod',\n//     'With': 'With',\n//     'WrPt': 'WorkPath',\n//     'WrkP': 'WorkPathIndex',\n//     'X   ': 'X',\n//     'Y   ': 'Y',\n//     'Ylw ': 'Yellow',\n//     'ZZTy': 'ZigZagType',\n//     'Alis': '_3DAntiAlias',\n// };\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\n// Specification: https://www.adobe.com/devnet-apps/photoshop/fileformatashtml/\n\nimport {getDataView, getStringFromDataView, getPascalStringFromDataView} from './utils.js';\nimport Types from './types.js';\nimport TagNames from './photoshop-tag-names.js';\n\nexport default {\n    read\n};\n\nconst SIGNATURE = '8BIM';\nconst TAG_ID_SIZE = 2;\nconst RESOURCE_LENGTH_SIZE = 4;\n\nconst SIGNATURE_SIZE = SIGNATURE.length;\n\nfunction read(bytes, includeUnknown) {\n    const dataView = getDataView(new Uint8Array(bytes).buffer);\n    const tags = {};\n    let offset = 0;\n\n    while (offset < bytes.length) {\n        const signature = getStringFromDataView(dataView, offset, SIGNATURE_SIZE);\n        offset += SIGNATURE_SIZE;\n        const tagId = Types.getShortAt(dataView, offset);\n        offset += TAG_ID_SIZE;\n        const {tagName, tagNameSize} = getTagName(dataView, offset);\n        offset += tagNameSize;\n        const resourceSize = Types.getLongAt(dataView, offset);\n        offset += RESOURCE_LENGTH_SIZE;\n        if (signature === SIGNATURE) {\n            const valueDataView = getDataView(dataView.buffer, offset, resourceSize);\n            const tag = {\n                id: tagId,\n                value: getStringFromDataView(valueDataView, 0, resourceSize),\n            };\n            if (TagNames[tagId]) {\n                try {\n                    tag.description = TagNames[tagId].description(valueDataView);\n                } catch (error) {\n                    tag.description = '<no description formatter>';\n                }\n                tags[tagName ? tagName : TagNames[tagId].name] = tag;\n            } else if (includeUnknown) {\n                tags[`undefined-${tagId}`] = tag;\n            }\n        }\n        offset += resourceSize + (resourceSize % 2);\n    }\n\n    return tags;\n}\n\nfunction getTagName(dataView, offset) {\n    // The name is encoded as a Pascal string (the string is prefixed with one\n    // byte containing the length of the string) and everything is padded with a\n    // null byte to make the size even.\n    const [stringSize, string] = getPascalStringFromDataView(dataView, offset);\n    return {\n        tagName: string,\n        tagNameSize: 1 + stringSize + (stringSize % 2 === 0 ? 1 : 0)\n    };\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport {getStringFromDataView} from './utils.js';\n\nexport const iccTags = {\n    'desc': {\n        'name': 'ICC Description',\n    },\n    'cprt': {\n        'name': 'ICC Copyright',\n    },\n    'dmdd': {\n        'name': 'ICC Device Model Description',\n    },\n    'vued': {\n        'name': 'ICC Viewing Conditions Description',\n    },\n    'dmnd': {\n        'name': 'ICC Device Manufacturer for Display',\n    },\n    'tech': {\n        'name': 'Technology',\n    },\n};\n\nexport const iccProfile = {\n    4: {\n        'name': 'Preferred CMM type',\n        'value': (dataView, offset) => getStringFromDataView(dataView, offset, 4),\n        'description': (value) => value !== null ? toCompany(value) : '',\n    },\n    8: {\n        'name': 'Profile Version',\n        'value': (dataView, offset) => {\n            return (dataView.getUint8(offset)).toString(10) + '.'\n            + (dataView.getUint8(offset + 1) >> 4).toString(10) + '.'\n            + (dataView.getUint8(offset + 1) % 16).toString(10);\n        }\n    },\n    12: {\n        'name': 'Profile/Device class',\n        'value': (dataView, offset) => getStringFromDataView(dataView, offset, 4),\n        'description': (value) => {\n            switch (value.toLowerCase()) {\n                case 'scnr': return 'Input Device profile';\n                case 'mntr': return 'Display Device profile';\n                case 'prtr': return 'Output Device profile';\n                case 'link': return 'DeviceLink profile';\n                case 'abst': return 'Abstract profile';\n                case 'spac': return 'ColorSpace profile';\n                case 'nmcl': return 'NamedColor profile';\n                case 'cenc': return 'ColorEncodingSpace profile';\n                case 'mid ': return 'MultiplexIdentification profile';\n                case 'mlnk': return 'MultiplexLink profile';\n                case 'mvis': return 'MultiplexVisualization profile';\n                default: return value;\n            }\n        }\n    },\n    16: {\n        'name': 'Color Space',\n        'value': (dataView, offset) => getStringFromDataView(dataView, offset, 4)\n    },\n    20: {\n        'name': 'Connection Space',\n        'value': (dataView, offset) => getStringFromDataView(dataView, offset, 4)\n    },\n    24: {\n        'name': 'ICC Profile Date',\n        'value': (dataView, offset) => parseDate(dataView, offset).toISOString()\n    },\n    36: {\n        'name': 'ICC Signature',\n        'value': (dataView, offset) => sliceToString(dataView.buffer.slice(offset, offset + 4))\n    },\n    40: {\n        'name': 'Primary Platform',\n        'value': (dataView, offset) => getStringFromDataView(dataView, offset, 4),\n        'description': (value) => toCompany(value)\n    },\n    48: {\n        'name': 'Device Manufacturer',\n        'value': (dataView, offset) => getStringFromDataView(dataView, offset, 4),\n        'description': (value) => toCompany(value)\n    },\n    52: {\n        'name': 'Device Model Number',\n        'value': (dataView, offset) => getStringFromDataView(dataView, offset, 4)\n    },\n    64: {\n        'name': 'Rendering Intent',\n        'value': (dataView, offset) => dataView.getUint32(offset),\n        'description': (value) => {\n            switch (value) {\n                case 0: return 'Perceptual';\n                case 1: return 'Relative Colorimetric';\n                case 2: return 'Saturation';\n                case 3: return 'Absolute Colorimetric';\n                default: return value;\n            }\n        }\n    },\n\n    80: {\n        'name': 'Profile Creator',\n        'value': (dataView, offset) => getStringFromDataView(dataView, offset, 4)\n    },\n};\n\nfunction parseDate(dataView, offset) {\n    const year = dataView.getUint16(offset);\n    const month = dataView.getUint16(offset + 2) - 1;\n    const day = dataView.getUint16(offset + 4);\n    const hours = dataView.getUint16(offset + 6);\n    const minutes = dataView.getUint16(offset + 8);\n    const seconds = dataView.getUint16(offset + 10);\n    return new Date(Date.UTC(year, month, day, hours, minutes, seconds));\n}\n\nfunction sliceToString(slice) {\n    return String.fromCharCode.apply(null, new Uint8Array(slice));\n}\n\nfunction toCompany(value) {\n    switch (value.toLowerCase()) {\n        case 'appl': return 'Apple';\n        case 'adbe': return 'Adobe';\n        case 'msft': return 'Microsoft';\n        case 'sunw': return 'Sun Microsystems';\n        case 'sgi': return 'Silicon Graphics';\n        case 'tgnt': return 'Taligent';\n        default: return value;\n    }\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\nimport {iccTags, iccProfile} from './icc-tag-names.js';\nimport {getStringFromDataView, getUnicodeStringFromDataView, decompress, COMPRESSION_METHOD_NONE, COMPRESSION_METHOD_DEFLATE} from './utils.js';\n\nexport default {\n    read\n};\n\nconst PROFILE_HEADER_LENGTH = 84;\nconst ICC_TAG_COUNT_OFFSET = 128;\nconst ICC_SIGNATURE = 'acsp';\nconst TAG_TYPE_DESC = 'desc';\nconst TAG_TYPE_MULTI_LOCALIZED_UNICODE_TYPE = 'mluc';\nconst TAG_TYPE_TEXT = 'text';\nconst TAG_TYPE_SIGNATURE = 'sig ';\nconst TAG_TABLE_SINGLE_TAG_DATA = 12;\n\n// ICC profile data can be longer than application segment max length of ~64k.\n// so it can be split into multiple APP2 segments. Each segment includes\n// total chunk count and chunk number.\n// Here we read all chunks into single continuous array of bytes.\n// Compressed ICC profile data only has support for a single chunk.\nfunction read(dataView, iccData, async) {\n    if (async && iccData[0].compressionMethod !== COMPRESSION_METHOD_NONE) {\n        return readCompressedIcc(dataView, iccData);\n    }\n\n    return readIcc(dataView, iccData);\n}\n\nfunction readCompressedIcc(dataView, iccData) {\n    if (!compressionMethodIsSupported(iccData[0].compressionMethod)) {\n        return {};\n    }\n    const compressedDataView = new DataView(dataView.buffer.slice(iccData[0].offset, iccData[0].offset + iccData[0].length));\n    return decompress(compressedDataView, iccData[0].compressionMethod, 'utf-8', 'dataview')\n        .then(parseTags)\n        .catch(() => ({}));\n}\n\nfunction compressionMethodIsSupported(compressionMethod) {\n    return compressionMethod === COMPRESSION_METHOD_DEFLATE;\n}\n\nfunction readIcc(dataView, iccData) {\n    try {\n        const totalIccProfileLength = iccData.reduce((sum, icc) => sum + icc.length, 0);\n\n        const iccBinaryData = new Uint8Array(totalIccProfileLength);\n        let offset = 0;\n        const buffer = getBuffer(dataView);\n\n        for (let chunkNumber = 1; chunkNumber <= iccData.length; chunkNumber++) {\n            const iccDataChunk = iccData.find((x) => x.chunkNumber === chunkNumber);\n            if (!iccDataChunk) {\n                throw new Error(`ICC chunk ${chunkNumber} not found`);\n            }\n\n            const data = buffer.slice(iccDataChunk.offset, iccDataChunk.offset + iccDataChunk.length);\n            const chunkData = new Uint8Array(data);\n\n            iccBinaryData.set(chunkData, offset);\n            offset += chunkData.length;\n        }\n\n        return parseTags(new DataView(iccBinaryData.buffer));\n    } catch (error) {\n        return {};\n    }\n}\n\nfunction getBuffer(dataView) {\n    if (Array.isArray(dataView)) {\n        return (new DataView(Uint8Array.from(dataView).buffer)).buffer;\n    }\n    return dataView.buffer;\n}\n\nfunction iccDoesNotHaveTagCount(buffer) {\n    return buffer.length < (ICC_TAG_COUNT_OFFSET + 4);\n}\n\nfunction hasTagsData(buffer, tagHeaderOffset) {\n    return buffer.length < tagHeaderOffset + TAG_TABLE_SINGLE_TAG_DATA;\n}\n\nexport function parseTags(dataView) {\n    const buffer = dataView.buffer;\n\n    const length = dataView.getUint32();\n    if (dataView.byteLength !== length) {\n        throw new Error('ICC profile length not matching');\n    }\n\n    if (dataView.length < PROFILE_HEADER_LENGTH) {\n        throw new Error('ICC profile too short');\n    }\n\n    const tags = {};\n\n    const iccProfileKeys = Object.keys(iccProfile);\n    for (let i = 0; i < iccProfileKeys.length; i++) {\n        const offset = iccProfileKeys[i];\n        const profileEntry = iccProfile[offset];\n        const value = profileEntry.value(dataView, parseInt(offset, 10));\n        let description = value;\n        if (profileEntry.description) {\n            description = profileEntry.description(value);\n        }\n\n        tags[profileEntry.name] = {\n            value,\n            description\n        };\n    }\n\n    const signature = sliceToString(buffer.slice(36, 40));\n    if (signature !== ICC_SIGNATURE) {\n        throw new Error('ICC profile: missing signature');\n    }\n\n    /* ICC data is incomplete but we have header parsed so lets return it */\n    if (iccDoesNotHaveTagCount(buffer)) {\n        return tags;\n    }\n\n    const tagCount = dataView.getUint32(128);\n    let tagHeaderOffset = 132;\n\n    for (let i = 0; i < tagCount; i++) {\n        if (hasTagsData(buffer, tagHeaderOffset)) {\n            // Tags are corrupted (offset too far), return what we parsed until now\n            return tags;\n        }\n        const tagSignature = getStringFromDataView(dataView, tagHeaderOffset, 4);\n        const tagOffset = dataView.getUint32(tagHeaderOffset + 4);\n        const tagSize = dataView.getUint32(tagHeaderOffset + 8);\n\n        if (tagOffset > buffer.length) {\n            // Tag data is invalid, lets return what we managed to parse\n            return tags;\n        }\n        const tagType = getStringFromDataView(dataView, tagOffset, 4);\n\n        if (tagType === TAG_TYPE_DESC) {\n            const tagValueSize = dataView.getUint32(tagOffset + 8);\n            if (tagValueSize > tagSize) {\n                // Tag data is invalid, lets return what we managed to parse\n                return tags;\n            }\n\n            const val = sliceToString(buffer.slice(tagOffset + 12, tagOffset + tagValueSize + 11));\n            addTag(tags, tagSignature, val);\n        } else if (tagType === TAG_TYPE_MULTI_LOCALIZED_UNICODE_TYPE) {\n            const numRecords = dataView.getUint32(tagOffset + 8);\n            const recordSize = dataView.getUint32(tagOffset + 12);\n            let offset = tagOffset + 16;\n            const val = [];\n            for (let recordNum = 0; recordNum < numRecords; recordNum++) {\n                const languageCode = getStringFromDataView(dataView, offset + 0, 2);\n                const countryCode = getStringFromDataView(dataView, offset + 2, 2);\n                const textLength = dataView.getUint32(offset + 4);\n                const textOffset = dataView.getUint32(offset + 8);\n\n                const text = getUnicodeStringFromDataView(dataView, tagOffset + textOffset, textLength);\n                val.push({languageCode, countryCode, text});\n                offset += recordSize;\n            }\n            if (numRecords === 1) {\n                addTag(tags, tagSignature, val[0].text);\n            } else {\n                const valObj = {};\n                for (let valIndex = 0; valIndex < val.length; valIndex++) {\n                    valObj[`${val[valIndex].languageCode}-${val[valIndex].countryCode}`] = val[valIndex].text;\n                }\n                addTag(tags, tagSignature, valObj);\n            }\n        } else if (tagType === TAG_TYPE_TEXT) {\n            const val = sliceToString(buffer.slice(tagOffset + 8, tagOffset + tagSize - 7));\n            addTag(tags, tagSignature, val);\n        } else if (tagType === TAG_TYPE_SIGNATURE) {\n            const val = sliceToString(buffer.slice(tagOffset + 8, tagOffset + 12));\n            addTag(tags, tagSignature, val);\n        }\n        tagHeaderOffset = tagHeaderOffset + 12;\n    }\n\n    return tags;\n}\n\nfunction sliceToString(slice) {\n    return String.fromCharCode.apply(null, new Uint8Array(slice));\n}\n\nfunction addTag(tags, tagSignature, value) {\n    if (iccTags[tagSignature]) {\n        tags[iccTags[tagSignature].name] = {value, description: value};\n    } else {\n        tags[tagSignature] = {value, description: value};\n    }\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\n// Reverse-engineered docs:\n// https://exiv2.org/tags-canon.html\n// https://gist.github.com/redaktor/bae0ef2377ab70bc5276\n// https://www.ozhiker.com/electronics/pjmt/jpeg_info/canon_mn.html\n\nimport {objectAssign} from './utils.js';\nimport {readIfd} from './tags-helpers.js';\nimport {IFD_TYPE_CANON} from './tag-names.js';\n\nconst SHOT_INFO_AUTO_ROTATE = 27; // First position is size.\n\nexport default {\n    read,\n    SHOT_INFO_AUTO_ROTATE\n};\n\nfunction read(dataView, tiffHeaderOffset, offset, byteOrder, includeUnknown) {\n    let tags = readIfd(dataView, IFD_TYPE_CANON, tiffHeaderOffset, tiffHeaderOffset + offset, byteOrder, includeUnknown);\n\n    if (tags['ShotInfo']) {\n        tags = objectAssign({}, tags, parseShotInfo(tags['ShotInfo'].value));\n        delete tags['ShotInfo'];\n    }\n\n    return tags;\n}\n\nfunction parseShotInfo(shotInfoData) {\n    const tags = {};\n\n    if (shotInfoData[SHOT_INFO_AUTO_ROTATE] !== undefined) {\n        tags['AutoRotate'] = {\n            value: shotInfoData[SHOT_INFO_AUTO_ROTATE],\n            description: getAutoRotateDescription(shotInfoData[SHOT_INFO_AUTO_ROTATE])\n        };\n    }\n\n    return tags;\n}\n\nfunction getAutoRotateDescription(autoRotate) {\n    if (autoRotate === 0) {\n        return 'None';\n    }\n    if (autoRotate === 1) {\n        return 'Rotate 90 CW';\n    }\n    if (autoRotate === 2) {\n        return 'Rotate 180';\n    }\n    if (autoRotate === 3) {\n        return 'Rotate 270 CW';\n    }\n    return 'Unknown';\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport Types from './types.js';\n\nexport default {\n    read\n};\n\nfunction read(dataView, fileDataOffset) {\n    return {\n        'Image Width': getImageWidth(dataView, fileDataOffset),\n        'Image Height': getImageHeight(dataView, fileDataOffset),\n        'Bit Depth': getBitDepth(dataView, fileDataOffset),\n        'Color Type': getColorType(dataView, fileDataOffset),\n        'Compression': getCompression(dataView, fileDataOffset),\n        'Filter': getFilter(dataView, fileDataOffset),\n        'Interlace': getInterlace(dataView, fileDataOffset)\n    };\n}\n\nfunction getImageWidth(dataView, fileDataOffset) {\n    const OFFSET = 0;\n    const SIZE = 4;\n\n    if (fileDataOffset + OFFSET + SIZE > dataView.byteLength) {\n        return undefined;\n    }\n\n    const value = Types.getLongAt(dataView, fileDataOffset);\n    return {\n        value,\n        description: `${value}px`\n    };\n}\n\nfunction getImageHeight(dataView, fileDataOffset) {\n    const OFFSET = 4;\n    const SIZE = 4;\n\n    if (fileDataOffset + OFFSET + SIZE > dataView.byteLength) {\n        return undefined;\n    }\n\n    const value = Types.getLongAt(dataView, fileDataOffset + OFFSET);\n    return {\n        value,\n        description: `${value}px`\n    };\n}\n\nfunction getBitDepth(dataView, fileDataOffset) {\n    const OFFSET = 8;\n    const SIZE = 1;\n\n    if (fileDataOffset + OFFSET + SIZE > dataView.byteLength) {\n        return undefined;\n    }\n\n    const value = Types.getByteAt(dataView, fileDataOffset + OFFSET);\n    return {\n        value,\n        description: `${value}`\n    };\n}\n\nfunction getColorType(dataView, fileDataOffset) {\n    const OFFSET = 9;\n    const SIZE = 1;\n    const COLOR_TYPES = {\n        0: 'Grayscale',\n        2: 'RGB',\n        3: 'Palette',\n        4: 'Grayscale with Alpha',\n        6: 'RGB with Alpha'\n    };\n\n    if (fileDataOffset + OFFSET + SIZE > dataView.byteLength) {\n        return undefined;\n    }\n\n    const value = Types.getByteAt(dataView, fileDataOffset + OFFSET);\n    return {\n        value,\n        description: COLOR_TYPES[value] || 'Unknown'\n    };\n}\n\nfunction getCompression(dataView, fileDataOffset) {\n    const OFFSET = 10;\n    const SIZE = 1;\n\n    if (fileDataOffset + OFFSET + SIZE > dataView.byteLength) {\n        return undefined;\n    }\n\n    const value = Types.getByteAt(dataView, fileDataOffset + OFFSET);\n    return {\n        value,\n        description: value === 0 ? 'Deflate/Inflate' : 'Unknown'\n    };\n}\n\nfunction getFilter(dataView, fileDataOffset) {\n    const OFFSET = 11;\n    const SIZE = 1;\n\n    if (fileDataOffset + OFFSET + SIZE > dataView.byteLength) {\n        return undefined;\n    }\n\n    const value = Types.getByteAt(dataView, fileDataOffset + OFFSET);\n    return {\n        value,\n        description: value === 0 ? 'Adaptive' : 'Unknown'\n    };\n}\n\nfunction getInterlace(dataView, fileDataOffset) {\n    const OFFSET = 12;\n    const SIZE = 1;\n    const INTERLACE_TYPES = {\n        0: 'Noninterlaced',\n        1: 'Adam7 Interlace'\n    };\n\n    if (fileDataOffset + OFFSET + SIZE > dataView.byteLength) {\n        return undefined;\n    }\n\n    const value = Types.getByteAt(dataView, fileDataOffset + OFFSET);\n    return {\n        value,\n        description: INTERLACE_TYPES[value] || 'Unknown'\n    };\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\n// Specification: http://www.libpng.org/pub/png/spec/1.2/\n\nimport {getStringValueFromArray, getStringFromDataView, decompress, COMPRESSION_METHOD_NONE} from './utils.js';\nimport TagDecoder from './tag-decoder.js';\nimport {TYPE_TEXT, TYPE_ITXT, TYPE_ZTXT} from './image-header-png.js';\nimport Tags from './tags.js';\nimport IptcTags from './iptc-tags.js';\nimport Constants from './constants.js';\n\nexport default {\n    read\n};\n\nconst STATE_KEYWORD = 'STATE_KEYWORD';\nconst STATE_COMPRESSION = 'STATE_COMPRESSION';\nconst STATE_LANG = 'STATE_LANG';\nconst STATE_TRANSLATED_KEYWORD = 'STATE_TRANSLATED_KEYWORD';\nconst STATE_TEXT = 'STATE_TEXT';\nconst COMPRESSION_SECTION_ITXT_EXTRA_BYTE = 1;\nconst COMPRESSION_FLAG_COMPRESSED = 1;\nconst EXIF_OFFSET = 6;\n\nfunction read(dataView, pngTextChunks, async, includeUnknown) {\n    const tags = {};\n    const tagsPromises = [];\n    for (let i = 0; i < pngTextChunks.length; i++) {\n        const {offset, length, type} = pngTextChunks[i];\n        const nameAndValue = getNameAndValue(dataView, offset, length, type, async);\n        if (nameAndValue instanceof Promise) {\n            tagsPromises.push(nameAndValue.then(({name, value, description}) => {\n                try {\n                    if (Constants.USE_EXIF && isExifGroupTag(name, value)) {\n                        return {\n                            __exif: Tags.read(decodeRawData(value), EXIF_OFFSET, includeUnknown).tags\n                        };\n                    } else if (Constants.USE_IPTC && isIptcGroupTag(name, value)) {\n                        return {\n                            __iptc: IptcTags.read(decodeRawData(value), 0, includeUnknown)\n                        };\n                    } else if (name && !isExifGroupTag(name, value) && !isIptcGroupTag(name, value)) {\n                        return {\n                            [name]: {\n                                value,\n                                description\n                            }\n                        };\n                    }\n                } catch (error) {\n                    // Ignore the broken tag.\n                }\n                return {};\n            }));\n        } else {\n            const {name, value, description} = nameAndValue;\n            if (name) {\n                tags[name] = {\n                    value,\n                    description\n                };\n            }\n        }\n    }\n\n    return {\n        readTags: tags,\n        readTagsPromise: tagsPromises.length > 0 ? Promise.all(tagsPromises) : undefined\n    };\n}\n\nfunction getNameAndValue(dataView, offset, length, type, async) {\n    const keywordChars = [];\n    const langChars = [];\n    const translatedKeywordChars = [];\n    let valueChars;\n    let parsingState = STATE_KEYWORD;\n    let compressionMethod = COMPRESSION_METHOD_NONE;\n\n    for (let i = 0; i < length && offset + i < dataView.byteLength; i++) {\n        if (parsingState === STATE_COMPRESSION) {\n            compressionMethod = getCompressionMethod({type, dataView, offset: offset + i});\n            if (type === TYPE_ITXT) {\n                i += COMPRESSION_SECTION_ITXT_EXTRA_BYTE;\n            }\n            parsingState = moveToNextState(type, parsingState);\n            continue;\n        } else if (parsingState === STATE_TEXT) {\n            valueChars = new DataView(dataView.buffer.slice(offset + i, offset + length));\n            break;\n        }\n        const byte = dataView.getUint8(offset + i);\n        if (byte === 0) {\n            parsingState = moveToNextState(type, parsingState);\n        } else if (parsingState === STATE_KEYWORD) {\n            keywordChars.push(byte);\n        } else if (parsingState === STATE_LANG) {\n            langChars.push(byte);\n        } else if (parsingState === STATE_TRANSLATED_KEYWORD) {\n            translatedKeywordChars.push(byte);\n        }\n    }\n\n    if (compressionMethod !== COMPRESSION_METHOD_NONE && !async) {\n        return {};\n    }\n    const decompressedValueChars = decompress(valueChars, compressionMethod, getEncodingFromType(type));\n    if (decompressedValueChars instanceof Promise) {\n        return decompressedValueChars\n            .then((_decompressedValueChars) => constructTag(_decompressedValueChars, type, langChars, keywordChars))\n            .catch(() => constructTag('<text using unknown compression>'.split(''), type, langChars, keywordChars));\n    }\n    return constructTag(decompressedValueChars, type, langChars, keywordChars);\n}\n\nfunction getCompressionMethod({type, dataView, offset}) {\n    if (type === TYPE_ITXT) {\n        if (dataView.getUint8(offset) === COMPRESSION_FLAG_COMPRESSED) {\n            return dataView.getUint8(offset + 1);\n        }\n    } else if (type === TYPE_ZTXT) {\n        return dataView.getUint8(offset);\n    }\n    return COMPRESSION_METHOD_NONE;\n}\n\nfunction moveToNextState(type, parsingState) {\n    if (parsingState === STATE_KEYWORD && [TYPE_ITXT, TYPE_ZTXT].includes(type)) {\n        return STATE_COMPRESSION;\n    }\n    if (parsingState === STATE_COMPRESSION) {\n        if (type === TYPE_ITXT) {\n            return STATE_LANG;\n        }\n        return STATE_TEXT;\n    }\n    if (parsingState === STATE_LANG) {\n        return STATE_TRANSLATED_KEYWORD;\n    }\n    return STATE_TEXT;\n}\n\nfunction getEncodingFromType(type) {\n    if (type === TYPE_TEXT || type === TYPE_ZTXT) {\n        return 'latin1';\n    }\n    return 'utf-8';\n}\n\nfunction constructTag(valueChars, type, langChars, keywordChars) {\n    const value = getValue(valueChars);\n    return {\n        name: getName(type, langChars, keywordChars),\n        value,\n        description: type === TYPE_ITXT ? getDescription(valueChars) : value\n    };\n}\n\nfunction getName(type, langChars, keywordChars) {\n    const name = getStringValueFromArray(keywordChars);\n    if (type === TYPE_TEXT || langChars.length === 0) {\n        return name;\n    }\n    const lang = getStringValueFromArray(langChars);\n    return `${name} (${lang})`;\n}\n\nfunction getValue(valueChars) {\n    if (valueChars instanceof DataView) {\n        return getStringFromDataView(valueChars, 0, valueChars.byteLength);\n    }\n    return valueChars;\n}\n\nfunction getDescription(valueChars) {\n    return TagDecoder.decode('UTF-8', valueChars);\n}\n\nfunction isExifGroupTag(name, value) {\n    return name.toLowerCase() === 'raw profile type exif' && value.substring(1, 5) === 'exif';\n}\n\nfunction isIptcGroupTag(name, value) {\n    return name.toLowerCase() === 'raw profile type iptc' && value.substring(1, 5) === 'iptc';\n}\n\nfunction decodeRawData(value) {\n    const parts = value.match(/\\n(exif|iptc)\\n\\s*\\d+\\n([\\s\\S]*)$/);\n    return hexToDataView(parts[2].replace(/\\n/g, ''));\n}\n\nfunction hexToDataView(hex) {\n    const dataView = new DataView(new ArrayBuffer(hex.length / 2));\n    for (let i = 0; i < hex.length; i += 2) {\n        dataView.setUint8(i / 2, parseInt(hex.substring(i, i + 2), 16));\n    }\n    return dataView;\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport Types from './types.js';\nimport {PNG_CHUNK_LENGTH_OFFSET, PNG_CHUNK_TYPE_OFFSET, PNG_CHUNK_DATA_OFFSET, PNG_CHUNK_TYPE_SIZE, TYPE_PHYS, TYPE_TIME} from './image-header-png.js';\nimport {getStringFromDataView} from './utils.js';\n\nexport default {\n    read\n};\n\nfunction read(dataView, chunkOffsets) {\n    const tags = {};\n\n    for (let i = 0; i < chunkOffsets.length; i++) {\n        const chunkLength = Types.getLongAt(dataView, chunkOffsets[i] + PNG_CHUNK_LENGTH_OFFSET);\n        const chunkType = getStringFromDataView(dataView, chunkOffsets[i] + PNG_CHUNK_TYPE_OFFSET, PNG_CHUNK_TYPE_SIZE);\n\n        if (chunkType === TYPE_PHYS) {\n            tags['Pixels Per Unit X'] = getPixelsPerUnitX(dataView, chunkOffsets[i], chunkLength);\n            tags['Pixels Per Unit Y'] = getPixelsPerUnitY(dataView, chunkOffsets[i], chunkLength);\n            tags['Pixel Units'] = getPixelUnits(dataView, chunkOffsets[i], chunkLength);\n        } else if (chunkType === TYPE_TIME) {\n            tags['Modify Date'] = getModifyDate(dataView, chunkOffsets[i], chunkLength);\n        }\n    }\n\n    return tags;\n}\n\nfunction getPixelsPerUnitX(dataView, chunkOffset, chunkLength) {\n    const TAG_OFFSET = 0;\n    const TAG_SIZE = 4;\n\n    if (!tagFitsInBuffer(dataView, chunkOffset, chunkLength, TAG_OFFSET, TAG_SIZE)) {\n        return undefined;\n    }\n\n    const value = Types.getLongAt(dataView, chunkOffset + PNG_CHUNK_DATA_OFFSET + TAG_OFFSET);\n\n    return {\n        value,\n        description: '' + value\n    };\n}\n\nfunction getPixelsPerUnitY(dataView, chunkOffset, chunkLength) {\n    const TAG_OFFSET = 4;\n    const TAG_SIZE = 4;\n\n    if (!tagFitsInBuffer(dataView, chunkOffset, chunkLength, TAG_OFFSET, TAG_SIZE)) {\n        return undefined;\n    }\n\n    const value = Types.getLongAt(dataView, chunkOffset + PNG_CHUNK_DATA_OFFSET + TAG_OFFSET);\n\n    return {\n        value,\n        description: '' + value\n    };\n}\n\nfunction getPixelUnits(dataView, chunkOffset, chunkLength) {\n    const TAG_OFFSET = 8;\n    const TAG_SIZE = 1;\n\n    if (!tagFitsInBuffer(dataView, chunkOffset, chunkLength, TAG_OFFSET, TAG_SIZE)) {\n        return undefined;\n    }\n\n    const value = Types.getByteAt(dataView, chunkOffset + PNG_CHUNK_DATA_OFFSET + TAG_OFFSET);\n\n    return {\n        value,\n        description: value === 1 ? 'meters' : 'Unknown'\n    };\n}\n\nfunction getModifyDate(dataView, chunkOffset, chunkLength) {\n    const TIME_TAG_SIZE = 7;\n\n    if (!tagFitsInBuffer(dataView, chunkOffset, chunkLength, 0, TIME_TAG_SIZE)) {\n        return undefined;\n    }\n\n    const year = Types.getShortAt(dataView, chunkOffset + PNG_CHUNK_DATA_OFFSET);\n    const month = Types.getByteAt(dataView, chunkOffset + PNG_CHUNK_DATA_OFFSET + 2);\n    const day = Types.getByteAt(dataView, chunkOffset + PNG_CHUNK_DATA_OFFSET + 3);\n    const hours = Types.getByteAt(dataView, chunkOffset + PNG_CHUNK_DATA_OFFSET + 4);\n    const minutes = Types.getByteAt(dataView, chunkOffset + PNG_CHUNK_DATA_OFFSET + 5);\n    const seconds = Types.getByteAt(dataView, chunkOffset + PNG_CHUNK_DATA_OFFSET + 6);\n\n    return {\n        value: [year, month, day, hours, minutes, seconds],\n        description: `${pad(year, 4)}-${pad(month, 2)}-${pad(day, 2)} ${pad(hours, 2)}:${pad(minutes, 2)}:${pad(seconds, 2)}`\n    };\n}\n\nfunction tagFitsInBuffer(dataView, chunkOffset, chunkLength, tagOffset, tagSize) {\n    return tagOffset + tagSize <= chunkLength && chunkOffset + PNG_CHUNK_DATA_OFFSET + tagOffset + tagSize <= dataView.byteLength;\n}\n\nfunction pad(number, size) {\n    return `${'0'.repeat(size - ('' + number).length)}${number}`;\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport Types from './types.js';\n\nexport default {\n    read\n};\n\nconst IMAGE_WIDTH_OFFSET = 4;\nconst IMAGE_HEIGHT_OFFSET = 7;\n\n// https://developers.google.com/speed/webp/docs/riff_container#extended_file_format\nfunction read(dataView, chunkOffset) {\n    const tags = {};\n\n    const flags = Types.getByteAt(dataView, chunkOffset);\n\n    tags['Alpha'] = getAlpha(flags);\n    tags['Animation'] = getAnimation(flags);\n    tags['ImageWidth'] = getThreeByteValue(dataView, chunkOffset + IMAGE_WIDTH_OFFSET);\n    tags['ImageHeight'] = getThreeByteValue(dataView, chunkOffset + IMAGE_HEIGHT_OFFSET);\n\n    return tags;\n}\n\nfunction getAlpha(flags) {\n    const value = flags & 0x10;\n    return {\n        value: value ? 1 : 0,\n        description: value ? 'Yes' : 'No'\n    };\n}\n\nfunction getAnimation(flags) {\n    const value = flags & 0x02;\n    return {\n        value: value ? 1 : 0,\n        description: value ? 'Yes' : 'No'\n    };\n}\n\nfunction getThreeByteValue(dataView, offset) {\n    // Values are stored little-endian.\n    const value = Types.getByteAt(dataView, offset)\n        + 256 * Types.getByteAt(dataView, offset + 1)\n        + 256 * 256 * Types.getByteAt(dataView, offset + 2)\n        + 1; // Value is 1-based, i.e. a value of 7 means 8px.\n\n    return {\n        value,\n        description: value + 'px'\n    };\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\n// https://www.w3.org/Graphics/GIF/spec-gif87.txt\n// https://www.w3.org/Graphics/GIF/spec-gif89a.txt\n\nimport {getStringFromDataView} from './utils.js';\n\nexport default {\n    read\n};\n\nfunction read(dataView) {\n    return {\n        'GIF Version': getGifVersion(dataView),\n        'Image Width': getImageWidth(dataView),\n        'Image Height': getImageHeight(dataView),\n        'Global Color Map': getGlobalColorMap(dataView),\n        'Bits Per Pixel': getBitDepth(dataView),\n        'Color Resolution Depth': getColorResolution(dataView)\n    };\n}\n\nfunction getGifVersion(dataView) {\n    const OFFSET = 3;\n    const SIZE = 3;\n\n    if (OFFSET + SIZE > dataView.byteLength) {\n        return undefined;\n    }\n\n    const value = getStringFromDataView(dataView, OFFSET, SIZE);\n    return {\n        value,\n        description: value\n    };\n}\n\nfunction getImageWidth(dataView) {\n    const OFFSET = 6;\n    const SIZE = 2;\n\n    if (OFFSET + SIZE > dataView.byteLength) {\n        return undefined;\n    }\n\n    const value = dataView.getUint16(OFFSET, true);\n    return {\n        value,\n        description: `${value}px`\n    };\n}\n\nfunction getImageHeight(dataView) {\n    const OFFSET = 8;\n    const SIZE = 2;\n\n    if (OFFSET + SIZE > dataView.byteLength) {\n        return undefined;\n    }\n\n    const value = dataView.getUint16(OFFSET, true);\n    return {\n        value,\n        description: `${value}px`\n    };\n}\n\nfunction getGlobalColorMap(dataView) {\n    const OFFSET = 10;\n    const SIZE = 1;\n\n    if (OFFSET + SIZE > dataView.byteLength) {\n        return undefined;\n    }\n\n    const byteValue = dataView.getUint8(OFFSET);\n    const value = (byteValue & 0b10000000) >>> 7;\n    return {\n        value,\n        description: value === 1 ? 'Yes' : 'No'\n    };\n}\n\nfunction getColorResolution(dataView) {\n    const OFFSET = 10;\n    const SIZE = 1;\n\n    if (OFFSET + SIZE > dataView.byteLength) {\n        return undefined;\n    }\n\n    const byteValue = dataView.getUint8(OFFSET);\n    const value = ((byteValue & 0b01110000) >>> 4) + 1; // Zero-based.\n    return {\n        value,\n        description: `${value} ${value === 1 ? 'bit' : 'bits'}`\n    };\n}\n\nfunction getBitDepth(dataView) {\n    const OFFSET = 10;\n    const SIZE = 1;\n\n    if (OFFSET + SIZE > dataView.byteLength) {\n        return undefined;\n    }\n\n    const byteValue = dataView.getUint8(OFFSET);\n    const value = (byteValue & 0b00000111) + 1; // Zero-based.\n    return {\n        value,\n        description: `${value} ${value === 1 ? 'bit' : 'bits'}`\n    };\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\nimport {deferInit, getBase64Image} from './utils.js';\n\n// https://exiftool.org/TagNames/EXIF.html#Compression\nconst COMPRESSION_JPEG = [6, 7, 99];\n\nexport default {\n    get,\n};\n\nfunction get(dataView, thumbnailTags, tiffHeaderOffset) {\n    if (hasJpegThumbnail(thumbnailTags)) {\n        thumbnailTags.type = 'image/jpeg';\n        const offset = tiffHeaderOffset + thumbnailTags.JPEGInterchangeFormat.value;\n        thumbnailTags.image = dataView.buffer.slice(offset, offset + thumbnailTags.JPEGInterchangeFormatLength.value);\n        deferInit(thumbnailTags, 'base64', function () {\n            return getBase64Image(this.image);\n        });\n    }\n\n    // There is a small possibility of thumbnails in TIFF format but they are\n    // not stored as a self-contained image file and would be much more\n    // difficult to extract.\n    // https://exiftool.org/forum/index.php?topic=3273.msg14778#msg14778\n\n    return thumbnailTags;\n}\n\nfunction hasJpegThumbnail(tags) {\n    return tags && ((tags.Compression === undefined) || (COMPRESSION_JPEG.includes(tags.Compression.value)))\n        && tags.JPEGInterchangeFormat && tags.JPEGInterchangeFormat.value\n        && tags.JPEGInterchangeFormatLength && tags.JPEGInterchangeFormatLength.value;\n}\n", "/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/. */\n\n/**\n * Thrown when no Exif metadata was found for the given image.\n *\n * @param {string} message The error message.\n */\nfunction MetadataMissingError(message) {\n    this.name = 'MetadataMissingError';\n    this.message = message || 'No Exif data';\n    this.stack = (new Error()).stack;\n}\n\nMetadataMissingError.prototype = new Error;\n\nexport default {\n    MetadataMissingError,\n};\n", "/**\n * ExifReader\n * http://github.com/mattiasw/exifreader\n * Copyright (C) 2011-2023  <PERSON><PERSON> <<EMAIL>>\n * This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at https://mozilla.org/MPL/2.0/.\n */\n/* global Buffer, __non_webpack_require__ */\n\nimport {objectAssign, dataUriToBuffer} from './utils.js';\nimport DataViewWrapper from './dataview.js';\nimport Constants from './constants.js';\nimport {getStringValueFromArray} from './utils.js';\nimport {getCalculatedGpsValue} from './tag-names-utils.js';\nimport ImageHeader from './image-header.js';\nimport Tags from './tags.js';\nimport MpfTags from './mpf-tags.js';\nimport FileTags from './file-tags.js';\nimport JfifTags from './jfif-tags.js';\nimport IptcTags from './iptc-tags.js';\nimport XmpTags from './xmp-tags.js';\nimport PhotoshopTags from './photoshop-tags.js';\nimport IccTags from './icc-tags.js';\nimport CanonTags from './canon-tags.js';\nimport PngFileTags from './png-file-tags.js';\nimport PngTextTags from './png-text-tags.js';\nimport PngTags from './png-tags.js';\nimport Vp8xTags from './vp8x-tags.js';\nimport GifFileTags from './gif-file-tags.js';\nimport Thumbnail from './thumbnail.js';\nimport exifErrors from './errors.js';\n\nexport default {\n    load,\n    loadView,\n    errors: exifErrors,\n};\n\nexport const errors = exifErrors;\n\nexport function load(data, options = {}) {\n    if (isFilePathOrURL(data)) {\n        options.async = true;\n        return loadFile(data, options).then((fileContents) => loadFromData(fileContents, options));\n    }\n    if (isBrowserFileObject(data)) {\n        options.async = true;\n        return loadFileObject(data).then((fileContents) => loadFromData(fileContents, options));\n    }\n    return loadFromData(data, options);\n}\n\nfunction isFilePathOrURL(data) {\n    return typeof data === 'string';\n}\n\nfunction loadFile(filename, options) {\n    if (/^\\w+:\\/\\//.test(filename)) {\n        if (typeof fetch !== 'undefined') {\n            return fetchRemoteFile(filename, options);\n        }\n\n        return nodeGetRemoteFile(filename, options);\n    }\n\n    if (isDataUri(filename)) {\n        return Promise.resolve(dataUriToBuffer(filename));\n    }\n\n    return loadLocalFile(filename, options);\n}\n\nfunction fetchRemoteFile(url, {length} = {}) {\n    const options = {method: 'GET'};\n    if (Number.isInteger(length) && length >= 0) {\n        options.headers = {\n            range: `bytes=0-${length - 1}`,\n        };\n    }\n    return fetch(url, options).then((response) => response.arrayBuffer());\n}\n\nfunction nodeGetRemoteFile(url, {length} = {}) {\n    return new Promise((resolve, reject) => {\n        const options = {};\n        if (Number.isInteger(length) && length >= 0) {\n            options.headers = {\n                range: `bytes=0-${length - 1}`,\n            };\n        }\n\n        const get = requireNodeGet(url);\n        get(url, options, (response) => {\n            if ((response.statusCode >= 200) && (response.statusCode <= 299)) {\n                const data = [];\n                response.on('data', (chunk) => data.push(Buffer.from(chunk)));\n                response.on('error', (error) => reject(error));\n                response.on('end', () => resolve(Buffer.concat(data)));\n            } else {\n                reject(`Could not fetch file: ${response.statusCode} ${response.statusMessage}`);\n                response.resume();\n            }\n        }).on('error', (error) => reject(error));\n    });\n}\n\nfunction requireNodeGet(url) {\n    if (/^https:\\/\\//.test(url)) {\n        return __non_webpack_require__('https').get;\n    }\n    return __non_webpack_require__('http').get;\n}\n\nfunction isDataUri(filename) {\n    return /^data:[^;,]*(;base64)?,/.test(filename);\n}\n\nfunction loadLocalFile(filename, {length} = {}) {\n    return new Promise((resolve, reject) => {\n        const fs = requireNodeFs();\n        fs.open(filename, (error, fd) => {\n            if (error) {\n                reject(error);\n            } else {\n                fs.stat(filename, (error, stat) => {\n                    if (error) {\n                        reject(error);\n                    } else {\n                        const size = Math.min(stat.size, length !== undefined ? length : stat.size);\n                        const buffer = Buffer.alloc(size);\n                        const options = {\n                            buffer,\n                            length: size\n                        };\n                        fs.read(fd, options, (error) => {\n                            if (error) {\n                                reject(error);\n                            } else {\n                                fs.close(fd, (error) => {\n                                    if (error) {\n                                        console.warn(`Could not close file ${filename}:`, error); // eslint-disable-line no-console\n                                    }\n                                    resolve(buffer);\n                                });\n                            }\n                        });\n                    }\n                });\n            }\n        });\n    });\n}\n\nfunction requireNodeFs() {\n    try {\n        return __non_webpack_require__('fs');\n    } catch (error) {\n        return undefined;\n    }\n}\n\nfunction isBrowserFileObject(data) {\n    return (typeof window !== 'undefined') && (typeof File !== 'undefined') && (data instanceof File);\n}\n\nfunction loadFileObject(file) {\n    return new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onload = (readerEvent) => resolve(readerEvent.target.result);\n        reader.onerror = () => reject(reader.error);\n        reader.readAsArrayBuffer(file);\n    });\n}\n\nfunction loadFromData(data, options) {\n    if (isNodeBuffer(data)) {\n        // File data read in Node can share the underlying buffer with other\n        // data. Therefore it's safest to get a new one to avoid weird bugs.\n        data = (new Uint8Array(data)).buffer;\n    }\n    return loadView(getDataView(data), options);\n}\n\nfunction isNodeBuffer(data) {\n    try {\n        return Buffer.isBuffer(data);\n    } catch (error) {\n        return false;\n    }\n}\n\nfunction getDataView(data) {\n    try {\n        return new DataView(data);\n    } catch (error) {\n        return new DataViewWrapper(data);\n    }\n}\n\nexport function loadView(\n    dataView,\n    {expanded = false, async = false, includeUnknown = false} = {expanded: false, async: false, includeUnknown: false}\n) {\n    let foundMetaData = false;\n    let tags = {};\n    const tagsPromises = [];\n\n    const {\n        fileType,\n        fileDataOffset,\n        jfifDataOffset,\n        tiffHeaderOffset,\n        iptcDataOffset,\n        xmpChunks,\n        iccChunks,\n        mpfDataOffset,\n        pngHeaderOffset,\n        pngTextChunks,\n        pngChunkOffsets,\n        vp8xChunkOffset,\n        gifHeaderOffset\n    } = ImageHeader.parseAppMarkers(dataView, async);\n\n    if (Constants.USE_JPEG && Constants.USE_FILE && hasFileData(fileDataOffset)) {\n        foundMetaData = true;\n        const readTags = FileTags.read(dataView, fileDataOffset);\n        if (expanded) {\n            tags.file = readTags;\n        } else {\n            tags = objectAssign({}, tags, readTags);\n        }\n    }\n\n    if (Constants.USE_JPEG && Constants.USE_JFIF && hasJfifData(jfifDataOffset)) {\n        foundMetaData = true;\n        const readTags = JfifTags.read(dataView, jfifDataOffset);\n        if (expanded) {\n            tags.jfif = readTags;\n        } else {\n            tags = objectAssign({}, tags, readTags);\n        }\n    }\n\n    if (Constants.USE_EXIF && hasExifData(tiffHeaderOffset)) {\n        foundMetaData = true;\n        const {tags: readTags, byteOrder} = Tags.read(dataView, tiffHeaderOffset, includeUnknown);\n        if (readTags.Thumbnail) {\n            tags.Thumbnail = readTags.Thumbnail;\n            delete readTags.Thumbnail;\n        }\n\n        if (expanded) {\n            tags.exif = readTags;\n            addGpsGroup(tags);\n        } else {\n            tags = objectAssign({}, tags, readTags);\n        }\n\n        if (Constants.USE_TIFF && Constants.USE_IPTC && readTags['IPTC-NAA'] && !hasIptcData(iptcDataOffset)) {\n            const readIptcTags = IptcTags.read(readTags['IPTC-NAA'].value, 0, includeUnknown);\n            if (expanded) {\n                tags.iptc = readIptcTags;\n            } else {\n                tags = objectAssign({}, tags, readIptcTags);\n            }\n        }\n\n        if (Constants.USE_TIFF && Constants.USE_XMP && readTags['ApplicationNotes'] && !hasXmpData(xmpChunks)) {\n            const readXmpTags = XmpTags.read(getStringValueFromArray(readTags['ApplicationNotes'].value));\n            if (expanded) {\n                tags.xmp = readXmpTags;\n            } else {\n                delete readXmpTags._raw;\n                tags = objectAssign({}, tags, readXmpTags);\n            }\n        }\n\n        if (Constants.USE_PHOTOSHOP && readTags['ImageSourceData']) {\n            const readPhotoshopTags = PhotoshopTags.read(readTags['PhotoshopSettings'].value, includeUnknown);\n            if (expanded) {\n                tags.photoshop = readPhotoshopTags;\n            } else {\n                tags = objectAssign({}, tags, readPhotoshopTags);\n            }\n        }\n\n        if (Constants.USE_TIFF && Constants.USE_ICC && readTags['ICC_Profile'] && !hasIccData(iccChunks)) {\n            const readIccTags = IccTags.read(\n                readTags['ICC_Profile'].value,\n                [{\n                    offset: 0,\n                    length: readTags['ICC_Profile'].value.length,\n                    chunkNumber: 1,\n                    chunksTotal: 1\n                }]\n            );\n            if (expanded) {\n                tags.icc = readIccTags;\n            } else {\n                tags = objectAssign({}, tags, readIccTags);\n            }\n        }\n\n        if (Constants.USE_MAKER_NOTES) {\n            if (hasCanonData(readTags)) {\n                const readCanonTags = CanonTags.read(dataView, tiffHeaderOffset, readTags['MakerNote'].__offset, byteOrder, includeUnknown);\n                if (expanded) {\n                    tags.makerNotes = readCanonTags;\n                } else {\n                    tags = objectAssign({}, tags, readCanonTags);\n                }\n            }\n        }\n\n        if (readTags['MakerNote']) {\n            delete readTags['MakerNote'].__offset;\n        }\n    }\n\n    if (Constants.USE_JPEG && Constants.USE_IPTC && hasIptcData(iptcDataOffset)) {\n        foundMetaData = true;\n        const readTags = IptcTags.read(dataView, iptcDataOffset, includeUnknown);\n        if (expanded) {\n            tags.iptc = readTags;\n        } else {\n            tags = objectAssign({}, tags, readTags);\n        }\n    }\n\n    if (Constants.USE_XMP && hasXmpData(xmpChunks)) {\n        foundMetaData = true;\n        const readTags = XmpTags.read(dataView, xmpChunks);\n        if (expanded) {\n            tags.xmp = readTags;\n        } else {\n            delete readTags._raw;\n            tags = objectAssign({}, tags, readTags);\n        }\n    }\n\n    if ((Constants.USE_JPEG || Constants.USE_WEBP) && Constants.USE_ICC && hasIccData(iccChunks)) {\n        foundMetaData = true;\n        const readTags = IccTags.read(dataView, iccChunks, async);\n        if (readTags instanceof Promise) {\n            tagsPromises.push(readTags.then(addIccTags));\n        } else {\n            addIccTags(readTags);\n        }\n    }\n\n    if (Constants.USE_MPF && hasMpfData(mpfDataOffset)) {\n        foundMetaData = true;\n        const readMpfTags = MpfTags.read(dataView, mpfDataOffset, includeUnknown);\n        if (expanded) {\n            tags.mpf = readMpfTags;\n        } else {\n            tags = objectAssign({}, tags, readMpfTags);\n        }\n    }\n\n    if (Constants.USE_PNG && Constants.USE_PNG_FILE && hasPngFileData(pngHeaderOffset)) {\n        foundMetaData = true;\n        const readTags = PngFileTags.read(dataView, pngHeaderOffset);\n        if (expanded) {\n            tags.png = !tags.png ? readTags : objectAssign({}, tags.png, readTags);\n            tags.pngFile = readTags;\n        } else {\n            tags = objectAssign({}, tags, readTags);\n        }\n    }\n\n    if (Constants.USE_PNG && hasPngTextData(pngTextChunks)) {\n        foundMetaData = true;\n        const {readTags, readTagsPromise} = PngTextTags.read(dataView, pngTextChunks, async, includeUnknown);\n        addPngTextTags(readTags);\n        if (readTagsPromise) {\n            tagsPromises.push(readTagsPromise.then((tagList) => tagList.forEach(addPngTextTags)));\n        }\n    }\n\n    if (Constants.USE_PNG && hasPngData(pngChunkOffsets)) {\n        foundMetaData = true;\n        const readTags = PngTags.read(dataView, pngChunkOffsets);\n        if (expanded) {\n            tags.png = !tags.png ? readTags : objectAssign({}, tags.png, readTags);\n        } else {\n            tags = objectAssign({}, tags, readTags);\n        }\n    }\n\n    if (Constants.USE_WEBP && hasVp8xData(vp8xChunkOffset)) {\n        foundMetaData = true;\n        const readTags = Vp8xTags.read(dataView, vp8xChunkOffset);\n        if (expanded) {\n            tags.riff = !tags.riff ? readTags : objectAssign({}, tags.riff, readTags);\n        } else {\n            tags = objectAssign({}, tags, readTags);\n        }\n    }\n\n    if (Constants.USE_GIF && hasGifFileData(gifHeaderOffset)) {\n        foundMetaData = true;\n        const readTags = GifFileTags.read(dataView, gifHeaderOffset);\n        if (expanded) {\n            tags.gif = !tags.gif ? readTags : objectAssign({}, tags.gif, readTags);\n        } else {\n            tags = objectAssign({}, tags, readTags);\n        }\n    }\n\n    const thumbnail = (Constants.USE_JPEG || Constants.USE_WEBP)\n        && Constants.USE_EXIF\n        && Constants.USE_THUMBNAIL\n        && Thumbnail.get(dataView, tags.Thumbnail, tiffHeaderOffset);\n    if (thumbnail) {\n        foundMetaData = true;\n        tags.Thumbnail = thumbnail;\n    } else {\n        delete tags.Thumbnail;\n    }\n\n    if (fileType) {\n        if (expanded) {\n            if (!tags.file) {\n                tags.file = {};\n            }\n            tags.file.FileType = fileType;\n        } else {\n            tags.FileType = fileType;\n        }\n        foundMetaData = true;\n    }\n\n    if (!foundMetaData) {\n        throw new exifErrors.MetadataMissingError();\n    }\n\n    if (async) {\n        return Promise.all(tagsPromises).then(() => tags);\n    }\n    return tags;\n\n    function addIccTags(readTags) {\n        if (expanded) {\n            tags.icc = readTags;\n        } else {\n            tags = objectAssign({}, tags, readTags);\n        }\n    }\n\n    function addPngTextTags(readTags) {\n        if (expanded) {\n            for (const group of ['exif', 'iptc']) {\n                const groupKey = `__${group}`;\n                if (readTags[groupKey]) {\n                    tags[group] = !tags[group] ? readTags[groupKey] : objectAssign({}, tags.exif, readTags[groupKey]);\n                    delete readTags[groupKey];\n                }\n            }\n            tags.png = !tags.png ? readTags : objectAssign({}, tags.png, readTags);\n            tags.pngText = !tags.pngText ? readTags : objectAssign({}, tags.png, readTags);\n        } else {\n            tags = objectAssign(\n                {},\n                tags,\n                readTags.__exif ? readTags.__exif : {},\n                readTags.__iptc ? readTags.__iptc : {},\n                readTags\n            );\n            delete tags.__exif;\n            delete tags.__iptc;\n        }\n    }\n}\n\nfunction hasFileData(fileDataOffset) {\n    return fileDataOffset !== undefined;\n}\n\nfunction hasJfifData(jfifDataOffset) {\n    return jfifDataOffset !== undefined;\n}\n\nfunction hasExifData(tiffHeaderOffset) {\n    return tiffHeaderOffset !== undefined;\n}\n\nfunction addGpsGroup(tags) {\n    if (tags.exif) {\n        if (tags.exif.GPSLatitude && tags.exif.GPSLatitudeRef) {\n            try {\n                tags.gps = tags.gps || {};\n                tags.gps.Latitude = getCalculatedGpsValue(tags.exif.GPSLatitude.value);\n                if (tags.exif.GPSLatitudeRef.value.join('') === 'S') {\n                    tags.gps.Latitude = -tags.gps.Latitude;\n                }\n            } catch (error) {\n                // Ignore.\n            }\n        }\n\n        if (tags.exif.GPSLongitude && tags.exif.GPSLongitudeRef) {\n            try {\n                tags.gps = tags.gps || {};\n                tags.gps.Longitude = getCalculatedGpsValue(tags.exif.GPSLongitude.value);\n                if (tags.exif.GPSLongitudeRef.value.join('') === 'W') {\n                    tags.gps.Longitude = -tags.gps.Longitude;\n                }\n            } catch (error) {\n                // Ignore.\n            }\n        }\n\n        if (tags.exif.GPSAltitude && tags.exif.GPSAltitudeRef) {\n            try {\n                tags.gps = tags.gps || {};\n                tags.gps.Altitude = tags.exif.GPSAltitude.value[0] / tags.exif.GPSAltitude.value[1];\n                if (tags.exif.GPSAltitudeRef.value === 1) {\n                    tags.gps.Altitude = -tags.gps.Altitude;\n                }\n            } catch (error) {\n                // Ignore.\n            }\n        }\n    }\n}\n\nfunction hasIptcData(iptcDataOffset) {\n    return iptcDataOffset !== undefined;\n}\n\nfunction hasXmpData(xmpChunks) {\n    return Array.isArray(xmpChunks) && xmpChunks.length > 0;\n}\n\nfunction hasIccData(iccDataOffsets) {\n    return Array.isArray(iccDataOffsets) && iccDataOffsets.length > 0;\n}\n\nfunction hasCanonData(tags) {\n    return tags['Make'] && tags['Make'].value && Array.isArray(tags['Make'].value) && tags['Make'].value[0] === 'Canon'\n        && tags['MakerNote'] && tags['MakerNote'].__offset;\n}\n\nfunction hasMpfData(mpfDataOffset) {\n    return mpfDataOffset !== undefined;\n}\n\nfunction hasPngFileData(pngFileDataOffset) {\n    return pngFileDataOffset !== undefined;\n}\n\nfunction hasPngTextData(pngTextChunks) {\n    return pngTextChunks !== undefined;\n}\n\nfunction hasPngData(pngChunkOffsets) {\n    return pngChunkOffsets !== undefined;\n}\n\nfunction hasVp8xData(vp8xChunkOffset) {\n    return vp8xChunkOffset !== undefined;\n}\n\nfunction hasGifFileData(gifHeaderOffset) {\n    return gifHeaderOffset !== undefined;\n}\n"], "mappings": ";;;AAAA,IAAqBA,YAArB,MAA8B;AAAA,EAC1B,YAAY,QAAQ;AAChB,QAAI,wBAAwB,MAAM,GAAG;AACjC,YAAM,IAAI,MAAM,8CAA8C;AAAA,IAClE;AAEA,SAAK,SAAS;AACd,SAAK,aAAa,KAAK,OAAO;AAAA,EAClC;AAAA,EAEA,SAAS,QAAQ;AACb,WAAO,KAAK,OAAO,UAAU,MAAM;AAAA,EACvC;AAAA,EAEA,UAAU,QAAQ,cAAc;AAC5B,QAAI,cAAc;AACd,aAAO,KAAK,OAAO,aAAa,MAAM;AAAA,IAC1C;AACA,WAAO,KAAK,OAAO,aAAa,MAAM;AAAA,EAC1C;AAAA,EAEA,UAAU,QAAQ,cAAc;AAC5B,QAAI,cAAc;AACd,aAAO,KAAK,OAAO,aAAa,MAAM;AAAA,IAC1C;AACA,WAAO,KAAK,OAAO,aAAa,MAAM;AAAA,EAC1C;AAAA,EAEA,SAAS,QAAQ,cAAc;AAC3B,QAAI,cAAc;AACd,aAAO,KAAK,OAAO,YAAY,MAAM;AAAA,IACzC;AACA,WAAO,KAAK,OAAO,YAAY,MAAM;AAAA,EACzC;AACJ;AAEA,SAAS,wBAAwB,QAAQ;AACrC,SAAO,OAAO,WAAW,YAClB,OAAO,WAAW,UAClB,OAAO,cAAc,UACrB,OAAO,iBAAiB,UACxB,OAAO,iBAAiB,UACxB,OAAO,iBAAiB,UACxB,OAAO,iBAAiB,UACxB,OAAO,gBAAgB,UACvB,OAAO,gBAAgB;AAClC;;;ACxCO,SAAS,YAAY,MAAM,YAAY,YAAY;AACtD,MAAI;AACA,WAAO,IAAI,SAAS,MAAM,YAAY,UAAU;AAAA,EACpD,SAAS,OAAP;AACE,WAAO,IAAIC,UAAgB,MAAM,YAAY,UAAU;AAAA,EAC3D;AACJ;AAEO,SAAS,sBAAsB,UAAU,QAAQ,QAAQ;AAC5D,QAAM,QAAQ,CAAC;AACf,WAAS,IAAI,GAAG,IAAI,UAAU,SAAS,IAAI,SAAS,YAAY,KAAK;AACjE,UAAM,KAAK,SAAS,SAAS,SAAS,CAAC,CAAC;AAAA,EAC5C;AACA,SAAO,wBAAwB,KAAK;AACxC;AAEO,SAAS,oCAAoC,UAAU,QAAQ;AAClE,QAAM,QAAQ,CAAC;AACf,MAAI,IAAI;AACR,SAAO,SAAS,IAAI,SAAS,YAAY;AACrC,UAAM,OAAO,SAAS,SAAS,SAAS,CAAC;AACzC,QAAI,SAAS,GAAG;AACZ;AAAA,IACJ;AACA,UAAM,KAAK,IAAI;AACf;AAAA,EACJ;AACA,SAAO,wBAAwB,KAAK;AACxC;AAEO,SAAS,6BAA6B,UAAU,QAAQ,QAAQ;AACnE,QAAM,QAAQ,CAAC;AACf,WAAS,IAAI,GAAG,IAAI,UAAU,SAAS,IAAI,SAAS,YAAY,KAAK,GAAG;AACpE,UAAM,KAAK,SAAS,UAAU,SAAS,CAAC,CAAC;AAAA,EAC7C;AACA,MAAI,MAAM,MAAM,SAAS,OAAO,GAAG;AAC/B,UAAM,IAAI;AAAA,EACd;AACA,SAAO,wBAAwB,KAAK;AACxC;AAEO,SAAS,4BAA4B,UAAU,QAAQ;AAC1D,QAAM,OAAO,SAAS,SAAS,MAAM;AACrC,QAAM,SAAS,sBAAsB,UAAU,SAAS,GAAG,IAAI;AAC/D,SAAO,CAAC,MAAM,MAAM;AACxB;AAEO,SAAS,wBAAwB,WAAW;AAC/C,SAAO,UAAU,IAAI,CAAC,aAAa,OAAO,aAAa,QAAQ,CAAC,EAAE,KAAK,EAAE;AAC7E;AAMO,SAAS,eAAe;AAC3B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,eAAW,YAAY,UAAU,IAAI;AACjC,gBAAU,GAAG,YAAY,UAAU,GAAG;AAAA,IAC1C;AAAA,EACJ;AAEA,SAAO,UAAU;AACrB;AAEO,SAAS,UAAU,QAAQ,KAAK,aAAa;AAChD,MAAI,cAAc;AAClB,SAAO,eAAe,QAAQ,KAAK;AAAA,IAC/B,MAAM;AACF,UAAI,CAAC,aAAa;AACd,sBAAc;AACd,eAAO,eAAe,QAAQ,KAAK;AAAA,UAC/B,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,OAAO,YAAY,MAAM,MAAM;AAAA,UAC/B,UAAU;AAAA,QACd,CAAC;AAAA,MACL;AACA,aAAO,OAAO;AAAA,IAClB;AAAA,IACA,cAAc;AAAA,IACd,YAAY;AAAA,EAChB,CAAC;AACL;AAEO,SAAS,eAAe,OAAO;AAClC,MAAI,OAAO,SAAS,aAAa;AAC7B,QAAI,OAAO,UAAU,UAAU;AAE3B,aAAO,KAAK,KAAK;AAAA,IACrB;AAEA,WAAO,KAAK,MAAM,UAAU,OAAO,KAAK,IAAI,WAAW,KAAK,GAAG,CAAC,MAAM,SAAS,OAAO,OAAO,aAAa,IAAI,GAAG,EAAE,CAAC;AAAA,EACxH;AACA,MAAI,OAAO,WAAW,aAAa;AAC/B,WAAO;AAAA,EACX;AACA,MAAI,OAAO,OAAO,SAAS,aAAa;AACpC,WAAO,OAAO,KAAK,KAAK,EAAE,SAAS,QAAQ;AAAA,EAC/C;AACA,SAAQ,IAAI,OAAO,KAAK,EAAG,SAAS,QAAQ;AAChD;AAEO,SAAS,gBAAgB,SAAS;AACrC,QAAM,OAAO,QAAQ,UAAU,QAAQ,QAAQ,GAAG,IAAI,CAAC;AAEvD,MAAI,QAAQ,QAAQ,SAAS,MAAM,IAAI;AACnC,QAAI,OAAO,SAAS,aAAa;AAC7B,aAAO,WAAW,KAAK,KAAK,IAAI,GAAG,CAAC,SAAS,KAAK,WAAW,CAAC,CAAC,EAAE;AAAA,IACrE;AACA,QAAI,OAAO,WAAW,aAAa;AAC/B,aAAO;AAAA,IACX;AACA,QAAI,OAAO,OAAO,SAAS,aAAa;AACpC,aAAO,OAAO,KAAK,MAAM,QAAQ;AAAA,IACrC;AACA,WAAO,IAAI,OAAO,MAAM,QAAQ;AAAA,EACpC;AAEA,QAAM,cAAc,mBAAmB,IAAI;AAC3C,MAAI,OAAO,WAAW,aAAa;AAC/B,QAAI,OAAO,OAAO,SAAS,aAAa;AACpC,aAAO,OAAO,KAAK,WAAW;AAAA,IAClC;AACA,WAAO,IAAI,OAAO,WAAW;AAAA,EACjC;AACA,SAAO,WAAW,KAAK,aAAa,CAAC,SAAS,KAAK,WAAW,CAAC,CAAC,EAAE;AACtE;AAEO,SAAS,SAAS,QAAQ,QAAQ,WAAW;AAChD,QAAM,UAAU,UAAU,WAAW,SAAS,OAAO,MAAM;AAC3D,SAAO,UAAU;AACrB;AAEO,SAAS,gBAAgB,QAAQ,OAAO;AAC3C,SAAO,SAAS,OAAO,QAAQ,KAAK,EAAE,GAAG,KAAK,IACxC,KAAK,IAAI,QAAQ,OAAO,MAAM,GAAG,EAAE,MAAM,IAAI,MAAM;AAC7D;AAEO,SAAS,UAAU,QAAQ,KAAK;AACnC,SAAO,IAAI,MAAM,MAAM,CAAC,EAAE,KAAK,MAAM;AACzC;AAEO,IAAM,0BAA0B;AAChC,IAAM,6BAA6B;AAEnC,SAAS,WAAW,UAAU,mBAAmB,UAAU,aAAa,UAAU;AACrF,MAAI,sBAAsB,4BAA4B;AAClD,QAAI,OAAO,wBAAwB,YAAY;AAC3C,YAAM,sBAAsB,IAAI,oBAAoB,SAAS;AAC7D,YAAM,qBAAqB,IAAI,KAAK,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,YAAY,mBAAmB;AACxF,UAAI,eAAe,YAAY;AAC3B,eAAO,IAAI,SAAS,kBAAkB,EAAE,YAAY,EAAE,KAAK,CAAC,gBAAgB,IAAI,SAAS,WAAW,CAAC;AAAA,MACzG;AACA,aAAO,IAAI,SAAS,kBAAkB,EAAE,YAAY,EAC/C,KAAK,CAAC,WAAW,IAAI,YAAY,QAAQ,EAAE,OAAO,MAAM,CAAC;AAAA,IAClE;AAAA,EACJ;AACA,MAAI,sBAAsB,QAAW;AACjC,WAAO,QAAQ,OAAO,8BAA8B,oBAAoB;AAAA,EAC5E;AACA,SAAO;AACX;;;ACpKA,IAAO,oBAAQ;AAAA,EACX,UAAU;AAAA,EACV,UAAU;AAAA,EACV,cAAc;AAAA,EACd,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,SAAS;AAAA,EACT,eAAe;AAAA,EACf,eAAe;AAAA,EACf,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,iBAAiB;AACrB;;;ACnBO,SAAS,eAAe,OAAO;AAClC,SAAO,MAAM,IAAI,CAAC,aAAa,OAAO,aAAa,QAAQ,CAAC,EAAE,KAAK,EAAE;AACzE;AAEO,SAAS,iBAAiB,OAAO;AACpC,MAAI,MAAM,UAAU,GAAG;AACnB,UAAM,WAAW,eAAe,MAAM,MAAM,GAAG,CAAC,CAAC;AAEjD,QAAI,aAAa,eAAqB;AAClC,aAAO,eAAe,MAAM,MAAM,CAAC,CAAC;AAAA,IACxC,WAAW,aAAa,iBAA2B;AAC/C,aAAO;AAAA,IACX,WAAW,aAAa,aAAe;AACnC,aAAO;AAAA,IACX,WAAW,aAAa,oBAAoC;AACxD,aAAO;AAAA,IACX;AAAA,EACJ;AAEA,SAAO;AACX;AAEO,SAAS,sBAAsB,OAAO;AACzC,SAAQ,MAAM,GAAG,KAAK,MAAM,GAAG,KAAO,MAAM,GAAG,KAAK,MAAM,GAAG,KAAM,KAAM,MAAM,GAAG,KAAK,MAAM,GAAG,KAAM;AAC1G;;;ACxBA,IAAM,gBAAgB;AACtB,IAAM,aAAa;AAEnB,IAAO,qBAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AACJ;AAEA,SAAS,aAAa,UAAU,kBAAkB;AAC9C,MAAI,SAAS,UAAU,gBAAgB,MAAM,eAAe;AACxD,WAAO;AAAA,EACX,WAAW,SAAS,UAAU,gBAAgB,MAAM,YAAY;AAC5D,WAAO;AAAA,EACX;AACA,QAAM,IAAI,MAAM,yCAAyC;AAC7D;;;ACbA,IAAO,4BAAQ;AAAA,EACX;AAAA,EACA;AACJ;AAEA,SAAS,WAAW,UAAU;AAC1B,QAAM,8BAA8B;AAEpC,SAAO,CAAC,CAAC,YAAa,SAAS,cAAc,+BAAgC,cAAc,QAAQ;AACvG;AAEA,SAAS,cAAc,UAAU;AAC7B,QAAM,UAAU;AAChB,QAAM,iBAAiB;AAEvB,QAAM,eAAe,SAAS,UAAU,CAAC,MAAM,mBAAU;AACzD,SAAO,SAAS,UAAU,gBAAgB,YAAY,MAAM;AAChE;AAEA,SAAS,kBAAkB;AACvB,QAAM,0BAA0B;AAEhC,MAAI,kBAAU,UAAU;AACpB,WAAO;AAAA,MACH,eAAe;AAAA,MACf,kBAAkB;AAAA,IACtB;AAAA,EACJ;AACA,SAAO,CAAC;AACZ;;;AC7BA,IAAO,4BAAQ;AAAA,EACX;AAAA,EACA;AACJ;AAEA,IAAM,8BAA8B;AACpC,IAAM,UAAU;AAChB,IAAM,eAAe;AACrB,IAAM,gBAAgB;AACtB,IAAM,kBAAkB;AACxB,IAAM,mBAAmB;AACzB,IAAM,qBAAqB;AAC3B,IAAM,mBAAmB;AACzB,IAAM,kBAAkB;AACxB,IAAM,2BAA2B;AACjC,IAAM,uBAAuB;AAC7B,IAAM,kBAAkB;AAExB,IAAM,sBAAsB;AAC5B,IAAM,0BAA0B,gBAAgB,oBAAoB;AACpE,IAAM,0BAA0B,0BAA0B;AAE1D,IAAM,sBAAsB;AAE5B,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,aAAa;AACnB,IAAM,aAAa;AACnB,IAAM,aAAa;AAEnB,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,iBAAiB;AAIvB,IAAM,YAAY;AAElB,IAAM,uBAAuB;AAC7B,IAAM,uBAAuB;AAC7B,IAAM,sBAAsB;AAC5B,IAAM,+BAA+B;AACrC,IAAM,wBAAwB;AAE9B,SAAS,WAAW,UAAU;AAC1B,SAAO,CAAC,CAAC,YAAa,SAAS,cAAc,+BAAiC,SAAS,UAAU,CAAC,MAAM;AAC5G;AAEA,SAAS,gBAAgB,UAAU;AAC/B,MAAI,oBAAoB;AACxB,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,SAAO,oBAAoB,gBAAgB,KAAK,SAAS,YAAY;AACjE,QAAI,kBAAU,YAAY,aAAa,UAAU,iBAAiB,GAAG;AACjE,oBAAc,SAAS,UAAU,oBAAoB,eAAe;AACpE,uBAAiB,oBAAoB;AAAA,IACzC,WAAW,kBAAU,YAAY,aAAa,UAAU,iBAAiB,GAAG;AACxE,oBAAc,SAAS,UAAU,oBAAoB,eAAe;AACpE,uBAAiB,oBAAoB;AAAA,IACzC,WAAW,kBAAU,YAAY,iBAAiB,UAAU,iBAAiB,GAAG;AAC5E,oBAAc,SAAS,UAAU,oBAAoB,eAAe;AACpE,uBAAiB,oBAAoB;AAAA,IACzC,WAAW,kBAAU,YAAY,iBAAiB,UAAU,iBAAiB,GAAG;AAC5E,oBAAc,SAAS,UAAU,oBAAoB,eAAe;AACpE,yBAAmB,oBAAoB;AAAA,IAC3C,WAAW,kBAAU,WAAW,gBAAgB,UAAU,iBAAiB,GAAG;AAC1E,UAAI,CAAC,WAAW;AACZ,oBAAY,CAAC;AAAA,MACjB;AACA,oBAAc,SAAS,UAAU,oBAAoB,eAAe;AACpE,gBAAU,KAAK,mBAAmB,mBAAmB,WAAW,CAAC;AAAA,IACrE,WAAW,kBAAU,WAAW,wBAAwB,UAAU,iBAAiB,GAAG;AAClF,UAAI,CAAC,WAAW;AACZ,oBAAY,CAAC;AAAA,MACjB;AACA,oBAAc,SAAS,UAAU,oBAAoB,eAAe;AACpE,gBAAU,KAAK,2BAA2B,mBAAmB,WAAW,CAAC;AAAA,IAC7E,WAAW,kBAAU,YAAY,uBAAuB,UAAU,iBAAiB,GAAG;AAClF,oBAAc,SAAS,UAAU,oBAAoB,eAAe;AACpE,uBAAiB,oBAAoB;AAAA,IACzC,WAAW,kBAAU,WAAW,gBAAgB,UAAU,iBAAiB,GAAG;AAC1E,oBAAc,SAAS,UAAU,oBAAoB,eAAe;AACpE,YAAM,gBAAgB,oBAAoB;AAC1C,YAAM,gBAAgB,eAAe,uBAAuB;AAE5D,YAAM,iBAAiB,SAAS,SAAS,oBAAoB,uBAAuB;AACpF,YAAM,iBAAiB,SAAS,SAAS,oBAAoB,uBAAuB;AACpF,UAAI,CAAC,WAAW;AACZ,oBAAY,CAAC;AAAA,MACjB;AACA,gBAAU,KAAK,EAAC,QAAQ,eAAe,QAAQ,eAAe,aAAa,gBAAgB,aAAa,eAAc,CAAC;AAAA,IAC3H,WAAW,kBAAU,WAAW,gBAAgB,UAAU,iBAAiB,GAAG;AAC1E,oBAAc,SAAS,UAAU,oBAAoB,eAAe;AACpE,sBAAgB,oBAAoB;AAAA,IACxC,WAAW,YAAY,UAAU,iBAAiB,GAAG;AACjD,oBAAc,SAAS,UAAU,oBAAoB,eAAe;AAAA,IACxE,WAAW,WAAW,UAAU,iBAAiB,GAAG;AAChD;AACA;AAAA,IACJ,OAAO;AACH;AAAA,IACJ;AACA,yBAAqB,kBAAkB;AAAA,EAC3C;AAEA,SAAO;AAAA,IACH,eAAe,oBAAoB;AAAA,IACnC,gBAAgB,kBAAkB;AAAA,IAClC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,SAAS,aAAa,UAAU,mBAAmB;AAC/C,SAAQ,SAAS,UAAU,iBAAiB,MAAM;AACtD;AAEA,SAAS,aAAa,UAAU,mBAAmB;AAC/C,SAAQ,SAAS,UAAU,iBAAiB,MAAM;AACtD;AAEA,SAAS,gBAAgB,UAAU,mBAAmB;AAClD,QAAM,iBAAiB,oBAAoB;AAE3C,SAAQ,SAAS,UAAU,iBAAiB,MAAM,eAC1C,sBAAsB,UAAU,oBAAoB,eAAe,cAAc,MAAM;AACnG;AAEA,SAAS,gBAAgB,UAAU,mBAAmB;AAClD,QAAM,iBAAiB,oBAAoB;AAE3C,SAAQ,SAAS,UAAU,iBAAiB,MAAM,eAC1C,sBAAsB,UAAU,oBAAoB,eAAe,cAAc,MAAM;AACnG;AAEA,SAAS,iBAAiB,UAAU,mBAAmB;AACnD,QAAM,iBAAiB,qBAAqB;AAE5C,SAAQ,SAAS,UAAU,iBAAiB,MAAM,eAC1C,sBAAsB,UAAU,oBAAoB,eAAe,cAAc,MAAM,wBACvF,SAAS,SAAS,oBAAoB,gBAAgB,cAAc,MAAM;AACtF;AAEA,SAAS,iBAAiB,UAAU,mBAAmB;AACnD,QAAM,iBAAiB,qBAAqB;AAE5C,SAAQ,SAAS,UAAU,iBAAiB,MAAM,eAC1C,sBAAsB,UAAU,oBAAoB,eAAe,cAAc,MAAM,wBACvF,SAAS,SAAS,oBAAoB,gBAAgB,cAAc,MAAM;AACtF;AAEA,SAAS,gBAAgB,UAAU,mBAAmB;AAClD,SAAQ,SAAS,UAAU,iBAAiB,MAAM,eAC3C,gBAAgB,UAAU,iBAAiB;AACtD;AAEA,SAAS,gBAAgB,UAAU,mBAAmB;AAClD,QAAM,iBAAiB,oBAAoB;AAC3C,SAAO,sBAAsB,UAAU,oBAAoB,eAAe,cAAc,MAAM;AAClG;AAEA,SAAS,wBAAwB,UAAU,mBAAmB;AAC1D,SAAQ,SAAS,UAAU,iBAAiB,MAAM,eAC3C,wBAAwB,UAAU,iBAAiB;AAC9D;AAEA,SAAS,wBAAwB,UAAU,mBAAmB;AAC1D,QAAM,iBAAiB,6BAA6B;AACpD,SAAO,sBAAsB,UAAU,oBAAoB,eAAe,cAAc,MAAM;AAClG;AAEA,SAAS,mBAAmB,mBAAmB,aAAa;AACxD,SAAO;AAAA,IACH,YAAY,oBAAoB;AAAA,IAChC,QAAQ,eAAe,kBAAkB;AAAA,EAC7C;AACJ;AAEA,SAAS,2BAA2B,mBAAmB,aAAa;AAChE,SAAO;AAAA,IACH,YAAY,oBAAoB;AAAA,IAChC,QAAQ,eAAe,2BAA2B;AAAA,EACtD;AACJ;AAEA,SAAS,uBAAuB,UAAU,mBAAmB;AACzD,QAAM,iBAAiB,sBAAsB;AAE7C,SAAQ,SAAS,UAAU,iBAAiB,MAAM,gBAC1C,sBAAsB,UAAU,oBAAoB,eAAe,cAAc,MAAM,yBACvF,SAAS,SAAS,oBAAoB,gBAAgB,cAAc,MAAM;AACtF;AAEA,SAAS,YAAY,UAAU,mBAAmB;AAC9C,QAAM,YAAY,SAAS,UAAU,iBAAiB;AACtD,SAAS,aAAa,eAAiB,aAAa,gBAC5C,cAAc,kBACd,cAAc,eACd,cAAc,eACd,cAAc,cACd,cAAc,cACd,cAAc,cACd,cAAc;AAC1B;AAEA,SAAS,WAAW,UAAU,mBAAmB;AAC7C,SAAO,SAAS,UAAU,iBAAiB,MAAM;AACrD;;;AC7NA,IAAO,2BAAQ;AAAA,EACX;AAAA,EACA;AACJ;AAEA,IAAM,SAAS;AACf,IAAM,wBAAwB;AACvB,IAAM,sBAAsB;AAC5B,IAAM,0BAA0B;AAChC,IAAM,wBAAwB;AAC9B,IAAM,wBAAwB,wBAAwB;AAC7D,IAAM,iBAAiB;AAChB,IAAM,YAAY;AAClB,IAAM,YAAY;AAClB,IAAM,YAAY;AAClB,IAAM,YAAY;AAClB,IAAM,YAAY;AAClB,IAAM,YAAY;AAClB,IAAM,YAAY;AAEzB,SAAS,UAAU,UAAU;AACzB,SAAO,CAAC,CAAC,YAAY,sBAAsB,UAAU,GAAG,OAAO,MAAM,MAAM;AAC/E;AAEA,SAAS,eAAe,UAAU,OAAO;AACrC,QAAM,eAAe;AAErB,QAAM,UAAU;AAAA,IACZ,eAAe;AAAA,EACnB;AAEA,MAAI,SAAS,OAAO;AAEpB,SAAO,SAAS,wBAAwB,uBAAuB,SAAS,YAAY;AAChF,QAAI,kBAAU,gBAAgB,sBAAsB,UAAU,MAAM,GAAG;AACnE,cAAQ,gBAAgB;AACxB,cAAQ,kBAAkB,SAAS;AAAA,IACvC,WAAW,kBAAU,WAAW,cAAc,UAAU,MAAM,GAAG;AAC7D,YAAM,aAAa,oBAAoB,UAAU,MAAM;AACvD,UAAI,eAAe,QAAW;AAC1B,gBAAQ,gBAAgB;AACxB,gBAAQ,YAAY,CAAC;AAAA,UACjB;AAAA,UACA,QAAQ,SAAS,UAAU,SAAS,uBAAuB,KAAK,cAAc,SAAS;AAAA,QAC3F,CAAC;AAAA,MACL;AAAA,IACJ,WAAW,eAAe,UAAU,QAAQ,KAAK,GAAG;AAChD,cAAQ,gBAAgB;AACxB,YAAM,YAAY,sBAAsB,UAAU,SAAS,uBAAuB,mBAAmB;AACrG,UAAI,CAAC,QAAQ,eAAe;AACxB,gBAAQ,gBAAgB,CAAC;AAAA,MAC7B;AACA,cAAQ,cAAc,KAAK;AAAA,QACvB,QAAQ,SAAS,UAAU,SAAS,uBAAuB;AAAA,QAC3D,MAAM;AAAA,QACN,QAAQ,SAAS;AAAA,MACrB,CAAC;AAAA,IACL,WAAW,eAAe,UAAU,MAAM,GAAG;AACzC,cAAQ,gBAAgB;AACxB,cAAQ,mBAAmB,SAAS;AAAA,IACxC,WAAW,kBAAU,WAAW,SAAS,eAAe,UAAU,MAAM,GAAG;AACvE,cAAQ,gBAAgB;AACxB,YAAM,kBAAkB,SAAS,UAAU,SAAS,uBAAuB;AAC3E,YAAM,kBAAkB,SAAS;AACjC,YAAM,EAAC,aAAa,mBAAmB,wBAAuB,IAAI,eAAe,UAAU,eAAe;AAC1G,UAAI,CAAC,QAAQ,WAAW;AACpB,gBAAQ,YAAY,CAAC;AAAA,MACzB;AACA,cAAQ,UAAU,KAAK;AAAA,QACnB,QAAQ;AAAA,QACR,QAAQ,mBAAmB,0BAA0B;AAAA,QACrD,aAAa;AAAA,QACb,aAAa;AAAA,QACb;AAAA,QACA;AAAA,MACJ,CAAC;AAAA,IACL,WAAW,WAAW,UAAU,MAAM,GAAG;AACrC,cAAQ,gBAAgB;AACxB,UAAI,CAAC,QAAQ,iBAAiB;AAC1B,gBAAQ,kBAAkB,CAAC;AAAA,MAC/B;AACA,cAAQ,gBAAgB,KAAK,SAAS,uBAAuB;AAAA,IACjE;AAEA,cAAU,SAAS,UAAU,SAAS,uBAAuB,IACvD,wBACA,sBACA;AAAA,EACV;AAEA,SAAO;AACX;AAEA,SAAS,sBAAsB,UAAU,QAAQ;AAC7C,QAAM,8BAA8B;AACpC,SAAO,sBAAsB,UAAU,SAAS,uBAAuB,mBAAmB,MAAM;AACpG;AAEA,SAAS,cAAc,UAAU,QAAQ;AACrC,SAAQ,sBAAsB,UAAU,SAAS,uBAAuB,mBAAmB,MAAM,aACzF,sBAAsB,UAAU,SAAS,uBAAuB,eAAe,MAAM,MAAM;AACvG;AAEA,SAAS,eAAe,UAAU,QAAQ,OAAO;AAC7C,QAAM,YAAY,sBAAsB,UAAU,SAAS,uBAAuB,mBAAmB;AACrG,SAAO,cAAc,aAAa,cAAc,aAAc,cAAc,aAAa;AAC7F;AAEA,SAAS,eAAe,UAAU,QAAQ;AACtC,SAAO,sBAAsB,UAAU,SAAS,uBAAuB,mBAAmB,MAAM;AACpG;AAEA,SAAS,eAAe,UAAU,QAAQ;AACtC,SAAO,sBAAsB,UAAU,SAAS,uBAAuB,mBAAmB,MAAM;AACpG;AAEA,SAAS,WAAW,UAAU,QAAQ;AAClC,QAAM,4BAA4B,CAAC,WAAW,SAAS;AACvD,QAAM,YAAY,sBAAsB,UAAU,SAAS,uBAAuB,mBAAmB;AACrG,SAAO,0BAA0B,SAAS,SAAS;AACvD;AAEA,SAAS,oBAAoB,UAAU,QAAQ;AAC3C,QAAM,wBAAwB;AAC9B,QAAM,0BAA0B;AAEhC,YAAU,wBAAwB,eAAe,SAAS,wBAAwB;AAElF,MAAI,yBAAyB;AAC7B,SAAO,yBAAyB,KAAK,SAAS,SAAS,YAAY;AAC/D,QAAI,SAAS,SAAS,MAAM,MAAM,GAAM;AACpC;AAAA,IACJ;AACA;AAAA,EACJ;AACA,MAAI,yBAAyB,GAAG;AAC5B,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAEA,SAAS,eAAe,UAAU,QAAQ;AACtC,QAAM,sBAAsB;AAC5B,QAAM,0BAA0B;AAEhC,QAAM,cAAc,oCAAoC,UAAU,MAAM;AACxE,YAAU,YAAY,SAAS;AAE/B,QAAM,oBAAoB,SAAS,SAAS,MAAM;AAClD,YAAU;AAEV,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA,yBAAyB;AAAA,EAC7B;AACJ;;;ACrKO,SAAS,cAAc,UAAU,QAAQ;AAG5C,SAAO,SAAS,UAAU,SAAS,CAAC;AACxC;;;ACFO,SAAS,qBAAqB,UAAU,SAAS,eAAe,WAAW;AAC9E,QAAM,aAAa;AAEnB,QAAM,EAAC,SAAS,MAAK,IAAI,kCAAkC,SAAS,gBAAgB,UAAU;AAE9F,QAAM,aAAa,SAAS,SAAS,QAAQ,UAAU,KAAK;AAC5D,QAAM,KAAK,OAAO,eAAe;AACjC,QAAM,aAAa,SAAS,SAAS,QAAQ,UAAU,IAAI;AAC3D,QAAM,KAAK,OAAO,eAAe;AACjC,QAAM,iBAAiB,SAAS,SAAS,QAAQ,cAAc,KAAK;AACpE,QAAM,KAAK,aAAa;AACxB,QAAM,YAAY,aAAa,UAAU,QAAQ,WAAW,OAAO;AACnE,QAAM,KAAK,OAAO,cAAc,cAAc,SAAY,YAAY;AACtE,QAAM,YAAY,aAAa,UAAU,QAAQ,WAAW,OAAO;AAEnE,SAAO;AAAA,IACH,MAAM;AAAA,IACN,OAAO,SAAS,UAAU,SAAS,SAAS,OAAO,YAAY,YAAY,WAAW,SAAS;AAAA,IAC/F,QAAQ;AAAA,EACZ;AACJ;AAEA,SAAS,kCAAkC,SAAS,eAAe;AAC/D,QAAM,QAAQ;AAAA,IACV,MAAM;AAAA,MACF,oBAAoB;AAAA,MACpB,aAAa;AAAA,MACb,QAAQ,CAAC;AAAA,IACb;AAAA,EACJ;AACA,MAAI,UAAU,GAAG;AACb,UAAM,YAAY;AAClB,UAAM,KAAK,SAAS;AAAA,EACxB,WAAW,YAAY,GAAG;AACtB,UAAM,YAAY;AAClB,UAAM,KAAK,SAAS;AAAA,EACxB;AACA,MAAI,YAAY,KAAK,YAAY,GAAG;AAChC,UAAM,KAAK,qBAAqB;AAAA,EACpC,OAAO;AACH,UAAM,KAAK,qBAAqB;AAAA,EACpC;AAEA,QAAM,UAAU;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,gBAAgB,gBAAgB;AAAA,IAChC,WAAW,gBAAgB;AAAA,EAC/B;AACA,UAAQ,YAAY,gBAAgB;AACpC,UAAQ,QAAQ,QAAQ,YAAY,MAAM;AAC1C,UAAQ,OAAO;AAAA,IACX,QAAQ;AAAA,EACZ;AACA,UAAQ,KAAK,qBAAqB,QAAQ,KAAK,SAAS,MAAM,KAAK;AACnE,UAAQ,KAAK,qBAAqB,QAAQ,KAAK,qBAAqB,MAAM,KAAK;AAE/E,SAAO,EAAC,SAAS,MAAK;AAC1B;AAEA,SAAS,aAAa,UAAU,QAAQ,SAAS;AAC7C,MAAI,YAAY,KAAK,YAAY,GAAG;AAChC,WAAO,SAAS,SAAS,MAAM,IAAI;AAAA,EACvC;AACA,SAAO;AACX;AAEA,SAAS,aAAa,UAAU,QAAQ,SAAS;AAC7C,MAAI,UAAU,GAAG;AACb,WAAO,SAAS,UAAU,MAAM;AAAA,EACpC,WAAW,YAAY,GAAG;AACtB,WAAO,SAAS,UAAU,MAAM;AAAA,EACpC;AACA,SAAO;AACX;AAEA,SAAS,SAAS,UAAU,SAAS,SAAS,OAAO,YAAY,YAAY,WAAW,WAAW;AAC/F,MAAI,cAAc,QAAW;AACzB,WAAO,CAAC;AAAA,EACZ;AAEA,QAAM,QAAQ,CAAC;AACf,MAAI,SAAS,QAAQ;AAErB,WAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAChC,UAAM,OAAO,EAAC,SAAS,CAAC,EAAC;AACzB,SAAK,SAAS,UAAU,UAAU,QAAQ,OAAO;AACjD,cAAU,MAAM,KAAK;AACrB,SAAK,qBAAsB,YAAY,KAAO,YAAY,IAAK,SAAS,UAAU,MAAM,IAAI,KAAO;AACnG,cAAU,MAAM,KAAK;AACrB,SAAK,qBAAqB,SAAS,UAAU,MAAM;AACnD,cAAU,MAAM,KAAK;AACrB,SAAK,aAAa,sBAAsB,UAAU,QAAQ,MAAM,KAAK,UAAU;AAC/E,cAAU,MAAM,KAAK;AACrB,SAAK,cAAc,SAAS,UAAU,MAAM;AAC5C,cAAU,MAAM,KAAK;AACrB,aAAS,IAAI,GAAG,IAAI,KAAK,aAAa,KAAK;AACvC,YAAM,SAAS,CAAC;AAEhB,aAAO,cAAc,eAAe,UAAU,SAAS,QAAQ,SAAS;AACxE,gBAAU,MAAM,KAAK,OAAO;AAC5B,aAAO,eAAe,sBAAsB,UAAU,QAAQ,UAAU;AACxE,gBAAU,MAAM,KAAK,OAAO;AAC5B,aAAO,eAAe,sBAAsB,UAAU,QAAQ,UAAU;AACxE,gBAAU,MAAM,KAAK,OAAO;AAE5B,WAAK,QAAQ,KAAK,MAAM;AAAA,IAC5B;AAEA,UAAM,KAAK,IAAI;AAAA,EACnB;AAEA,SAAO;AACX;AAEA,SAAS,UAAU,UAAU,QAAQ,SAAS;AAC1C,MAAI,UAAU,GAAG;AACb,WAAO,SAAS,UAAU,MAAM;AAAA,EACpC,WAAW,YAAY,GAAG;AACtB,WAAO,SAAS,UAAU,MAAM;AAAA,EACpC;AACA,SAAO;AACX;AAEA,SAAS,eAAe,UAAU,SAAS,QAAQ,WAAW;AAC1D,OAAK,YAAY,KAAK,YAAY,MAAM,YAAY,GAAG;AACnD,WAAO,sBAAsB,UAAU,QAAQ,SAAS;AAAA,EAC5D;AACA,SAAO;AACX;AAEA,SAAS,sBAAsB,UAAU,QAAQ,MAAM;AACnD,MAAI,SAAS,GAAG;AACZ,WAAO,SAAS,UAAU,MAAM;AAAA,EACpC;AACA,MAAI,SAAS,GAAG;AAEZ,YAAQ,KAAK,wHAAwH;AACrI,WAAO,cAAc,UAAU,MAAM;AAAA,EACzC;AACA,SAAO;AACX;;;AC3HA,IAAM,YAAY;AAClB,IAAM,YAAY;AAClB,IAAM,YAAY;AAClB,IAAM,YAAY;AAClB,IAAM,YAAY;AAClB,IAAM,YAAY;AAClB,IAAM,YAAY;AAClB,IAAM,YAAY;AAIX,IAAM,sBAAsB;AAC5B,IAAM,sBAAsB;AACnC,IAAM,qBAAqB;AASpB,SAAS,SAAS,UAAU,QAAQ;AACvC,QAAM,kBAAkB;AACxB,QAAM,iBAAiB;AACvB,QAAM,eAAe;AAErB,QAAM,EAAC,QAAQ,cAAa,IAAI,aAAa,UAAU,MAAM;AAC7D,MAAI,SAAS,gBAAgB;AACzB,WAAO;AAAA,EACX;AAEA,QAAM,OAAO,SAAS,UAAU,SAAS,eAAe;AAExD,MAAI,SAAS,WAAW;AACpB,WAAO,iBAAiB,UAAU,eAAe,MAAM;AAAA,EAC3D;AACA,MAAI,SAAS,WAAW;AACpB,WAAO,uBAAuB,UAAU,QAAQ,eAAe,MAAM;AAAA,EACzE;AACA,MAAI,SAAS,WAAW;AACpB,WAAO,8BAA8B,UAAU,QAAQ,eAAe,MAAM;AAAA,EAChF;AACA,MAAI,SAAS,WAAW;AACpB,WAAO,yBAAyB,UAAU,eAAe,MAAM;AAAA,EACnE;AAGA,QAAM,UAAU,SAAS,SAAS,aAAa;AAE/C,MAAI,SAAS,WAAW;AACpB,WAAO,iBAAiB,UAAU,QAAQ,gBAAgB,cAAc,MAAM;AAAA,EAClF;AACA,MAAI,SAAS,WAAW;AACpB,WAAO,qBAAqB,UAAU,SAAS,gBAAgB,cAAc,MAAM;AAAA,EACvF;AACA,MAAI,SAAS,WAAW;AACpB,WAAO,wBAAwB,UAAU,QAAQ,SAAS,gBAAgB,cAAc,MAAM;AAAA,EAClG;AACA,MAAI,SAAS,WAAW;AACpB,WAAO,6BAA6B,UAAU,QAAQ,SAAS,gBAAgB,cAAc,MAAM;AAAA,EACvG;AAEA,SAAO;AAAA,IAEH,MAAM;AAAA,IACN;AAAA,EACJ;AACJ;AAaA,SAAS,aAAa,UAAU,QAAQ;AACpC,QAAM,kBAAkB;AACxB,QAAM,gBAAgB;AACtB,QAAM,oBAAoB;AAC1B,QAAM,+BAA+B;AAErC,QAAM,YAAY,SAAS,UAAU,MAAM;AAC3C,MAAI,mBAAmB,SAAS,GAAG;AAC/B,WAAO;AAAA,MACH,QAAQ,SAAS,aAAa;AAAA,MAC9B,eAAe,SAAS,kBAAkB;AAAA,IAC9C;AAAA,EACJ;AACA,MAAI,gBAAgB,SAAS,GAAG;AAC5B,QAAI,iBAAiB,UAAU,MAAM,GAAG;AAGpC,aAAO;AAAA,QACH,QAAQ,SAAS,UAAU,SAAS,4BAA4B;AAAA,QAChE,eAAe,SAAS,kBAAkB,gBAAgB;AAAA,MAC9D;AAAA,IACJ;AAAA,EACJ;AAEA,SAAO;AAAA,IACH,QAAQ;AAAA,IACR,eAAe,SAAS,kBAAkB;AAAA,EAC9C;AACJ;AAEA,SAAS,mBAAmB,WAAW;AACnC,SAAO,cAAc;AACzB;AAEA,SAAS,gBAAgB,WAAW;AAChC,SAAO,cAAc;AACzB;AAEA,SAAS,iBAAiB,UAAU,QAAQ;AACxC,QAAM,2BAA2B;AACjC,SAAO,SAAS,UAAU,SAAS,wBAAwB,MAAM;AACrE;AAgBO,SAAS,YAAY,UAAU;AAClC,MAAI,kBAAU,YAAY,kBAAU,WAAW,kBAAU,SAAS;AAC9D,UAAM,UAAU,CAAC;AACjB,UAAM,UAAU,YAAY,QAAQ;AAEpC,QAAI,CAAC,SAAS;AACV,aAAO,EAAC,eAAe,MAAK;AAAA,IAChC;AAEA,QAAI,kBAAU,UAAU;AACpB,cAAQ,mBAAmB,eAAe,UAAU,OAAO;AAAA,IAC/D;AACA,QAAI,kBAAU,SAAS;AACnB,cAAQ,YAAY,cAAc,OAAO;AAAA,IAC7C;AACA,QAAI,kBAAU,SAAS;AACnB,cAAQ,YAAY,cAAc,OAAO;AAAA,IAC7C;AACA,YAAQ,gBAAiB,QAAQ,qBAAqB,UAAe,QAAQ,cAAc,UAAe,QAAQ,cAAc;AAChI,WAAO;AAAA,EACX;AAEA,SAAO,CAAC;AACZ;AAEA,SAAS,YAAY,UAAU;AAC3B,QAAM,kBAAkB;AACxB,QAAM,gBAAgB;AAEtB,MAAI,SAAS;AAEb,SAAO,SAAS,kBAAkB,iBAAiB,SAAS,YAAY;AACpE,UAAM,MAAM,SAAS,UAAU,MAAM;AAErC,QAAI,QAAQ,QAAW;AACnB;AAAA,IACJ;AAEA,QAAI,IAAI,SAAS,QAAQ;AACrB,aAAO;AAAA,IACX;AAEA,cAAU,IAAI;AAAA,EAClB;AAEA,SAAO;AACX;AAEA,SAAS,eAAe,UAAU,SAAS;AACvC,MAAI;AACA,UAAM,aAAa,mBAAmB,OAAO,EAAE;AAC/C,UAAM,WAAW,aAAa,SAAS,UAAU;AACjD,UAAM,aAAa,SAAS,aAAa,SAAS,QAAQ,GAAG;AAC7D,WAAO,oBAAoB,UAAU,UAAU;AAAA,EACnD,SAAS,OAAP;AACE,WAAO;AAAA,EACX;AACJ;AAEA,SAAS,mBAAmB,SAAS;AACjC,SAAO,QAAQ,SAAS,KAAK,CAAC,QAAQ,IAAI,SAAS,MAAM,EAAE,UAAU,KAAK,CAAC,aAAa,SAAS,aAAa,mBAAmB;AACrI;AAEA,SAAS,aAAa,SAAS,QAAQ;AACnC,SAAO,QAAQ,SAAS,KAAK,CAAC,QAAQ,IAAI,SAAS,MAAM,EAAE,MAAM,KAAK,CAAC,SAAS,KAAK,WAAW,MAAM;AAC1G;AAEA,SAAS,oBAAoB,UAAU,YAAY;AAG/C,QAAM,0BAA0B;AAChC,SAAO,aAAa,0BAA0B,SAAS,UAAU,UAAU;AAC/E;AAEA,SAAS,cAAc,SAAS;AAC5B,MAAI;AACA,UAAM,YAAY,kBAAkB,OAAO,EAAE;AAC7C,UAAM,WAAW,aAAa,SAAS,SAAS;AAChD,UAAM,iBAAiB,aAAa,SAAS,SAAS,EAAE,QAAQ;AAChE,WAAO;AAAA,MACH;AAAA,QACI,YAAY,SAAS,aAAa,eAAe;AAAA,QACjD,QAAQ,eAAe;AAAA,MAC3B;AAAA,IACJ;AAAA,EACJ,SAAS,OAAP;AACE,WAAO;AAAA,EACX;AACJ;AAEA,SAAS,kBAAkB,SAAS;AAChC,SAAO,QAAQ,SAAS,KAAK,CAAC,QAAQ,IAAI,SAAS,MAAM,EACpD,UAAU,KAAK,CAAC,aAAa,SAAS,aAAa,uBAAuB,SAAS,gBAAgB,qBAAqB;AACjI;AAEA,SAAS,cAAc,SAAS;AAM5B,MAAI;AACA,UAAM,MAAM,QAAQ,SAAS,KAAK,CAAC,QAAQ,IAAI,SAAS,MAAM,EACzD,SAAS,KAAK,CAAC,QAAQ,IAAI,SAAS,MAAM,EAC1C,WAAW,KAAK,CAAC,QAAQ,IAAI,SAAS,MAAM,EAC5C;AACL,QAAI,KAAK;AACL,aAAO,CAAC,GAAG;AAAA,IACf;AAAA,EACJ,SAAS,OAAP;AAAA,EAEF;AACA,SAAO;AACX;AAEA,SAAS,iBAAiB,UAAU,eAAe,WAAW;AAC1D,QAAM,mBAAmB;AACzB,QAAM,aAAa,sBAAsB,UAAU,eAAe,gBAAgB;AAElF,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA,QAAQ;AAAA,EACZ;AACJ;AAEA,SAAS,uBAAuB,UAAU,aAAa,eAAe,QAAQ;AAC1E,SAAO;AAAA,IACH,MAAM;AAAA,IACN,UAAU,cAAc,UAAU,eAAe,UAAU,gBAAgB,YAAY;AAAA,IACvF;AAAA,EACJ;AACJ;AAEA,SAAS,8BAA8B,UAAU,aAAa,eAAe,QAAQ;AACjF,SAAO;AAAA,IACH,MAAM;AAAA,IACN,YAAY,cAAc,UAAU,eAAe,UAAU,gBAAgB,YAAY;AAAA,IACzF;AAAA,EACJ;AACJ;AAEA,SAAS,yBAAyB,UAAU,eAAe,QAAQ;AAC/D,SAAO;AAAA,IACH,MAAM;AAAA,IACN,KAAK,SAAS,UAAU,aAAa;AAAA,IACrC;AAAA,EACJ;AACJ;AAEA,SAAS,SAAS,UAAU,eAAe;AACvC,QAAM,kBAAkB;AAExB,QAAM,YAAY,sBAAsB,UAAU,eAAe,eAAe;AAChF,MAAI,cAAc,UAAU,cAAc,QAAQ;AAI9C,WAAO;AAAA,EACX;AAEA,SAAO;AAAA,IACH,QAAQ,gBAAgB;AAAA,IACxB,QAAQ,SAAS,UAAU,gBAAgB,eAAe;AAAA,IAC1D,aAAa;AAAA,IACb,aAAa;AAAA,EACjB;AACJ;AAEA,SAAS,iBAAiB,UAAU,aAAa,eAAe,QAAQ;AACpE,QAAM,aAAa;AAEnB,SAAO;AAAA,IACH,MAAM;AAAA,IACN,UAAU,cAAc,UAAU,gBAAgB,YAAY,UAAU,gBAAgB,aAAa,YAAY;AAAA,IACjH;AAAA,EACJ;AACJ;AAQA,SAAS,cAAc,UAAU,QAAQ,QAAQ;AAC7C,QAAM,2BAA2B;AAAA,IAC7B;AAAA,IACA;AAAA,EACJ;AAEA,QAAM,WAAW,CAAC;AAClB,MAAI,gBAAgB;AACpB,SAAO,gBAAgB,SAAS,QAAQ;AACpC,UAAM,MAAM,SAAS,UAAU,aAAa;AAC5C,QAAI,QAAQ,QAAW;AACnB;AAAA,IACJ;AACA,QAAI,IAAI,SAAS,WAAc,IAAI,aAAa,UAAa,yBAAyB,QAAQ,IAAI,QAAQ,MAAM,KAAK;AACjH,eAAS,KAAK,GAAG;AAAA,IACrB;AACA,qBAAiB,IAAI;AAAA,EACzB;AACA,SAAO;AACX;AAEA,SAAS,wBAAwB,UAAU,aAAa,SAAS,eAAe,QAAQ;AACpF,QAAM,EAAC,QAAO,IAAI,qCAAqC,SAAS,aAAa;AAE7E,SAAO;AAAA,IACH,MAAM;AAAA,IACN,WAAW,cAAc,UAAU,QAAQ,WAAW,UAAU,QAAQ,YAAY,YAAY;AAAA,IAChG;AAAA,EACJ;AACJ;AAEA,SAAS,qCAAqC,SAAS,eAAe;AAClE,QAAM,aAAa;AAEnB,QAAM,UAAU,EAAC,YAAY,gBAAgB,WAAU;AACvD,QAAM,QAAQ,CAAC;AAEf,MAAI,YAAY,GAAG;AACf,UAAM,aAAa;AAAA,EACvB,OAAO;AACH,UAAM,aAAa;AAAA,EACvB;AAEA,UAAQ,YAAY,QAAQ,aAAa,MAAM;AAE/C,SAAO,EAAC,QAAO;AACnB;AAEA,SAAS,6BAA6B,UAAU,aAAa,SAAS,eAAe,QAAQ;AACzF,QAAM,aAAa;AAEnB,mBAAiB;AACjB,QAAM,QAAQ,EAAC,MAAM,QAAQ,OAAM;AAEnC,MAAI,YAAY,KAAK,YAAY,GAAG;AAChC,UAAM,SAAS,SAAS,UAAU,aAAa;AAC/C,qBAAiB;AACjB,UAAM,sBAAsB,SAAS,UAAU,aAAa;AAC5D,qBAAiB;AACjB,UAAM,WAAW,oCAAoC,UAAU,aAAa;AAC5E,qBAAiB,MAAM,SAAS,SAAS;AAAA,EAM7C;AAwBA,MAAI,WAAW,GAAG;AACd,QAAI,YAAY,GAAG;AACf,YAAM,SAAS,SAAS,UAAU,aAAa;AAC/C,uBAAiB;AAAA,IACrB,WAAW,YAAY,GAAG;AACtB,YAAM,SAAS,SAAS,UAAU,aAAa;AAC/C,uBAAiB;AAAA,IACrB;AACA,UAAM,sBAAsB,SAAS,UAAU,aAAa;AAC5D,qBAAiB;AAEjB,UAAM,WAAW,SAAS,UAAU,aAAa;AACjD,qBAAiB;AACjB,UAAM,WAAW,oCAAoC,UAAU,aAAa;AAC5E,qBAAiB,MAAM,SAAS,SAAS;AACzC,QAAI,MAAM,aAAa,qBAAqB;AACxC,YAAM,cAAc,oCAAoC,UAAU,aAAa;AAC/E,uBAAiB,MAAM,YAAY,SAAS;AAC5C,UAAI,cAAc,SAAS,eAAe;AACtC,cAAM,kBAAkB,oCAAoC,UAAU,aAAa;AACnF,yBAAiB,MAAM,gBAAgB,SAAS;AAAA,MACpD;AAAA,IACJ,WAAW,MAAM,aAAa,oBAAoB;AAC9C,YAAM,UAAU,oCAAoC,UAAU,aAAa;AAC3E,uBAAiB,MAAM,QAAQ,SAAS;AAAA,IAC5C;AAAA,EACJ;AACA,SAAO;AACX;;;ACvcA,IAAO,4BAAQ;AAAA,EACX;AAAA,EACA;AACJ;AAQA,SAAS,WAAW,UAAU;AAC1B,MAAI,CAAC,UAAU;AACX,WAAO;AAAA,EACX;AAEA,QAAM,oBAAoB,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAEjG,MAAI;AACA,UAAM,YAAY,SAAS,UAAU,CAAC;AACtC,WAAO,aAAa,kBAAkB,QAAQ,UAAU,UAAU,MAAM;AAAA,EAC5E,SAAS,OAAP;AACE,WAAO;AAAA,EACX;AACJ;AAQA,SAAS,gBAAgB,UAAU;AAC/B,SAAO,YAAY,QAAQ;AAC/B;;;AC/BA,IAAO,4BAAQ;AAAA,EACX;AAAA,EACA;AACJ;AAQA,SAAS,WAAW,UAAU;AAC1B,MAAI,CAAC,UAAU;AACX,WAAO;AAAA,EACX;AAEA,MAAI;AACA,UAAM,YAAY,SAAS,UAAU,CAAC;AACtC,WAAO,aAAa,UAAU,eAAe;AAAA,EACjD,SAAS,OAAP;AACE,WAAO;AAAA,EACX;AACJ;AAQA,SAAS,gBAAgB,UAAU;AAC/B,SAAO,YAAY,QAAQ;AAC/B;;;AClCA,IAAO,4BAAQ;AAAA,EACX;AAAA,EACA,aAAAC;AACJ;AAEA,SAAS,WAAW,UAAU;AAC1B,QAAM,iBAAiB;AACvB,QAAM,UAAU;AAChB,QAAM,qBAAqB;AAC3B,QAAM,cAAc;AAEpB,SAAO,CAAC,CAAC,YAAY,sBAAsB,UAAU,gBAAgB,QAAQ,MAAM,MAAM,WAClF,sBAAsB,UAAU,oBAAoB,YAAY,MAAM,MAAM;AACvF;AAEA,SAASA,aAAY,UAAU;AAC3B,QAAM,yBAAyB;AAC/B,QAAM,oBAAoB;AAC1B,QAAM,kBAAkB;AACxB,QAAM,oBAAoB;AAE1B,MAAI,SAAS;AACb,MAAI,gBAAgB;AACpB,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,SAAO,SAAS,oBAAoB,SAAS,YAAY;AACrD,UAAM,UAAU,sBAAsB,UAAU,QAAQ,CAAC;AACzD,UAAM,YAAY,SAAS,UAAU,SAAS,mBAAmB,IAAI;AAErE,QAAI,kBAAU,YAAa,YAAY,QAAS;AAC5C,sBAAgB;AAChB,UAAI,sBAAsB,UAAU,SAAS,mBAAmB,gBAAgB,MAAM,MAAM,iBAAiB;AACzG,2BAAmB,SAAS,oBAAoB,gBAAgB;AAAA,MACpE,OAAO;AACH,2BAAmB,SAAS;AAAA,MAChC;AAAA,IACJ,WAAW,kBAAU,WAAY,YAAY,QAAS;AAClD,sBAAgB;AAChB,kBAAY,CAAC;AAAA,QACT,YAAY,SAAS;AAAA,QACrB,QAAQ;AAAA,MACZ,CAAC;AAAA,IACL,WAAW,kBAAU,WAAY,YAAY,QAAS;AAClD,sBAAgB;AAChB,kBAAY,CAAC;AAAA,QACT,QAAQ,SAAS;AAAA,QACjB,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,aAAa;AAAA,MACjB,CAAC;AAAA,IACL,WAAW,YAAY,QAAQ;AAC3B,sBAAgB;AAChB,wBAAkB,SAAS;AAAA,IAC/B;AAEA,cAAU,qBAAqB,YAAY,MAAM,IAAI,YAAY,YAAY;AAAA,EACjF;AAEA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;;;ACrEA,IAAO,2BAAQ;AAAA,EACX;AAAA,EACA,aAAAC;AACJ;AAEA,IAAM,qBAAqB;AAC3B,IAAM,iBAAiB,CAAC,UAAU,QAAQ;AAE1C,SAAS,UAAU,UAAU;AACzB,SAAO,CAAC,CAAC,YAAY,eAAe,SAAS,sBAAsB,UAAU,GAAG,kBAAkB,CAAC;AACvG;AAEA,SAASA,eAAc;AACnB,SAAO;AAAA,IACH,iBAAiB;AAAA,EACrB;AACJ;;;AChBA,IAAO,cAAQ;AAAA,EACX;AAAA,EACA,aAAAC;AACJ;AAEA,IAAM,oBAAoB;AAC1B,IAAM,aAAa;AAEnB,SAAS,UAAU,UAAU;AACzB,SAAO,CAAC,CAAC,YAAY,sBAAsB,UAAU,mBAAmB,WAAW,MAAM,MAAM;AACnG;AAEA,SAASA,aAAY,UAAU;AAC3B,QAAM,YAAY,CAAC;AACnB,YAAU,KAAK,EAAC,YAAY,mBAAmB,QAAQ,SAAS,WAAU,CAAC;AAC3E,SAAO;AAAA,IACH;AAAA,EACJ;AACJ;;;ACTA,IAAO,uBAAQ;AAAA,EACX;AACJ;AAEA,SAAS,gBAAgB,UAAU,OAAO;AACtC,MAAI,kBAAU,YAAY,0BAAK,WAAW,QAAQ,GAAG;AACjD,WAAO,YAAY,0BAAK,gBAAgB,GAAG,QAAQ,MAAM;AAAA,EAC7D;AAEA,MAAI,kBAAU,YAAY,0BAAK,WAAW,QAAQ,GAAG;AACjD,WAAO,YAAY,0BAAK,gBAAgB,QAAQ,GAAG,QAAQ,MAAM;AAAA,EACrE;AAEA,MAAI,kBAAU,WAAW,yBAAI,UAAU,QAAQ,GAAG;AAC9C,WAAO,YAAY,yBAAI,eAAe,UAAU,KAAK,GAAG,OAAO,KAAK;AAAA,EACxE;AAEA,MAAI,kBAAU,YAAY,0BAAK,WAAW,QAAQ,GAAG;AACjD,WAAO,YAAY,0BAAK,gBAAgB,QAAQ,GAAG,QAAQ,MAAM;AAAA,EACrE;AAEA,MAAI,kBAAU,YAAY,0BAAK,WAAW,QAAQ,GAAG;AACjD,WAAO,YAAY,0BAAK,gBAAgB,QAAQ,GAAG,QAAQ,MAAM;AAAA,EACrE;AAEA,MAAI,kBAAU,YAAY,0BAAK,WAAW,QAAQ,GAAG;AACjD,WAAO,YAAY,0BAAK,YAAY,QAAQ,GAAG,QAAQ,MAAM;AAAA,EACjE;AAEA,MAAI,kBAAU,WAAW,yBAAI,UAAU,QAAQ,GAAG;AAC9C,WAAO,YAAY,yBAAI,YAAY,QAAQ,GAAG,OAAO,KAAK;AAAA,EAC9D;AAEA,MAAI,kBAAU,WAAW,YAAI,UAAU,QAAQ,GAAG;AAC9C,WAAO,YAAY,YAAI,YAAY,QAAQ,GAAG,OAAO,KAAK;AAAA,EAC9D;AAEA,QAAM,IAAI,MAAM,sBAAsB;AAC1C;AAEA,SAAS,YAAY,SAAS,UAAU,qBAAqB;AACzD,SAAO,aAAa,CAAC,GAAG,SAAS,EAAC,UAAU,EAAC,OAAO,UAAU,aAAa,oBAAmB,EAAC,CAAC;AACpG;;;ACrDA,IAAO,2BAAQ;AAAA,EACX,eAAe,CAAC,UAAU,KAAK,IAAI,KAAK,KAAK,CAAC,GAAG,MAAM,KAAK,MAAM,EAAE,EAAE,QAAQ,CAAC;AAAA,EAC/E,WAAW,OAAO;AACd,QAAI,UAAU,GAAG;AACb,aAAO;AAAA,IACX,WAAW,UAAU,OAAQ;AACzB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,wBAAwB,OAAO;AAC3B,WAAO,MAAM,IAAI,CAAC,cAAc;AAC5B,UAAI,cAAc,IAAM;AACpB,eAAO;AAAA,MACX,WAAW,cAAc,IAAM;AAC3B,eAAO;AAAA,MACX,WAAW,cAAc,IAAM;AAC3B,eAAO;AAAA,MACX,WAAW,cAAc,IAAM;AAC3B,eAAO;AAAA,MACX,WAAW,cAAc,IAAM;AAC3B,eAAO;AAAA,MACX,WAAW,cAAc,IAAM;AAC3B,eAAO;AAAA,MACX;AAAA,IACJ,CAAC,EAAE,KAAK,EAAE;AAAA,EACd;AAAA,EACA,SAAS,OAAO;AACZ,QAAI,UAAU,GAAG;AACb,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,eAAe,OAAO;AAClB,QAAI,UAAU,GAAG;AACb,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,aAAa,OAAO;AAChB,QAAI,UAAU,GAAG;AACb,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,gBAAgB,OAAO;AACnB,QAAI,UAAU,GAAG;AACb,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,aAAa,OAAO;AAChB,QAAI,MAAM,KAAK,MAAM,KAAK,MAAM;AAC5B,YAAM,UAAU,MAAM,KAAK,MAAM;AACjC,UAAI,OAAO,UAAU,OAAO,GAAG;AAC3B,eAAO,KAAK;AAAA,MAChB;AACA,aAAO,QAAQ,QAAQ,CAAC;AAAA,IAC5B;AACA,QAAI,MAAM,OAAO,GAAG;AAChB,aAAO,KAAK,KAAK,MAAM,MAAM,KAAK,MAAM,EAAE;AAAA,IAC9C;AACA,WAAO,KAAK,MAAM;AAAA,EACtB;AAAA,EACA,SAAS,CAAC,UAAU,KAAK,MAAM,KAAK,MAAM;AAAA,EAC1C,aAAa,CAAC,UAAW,MAAM,KAAK,MAAM,KAAM;AAAA,EAChD,yBAAyB,OAAO;AAC5B,QAAI,UAAU,GAAG;AACb,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,aAAa,CAAC,UAAU;AACpB,QAAI,UAAU,GAAG;AACb,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX,WAAW,UAAU,IAAI;AACrB,aAAO;AAAA,IACX,WAAW,UAAU,IAAI;AACrB,aAAO;AAAA,IACX,WAAW,UAAU,IAAI;AACrB,aAAO;AAAA,IACX,WAAW,UAAU,IAAI;AACrB,aAAO;AAAA,IACX,WAAW,UAAU,IAAI;AACrB,aAAO;AAAA,IACX,WAAW,UAAU,IAAI;AACrB,aAAO;AAAA,IACX,WAAW,UAAU,IAAI;AACrB,aAAO;AAAA,IACX,WAAW,UAAU,IAAI;AACrB,aAAO;AAAA,IACX,WAAW,UAAU,IAAI;AACrB,aAAO;AAAA,IACX,WAAW,UAAU,IAAI;AACrB,aAAO;AAAA,IACX,WAAW,UAAU,IAAI;AACrB,aAAO;AAAA,IACX,WAAW,UAAU,IAAI;AACrB,aAAO;AAAA,IACX,WAAW,UAAU,IAAI;AACrB,aAAO;AAAA,IACX,WAAW,UAAU,IAAI;AACrB,aAAO;AAAA,IACX,WAAW,UAAU,KAAK;AACtB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,aAAa,OAAO;AAChB,QAAI,UAAU,GAAG;AACb,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX,WAAW,UAAU,KAAK;AACtB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,eAAe,OAAO;AAClB,QAAI,UAAU,GAAG;AACb,aAAO;AAAA,IACX;AACA,QAAI,UAAU,GAAG;AACb,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,WAAW,OAAO;AACd,QAAI,UAAU,GAAG;AACb,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,iBAAiB,OAAO;AACpB,QAAI,UAAU,GAAG;AACb,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,UAAU,OAAO;AACb,QAAI,UAAU,GAAG;AACb,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,kBAAkB,OAAO;AACrB,UAAM,cAAc,KAAK,IAAI,GAAG,MAAM,KAAK,MAAM,EAAE;AACnD,QAAI,eAAe,GAAG;AAClB,aAAO,GAAG,KAAK,MAAM,IAAI,WAAW;AAAA,IACxC;AACA,WAAO,KAAK,KAAK,MAAM,WAAW;AAAA,EACtC;AAAA,EACA,aAAa,OAAO;AAChB,QAAI,UAAU,GAAG;AACb,aAAO;AAAA,IACX,WAAW,UAAU,GAAG;AACpB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,aAAa,CAAC,UAAU,KAAK,KAAK,MAAM,MAAM,KAAK,MAAM,EAAE;AAAA,EAC3D,aAAa,CAAC,UAAU,KAAK,KAAK,MAAM,MAAM,KAAK,MAAM,EAAE;AAC/D;;;AC5NA,IAAO,4BAAQ;AAAA,EACX,IAAQ;AAAA,EACR,KAAQ;AAAA,IACJ,MAAM;AAAA,IACN,aAAa,CAAC,WAAW;AAAA,MACrB,GAAK;AAAA,MACL,GAAK;AAAA,MACL,GAAK;AAAA,MACL,GAAK;AAAA,MACL,GAAK;AAAA,MACL,GAAK;AAAA,MACL,GAAK;AAAA,MACL,GAAK;AAAA,MACL,OAAS;AAAA,MACT,YAAY;AAAA,IAChB,GAAG,UAAU;AAAA,EACjB;AAAA,EACA,KAAQ;AAAA,IACJ,MAAM;AAAA,IACN,aAAa,CAAC,WAAW;AAAA,MACrB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACP,GAAG,UAAU;AAAA,EACjB;AAAA,EACA,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,IACJ,MAAM;AAAA,IACN,aAAa,CAAC,WAAW;AAAA,MACrB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACP,GAAG,UAAU;AAAA,EACjB;AAAA,EACA,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,IACJ,MAAM;AAAA,IACN,aAAa,CAAC,WAAW;AAAA,MACrB,GAAG;AAAA,MACH,GAAG;AAAA,IACP,GAAG,UAAU;AAAA,EACjB;AAAA,EACA,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,IACJ,MAAM;AAAA,IACN,aAAa,CAAC,UAAU;AACpB,UAAI,UAAU,GAAG;AACb,eAAO;AAAA,MACX;AACA,UAAI,UAAU,GAAG;AACb,eAAO;AAAA,MACX;AACA,UAAI,UAAU,GAAG;AACb,eAAO;AAAA,MACX;AACA,UAAI,UAAU,GAAG;AACb,eAAO;AAAA,MACX;AACA,UAAI,UAAU,GAAG;AACb,eAAO;AAAA,MACX;AACA,UAAI,UAAU,GAAG;AACb,eAAO;AAAA,MACX;AACA,UAAI,UAAU,GAAG;AACb,eAAO;AAAA,MACX;AACA,UAAI,UAAU,GAAG;AACb,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,yBAAe;AAAA,EAClC;AAAA,EACA,KAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,yBAAe;AAAA,EAClC;AAAA,EACA,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,aAAO,KAAK,KAAK,MAAM,MAAM,KAAK,MAAM,EAAE;AAAA,IAC9C;AAAA,EACJ;AAAA,EACA,KAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,aAAO,KAAK,KAAK,MAAM,MAAM,KAAK,MAAM,EAAE;AAAA,IAC9C;AAAA,EACJ;AAAA,EACA,KAAQ;AAAA,IACJ,MAAM;AAAA,IACN,aAAa,CAAC,WAAW;AAAA,MACrB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACP,GAAG,UAAU;AAAA,EACjB;AAAA,EACA,KAAQ;AAAA,IACJ,MAAM;AAAA,IACN,aAAa,yBAAe;AAAA,EAChC;AAAA,EACA,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,WAAW;AACvB,aAAO,OAAO,IAAI,CAAC,UAAU,GAAG,MAAM,MAAM,MAAM,IAAI,EAAE,KAAK,IAAI;AAAA,IACrE;AAAA,EACJ;AAAA,EACA,KAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,WAAW;AACvB,aAAO,OAAO,IAAI,CAAC,UAAU,GAAG,MAAM,MAAM,MAAM,IAAI,EAAE,KAAK,IAAI;AAAA,IACrE;AAAA,EACJ;AAAA,EACA,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,IACJ,MAAM;AAAA,IACN,aAAa,CAAC,WAAW;AAAA,MACrB,GAAG;AAAA,MACH,GAAG;AAAA,IACP,GAAG,UAAU;AAAA,EACjB;AAAA,EACA,KAAQ;AAAA,EACR,KAAQ;AAAA,IACJ,MAAM;AAAA,IACN,aAAa,CAAC,WAAW;AAAA,MACrB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACP,GAAG,UAAU;AAAA,EACjB;AAAA,EACA,KAAQ;AAAA,IACJ,MAAM;AAAA,IACN,aAAa,CAAC,UAAU;AACpB,YAAM,UAAU;AAAA,QACZ,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACP;AACA,UAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACvB,eAAO;AAAA,MACX;AACA,aAAO,MAAM,IAAI,CAAC,WAAW,QAAQ,WAAW,SAAS,EAAE,KAAK,IAAI;AAAA,IACxE;AAAA,EACJ;AAAA,EACA,KAAQ;AAAA,EACR,KAAQ;AAAA,EACR,KAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,WAAW;AACvB,aAAO,OAAO,IAAI,CAAC,UAAU,KAAK,MAAM,KAAK,MAAM,EAAE,EAAE,KAAK,GAAG;AAAA,IACnE;AAAA,EACJ;AAAA,EACA,KAAQ;AAAA,EACR,KAAQ;AAAA,IACJ,MAAM;AAAA,IACN,aAAa,CAAC,UAAU;AACpB,UAAI,UAAU,GAAG;AACb,eAAO;AAAA,MACX;AACA,UAAI,UAAU,GAAG;AACb,eAAO;AAAA,MACX;AACA,aAAO,eAAe;AAAA,IAC1B;AAAA,EACJ;AAAA,EACA,KAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,WAAW;AACvB,aAAO,OAAO,IAAI,CAAC,UAAU,KAAK,MAAM,KAAK,MAAM,EAAE,EAAE,KAAK,IAAI;AAAA,IACpE;AAAA,EACJ;AAAA,EACA,KAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,IACJ,MAAM;AAAA,IACN,aAAa,CAAC,UAAU,MAAM,KAAK,IAAI;AAAA,EAC3C;AAAA,EACA,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,IACJ,MAAM;AAAA,IACN,aAAa,CAAC,WAAW;AAAA,MACrB,GAAG;AAAA,MACH,GAAG;AAAA,IACP,GAAG,UAAU;AAAA,EACjB;AAAA,EACA,OAAQ;AAAA,IACJ,MAAM;AAAA,IACN,aAAa,yBAAe;AAAA,EAChC;AAAA,EACA,OAAQ;AAAA,IACJ,MAAM;AAAA,IACN,aAAa,yBAAe;AAAA,EAChC;AAAA,EACA,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,IACJ,MAAM;AAAA,IACN,aAAa,CAAC,WAAW;AAAA,MACrB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACP,GAAG,UAAU;AAAA,EACjB;AAAA,EACA,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,IACJ,MAAM;AAAA,IACN,aAAa,CAAC,WAAW;AAAA,MACrB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACP,GAAG,UAAU;AAAA,EACjB;AAAA,EACA,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,IACJ,MAAM;AAAA,IACN,aAAa,CAAC,WAAW;AAAA,MACrB,GAAG;AAAA,MACH,GAAG;AAAA,IACP,GAAG,UAAU;AAAA,EACjB;AAAA,EACA,OAAQ;AAAA,IACJ,MAAM;AAAA,IACN,aAAa,CAAC,WAAW;AAAA,MACrB,GAAG;AAAA,MACH,GAAG;AAAA,IACP,GAAG,UAAU;AAAA,EACjB;AAAA,EACA,OAAQ;AAAA,EACR,OAAQ;AAAA,IACJ,MAAM;AAAA,IACN,aAAa,CAAC,WAAW;AAAA,MACrB,GAAG;AAAA,MACH,GAAG;AAAA,IACP,GAAG,UAAU;AAAA,EACjB;AAAA,EACA,OAAQ;AAAA,EACR,OAAQ;AACZ;;;AC7VA,IAAO,6BAAQ;AAAA,EACX,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,yBAAe;AAAA,EAClC;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,yBAAe;AAAA,EAClC;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,yBAAe;AAAA,EAClC;AAAA,EACA,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,MAAM;AAAA,EACzB;AAAA,EACA,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,IACJ,MAAM;AAAA,IACN,aAAa,CAAC,WAAW;AAAA,MACrB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACP,GAAG,UAAU;AAAA,EACjB;AAAA,EACA,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU,eAAe,KAAK;AAAA,EAClD;AAAA,EACA,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,yBAAe;AAAA,EAClC;AAAA,EACA,OAAQ;AAAA,EACR,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,yBAAe;AAAA,EAClC;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,yBAAe;AAAA,EAClC;AAAA,EACA,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,aAAO,KAAK,IAAI,KAAK,KAAK,CAAC,GAAG,MAAM,KAAK,MAAM,EAAE,EAAE,QAAQ,CAAC;AAAA,IAChE;AAAA,EACJ;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAW,MAAM,KAAK,MAAM,KAAM;AAAA,EACtD;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,yBAAe;AAAA,EAClC;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,aAAa,yBAAe;AAAA,EAChC;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,UAAI,UAAU,GAAM;AAChB,eAAO;AAAA,MACX,WAAW,UAAU,GAAM;AACvB,eAAO;AAAA,MACX,WAAW,UAAU,GAAM;AACvB,eAAO;AAAA,MACX,WAAW,UAAU,GAAM;AACvB,eAAO;AAAA,MACX,WAAW,UAAU,GAAM;AACvB,eAAO;AAAA,MACX,WAAW,UAAU,IAAM;AACvB,eAAO;AAAA,MACX,WAAW,UAAU,IAAM;AACvB,eAAO;AAAA,MACX,WAAW,UAAU,IAAM;AACvB,eAAO;AAAA,MACX,WAAW,UAAU,IAAM;AACvB,eAAO;AAAA,MACX,WAAW,UAAU,IAAM;AACvB,eAAO;AAAA,MACX,WAAW,UAAU,IAAM;AACvB,eAAO;AAAA,MACX,WAAW,UAAU,IAAM;AACvB,eAAO;AAAA,MACX,WAAW,UAAU,IAAM;AACvB,eAAO;AAAA,MACX,WAAW,UAAU,IAAM;AACvB,eAAO;AAAA,MACX,WAAW,UAAU,IAAM;AACvB,eAAO;AAAA,MACX,WAAW,UAAU,IAAM;AACvB,eAAO;AAAA,MACX,WAAW,UAAU,IAAM;AACvB,eAAO;AAAA,MACX,WAAW,UAAU,IAAM;AACvB,eAAO;AAAA,MACX,WAAW,UAAU,IAAM;AACvB,eAAO;AAAA,MACX,WAAW,UAAU,IAAM;AACvB,eAAO;AAAA,MACX,WAAW,UAAU,IAAM;AACvB,eAAO;AAAA,MACX,WAAW,UAAU,IAAM;AACvB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,yBAAe;AAAA,EAClC;AAAA,EACA,OAAQ;AAAA,EACR,OAAQ;AAAA,IACJ,MAAM;AAAA,IACN,aAAa,CAAC,WAAW;AAAA,MACrB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACT,GAAG,UAAU;AAAA,EACjB;AAAA,EACA,OAAQ;AAAA,EACR,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,UAAI,MAAM,WAAW,GAAG;AACpB,eAAO,gBAAgB,MAAM,UAAU,MAAM;AAAA,MACjD,WAAW,MAAM,WAAW,GAAG;AAC3B,eAAO,cAAc,MAAM,UAAU,MAAM,iBAAiB,MAAM;AAAA,MACtE,WAAW,MAAM,WAAW,GAAG;AAC3B,eAAO,iBAAiB,MAAM,UAAU,MAAM,cAAc,MAAM,eAAe,MAAM;AAAA,MAC3F;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,MAAM;AAAA,EACzB;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe;AAAA,EACnB;AAAA,EACA,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAW,MAAM,KAAK,MAAM,KAAM;AAAA,EACtD;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAW,MAAM,KAAK,MAAM,KAAM;AAAA,EACtD;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAW,MAAM,KAAK,MAAM,KAAM;AAAA,EACtD;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAW,MAAM,KAAK,MAAM,KAAM;AAAA,EACtD;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAW,MAAM,KAAK,MAAM,KAAM;AAAA,EACtD;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAW,MAAM,KAAK,MAAM,KAAM;AAAA,EACtD;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU,MAAM,IAAI,CAAC,aAAa,OAAO,aAAa,QAAQ,CAAC,EAAE,KAAK,EAAE;AAAA,EAC5F;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,yBAAe;AAAA,EAClC;AAAA,EACA,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,MAAM;AAAA,EACzB;AAAA,EACA,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,yBAAe;AAAA,EAClC;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,CAAC,GAAG,CAAC,MAAM,MAAM,SAAS;AAAA,EAC9C;AAAA,EACA,OAAQ;AAAA,EACR,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,UAAI,UAAU,GAAG;AACb,eAAO;AAAA,MACX,WAAW,UAAU,GAAG;AACpB,eAAO;AAAA,MACX,WAAW,UAAU,GAAG;AACpB,eAAO;AAAA,MACX,WAAW,UAAU,GAAG;AACpB,eAAO;AAAA,MACX,WAAW,UAAU,GAAG;AACpB,eAAO;AAAA,MACX,WAAW,UAAU,GAAG;AACpB,eAAO;AAAA,MACX,WAAW,UAAU,GAAG;AACpB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,UAAI,UAAU,GAAG;AACb,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,UAAI,UAAU,GAAG;AACb,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,MAAM;AAAA,EACzB;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,yBAAe;AAAA,EAClC;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,yBAAe;AAAA,EAClC;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,yBAAe;AAAA,EAClC;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,UAAI,MAAM,OAAO,GAAG;AAChB,eAAO;AAAA,MACX;AACA,aAAO,KAAM,MAAM,KAAK,MAAM;AAAA,IAClC;AAAA,EACJ;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,UAAI,UAAU,GAAG;AACb,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,yBAAe;AAAA,EAClC;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,UAAI,UAAU,GAAG;AACb,eAAO;AAAA,MACX,WAAW,UAAU,GAAG;AACpB,eAAO;AAAA,MACX,WAAW,UAAU,GAAG;AACpB,eAAO;AAAA,MACX,WAAW,UAAU,GAAG;AACpB,eAAO;AAAA,MACX,WAAW,UAAU,GAAG;AACpB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,yBAAe;AAAA,EAClC;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,yBAAe;AAAA,EAClC;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,yBAAe;AAAA,EAClC;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,MAAM;AAAA,EACzB;AAAA,EACA,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,UAAI,UAAU,GAAG;AACb,eAAO;AAAA,MACX,WAAW,UAAU,GAAG;AACpB,eAAO;AAAA,MACX,WAAW,UAAU,GAAG;AACpB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,YAAM,kBAAkB,YAAY,MAAM,GAAG,KAAK,MAAM,GAAG,IAAI,QAAQ,CAAC,CAAC;AACzE,YAAM,gBAAgB,YAAY,MAAM,GAAG,KAAK,MAAM,GAAG,IAAI,QAAQ,CAAC,CAAC;AACvE,YAAM,eAAe,GAAG,mBAAmB;AAC3C,UAAI,MAAM,GAAG,OAAO,GAAG;AACnB,eAAO,GAAG;AAAA,MACd;AACA,YAAM,cAAc,KAAM,MAAM,GAAG,KAAK,MAAM,GAAG,MAAO,MAAM,GAAG,KAAK,MAAM,GAAG;AAC/E,aAAO,GAAG,kBAAkB,WAAW,YAAY,QAAQ,CAAC,CAAC;AAAA,IACjE;AAAA,EACJ;AAAA,EACA,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,IACJ,MAAM;AAAA,IACN,aAAa,CAAC,WAAW;AAAA,MACrB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACP,GAAG,UAAU;AAAA,EACjB;AAAA,EACA,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,MAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AACZ;;;AC3YA,IAAO,4BAAQ;AAAA,EACX,GAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,UAAI,MAAM,OAAO,KAAK,MAAM,OAAO,KAAK,MAAM,OAAO,KAAK,MAAM,OAAO,GAAG;AACtE,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,GAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,YAAM,MAAM,MAAM,KAAK,EAAE;AACzB,UAAI,QAAQ,KAAK;AACb,eAAO;AAAA,MACX,WAAW,QAAQ,KAAK;AACpB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,GAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe;AAAA,EACnB;AAAA,EACA,GAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,YAAM,MAAM,MAAM,KAAK,EAAE;AACzB,UAAI,QAAQ,KAAK;AACb,eAAO;AAAA,MACX,WAAW,QAAQ,KAAK;AACpB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,GAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe;AAAA,EACnB;AAAA,EACA,GAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,UAAI,UAAU,GAAG;AACb,eAAO;AAAA,MACX,WAAW,UAAU,GAAG;AACpB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,GAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,aAAQ,MAAM,KAAK,MAAM,KAAM;AAAA,IACnC;AAAA,EACJ;AAAA,EACA,GAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,WAAW;AACvB,aAAO,OAAO,IAAI,CAAC,CAAC,WAAW,WAAW,MAAM;AAC5C,cAAM,MAAM,YAAY;AACxB,YAAI,YAAY,KAAK,GAAG,KAAK,GAAG;AAC5B,iBAAO,IAAI;AAAA,QACf;AACA,eAAO;AAAA,MACX,CAAC,EAAE,KAAK,GAAG;AAAA,IACf;AAAA,EACJ;AAAA,EACA,GAAQ;AAAA,EACR,GAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,YAAM,SAAS,MAAM,KAAK,EAAE;AAC5B,UAAI,WAAW,KAAK;AAChB,eAAO;AAAA,MACX,WAAW,WAAW,KAAK;AACvB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,IAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,YAAM,OAAO,MAAM,KAAK,EAAE;AAC1B,UAAI,SAAS,KAAK;AACd,eAAO;AAAA,MACX,WAAW,SAAS,KAAK;AACrB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,IAAQ;AAAA,EACR,IAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,YAAM,MAAM,MAAM,KAAK,EAAE;AACzB,UAAI,QAAQ,KAAK;AACb,eAAO;AAAA,MACX,WAAW,QAAQ,KAAK;AACpB,eAAO;AAAA,MACX,WAAW,QAAQ,KAAK;AACpB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,IAAQ;AAAA,EACR,IAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,YAAM,MAAM,MAAM,KAAK,EAAE;AACzB,UAAI,QAAQ,KAAK;AACb,eAAO;AAAA,MACX,WAAW,QAAQ,KAAK;AACpB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,IAAQ;AAAA,EACR,IAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,YAAM,MAAM,MAAM,KAAK,EAAE;AACzB,UAAI,QAAQ,KAAK;AACb,eAAO;AAAA,MACX,WAAW,QAAQ,KAAK;AACpB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,IAAQ;AAAA,EACR,IAAQ;AAAA,EACR,IAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,YAAM,MAAM,MAAM,KAAK,EAAE;AACzB,UAAI,QAAQ,KAAK;AACb,eAAO;AAAA,MACX,WAAW,QAAQ,KAAK;AACpB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,IAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,aAAQ,MAAM,GAAG,KAAK,MAAM,GAAG,KAAO,MAAM,GAAG,KAAK,MAAM,GAAG,KAAM,KAAM,MAAM,GAAG,KAAK,MAAM,GAAG,KAAM;AAAA,IAC1G;AAAA,EACJ;AAAA,EACA,IAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,YAAM,MAAM,MAAM,KAAK,EAAE;AACzB,UAAI,QAAQ,KAAK;AACb,eAAO;AAAA,MACX,WAAW,QAAQ,KAAK;AACpB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,IAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,aAAQ,MAAM,GAAG,KAAK,MAAM,GAAG,KAAO,MAAM,GAAG,KAAK,MAAM,GAAG,KAAM,KAAM,MAAM,GAAG,KAAK,MAAM,GAAG,KAAM;AAAA,IAC1G;AAAA,EACJ;AAAA,EACA,IAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,YAAM,MAAM,MAAM,KAAK,EAAE;AACzB,UAAI,QAAQ,KAAK;AACb,eAAO;AAAA,MACX,WAAW,QAAQ,KAAK;AACpB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,IAAQ;AAAA,EACR,IAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,YAAM,MAAM,MAAM,KAAK,EAAE;AACzB,UAAI,QAAQ,KAAK;AACb,eAAO;AAAA,MACX,WAAW,QAAQ,KAAK;AACpB,eAAO;AAAA,MACX,WAAW,QAAQ,KAAK;AACpB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,IAAQ;AAAA,EACR,IAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe;AAAA,EACnB;AAAA,EACA,IAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe;AAAA,EACnB;AAAA,EACA,IAAQ;AAAA,EACR,IAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AACtB,UAAI,UAAU,GAAG;AACb,eAAO;AAAA,MACX,WAAW,UAAU,GAAG;AACpB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,IAAQ;AACZ;;;AChOA,IAAO,yCAAQ;AAAA,EACX,GAAQ;AAAA,EACR,GAAQ;AAAA,IACJ,MAAM;AAAA,IACN,aAAa,CAAC,UAAU,eAAe,KAAK;AAAA,EAChD;AAAA,EACA,MAAQ;AAAA,EACR,MAAQ;AAAA,EACR,MAAQ;AACZ;;;ACTA,IAAO,4BAAQ;AAAA,EACX,OAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU,eAAe,KAAK;AAAA,EAClD;AAAA,EACA,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AAAA,EACR,OAAQ;AACZ;;;ACXA,IAAO,8BAAQ;AAAA,EACX,GAAQ;AAAA,IACJ,QAAQ;AAAA,IACR,eAAe,CAAC,UAAU;AAAA,EAC9B;AACJ;;;ACIA,IAAM,sBAAsB,aAAa,CAAC,GAAG,2BAAgB,0BAAe;AAErE,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,gBAAgB;AACtB,IAAM,eAAe;AACrB,IAAM,4BAA4B;AAClC,IAAM,eAAe;AACrB,IAAM,iBAAiB;AAE9B,IAAO,oBAAQ;AAAA,EACX,CAAC,eAAe;AAAA,EAChB,CAAC,eAAe;AAAA,EAChB,CAAC,gBAAgB;AAAA,EACjB,CAAC,eAAe;AAAA,EAChB,CAAC,4BAA4B;AAAA,EAC7B,CAAC,eAAe,kBAAU,UAAU,4BAAiB,CAAC;AAAA,EACtD,CAAC,iBAAiB,kBAAU,kBAAkB,8BAAmB,CAAC;AACtE;;;ACzBA,IAAM,YAAY;AAAA,EACd,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,IAAI;AACR;AAEA,IAAM,WAAW;AAAA,EACb,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,SAAS;AAAA,EACT,aAAa;AAAA,EACb,OAAO;AACX;AAEA,IAAO,gBAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AAEA,SAAS,cAAc,WAAW;AAC9B,SAAO,UAAU,IAAI,CAAC,aAAa,OAAO,aAAa,QAAQ,CAAC;AACpE;AAEA,SAAS,UAAU,UAAU,QAAQ;AACjC,SAAO,SAAS,SAAS,MAAM;AACnC;AAEA,SAAS,WAAW,UAAU,QAAQ;AAClC,SAAO,SAAS,SAAS,MAAM;AACnC;AAEA,SAAS,WAAW,UAAU,QAAQ,WAAW;AAC7C,SAAO,SAAS,UAAU,QAAQ,cAAc,mBAAU,aAAa;AAC3E;AAEA,SAAS,UAAU,UAAU,QAAQ,WAAW;AAC5C,SAAO,SAAS,UAAU,QAAQ,cAAc,mBAAU,aAAa;AAC3E;AAEA,SAAS,cAAc,UAAU,QAAQ,WAAW;AAChD,SAAO,CAAC,UAAU,UAAU,QAAQ,SAAS,GAAG,UAAU,UAAU,SAAS,GAAG,SAAS,CAAC;AAC9F;AAEA,SAAS,eAAe,UAAU,QAAQ;AACtC,SAAO,UAAU,UAAU,MAAM;AACrC;AAEA,SAAS,WAAW,UAAU,QAAQ,WAAW;AAC7C,SAAO,SAAS,SAAS,QAAQ,cAAc,mBAAU,aAAa;AAC1E;AAEA,SAAS,eAAe,UAAU,QAAQ,WAAW;AACjD,SAAO,CAAC,WAAW,UAAU,QAAQ,SAAS,GAAG,WAAW,UAAU,SAAS,GAAG,SAAS,CAAC;AAChG;AAEA,SAAS,gBAAgB,UAAU,QAAQ,WAAW;AAClD,SAAO,UAAU,UAAU,QAAQ,SAAS;AAChD;AAEA,SAAS,YAAY,UAAU;AAC3B,MAAI,SAAS,cAAc,QAAW;AAClC,UAAM,IAAI,MAAM,qBAAqB;AAAA,EACzC;AAEA,SAAO,UAAU,SAAS;AAC9B;;;ACpFA,IAAM,gBAAgB;AAAA,EAClB,GAAG,cAAM;AAAA,EACT,GAAG,cAAM;AAAA,EACT,GAAG,cAAM;AAAA,EACT,GAAG,cAAM;AAAA,EACT,GAAG,cAAM;AAAA,EACT,GAAG,cAAM;AAAA,EACT,GAAG,cAAM;AAAA,EACT,IAAI,cAAM;AAAA,EACV,IAAI,cAAM;AACd;AAEO,SAAS,gBAAgB,UAAU,kBAAkB,WAAW;AACnE,SAAO,mBAAmB,cAAM,UAAU,UAAU,mBAAmB,GAAG,SAAS;AACvF;AAEO,SAAS,QAAQ,UAAU,SAAS,kBAAkB,QAAQ,WAAW,gBAAgB;AAC5F,QAAM,mBAAmB,cAAM,YAAY,OAAO;AAClD,QAAM,aAAa;AAEnB,QAAM,OAAO,CAAC;AACd,QAAM,iBAAiB,kBAAkB,UAAU,QAAQ,SAAS;AAEpE,YAAU;AACV,WAAS,aAAa,GAAG,aAAa,gBAAgB,cAAc;AAChE,QAAI,SAAS,aAAa,SAAS,YAAY;AAC3C;AAAA,IACJ;AAEA,UAAM,MAAM,QAAQ,UAAU,SAAS,kBAAkB,QAAQ,WAAW,cAAc;AAC1F,QAAI,QAAQ,QAAW;AACnB,WAAK,IAAI,QAAQ;AAAA,QACb,MAAM,IAAI;AAAA,QACV,SAAS,IAAI;AAAA,QACb,eAAe,IAAI;AAAA,MACvB;AACA,UAAI,IAAI,SAAS,aAAa;AAC1B,aAAK,IAAI,MAAM,WAAW,IAAI;AAAA,MAClC;AAAA,IACJ;AAEA,cAAU;AAAA,EACd;AAEA,MAAI,kBAAU,iBAAkB,SAAS,SAAS,aAAa,cAAM,YAAY,MAAM,GAAI;AACvF,UAAM,gBAAgB,cAAM,UAAU,UAAU,QAAQ,SAAS;AACjE,QAAI,kBAAkB,KAAK,YAAY,cAAc;AACjD,WAAK,eAAe,QAAQ,UAAU,cAAc,kBAAkB,mBAAmB,eAAe,WAAW,cAAc;AAAA,IACrI;AAAA,EACJ;AAEA,SAAO;AACX;AAEA,SAAS,kBAAkB,UAAU,QAAQ,WAAW;AACpD,MAAI,SAAS,cAAM,YAAY,OAAO,KAAK,SAAS,YAAY;AAC5D,WAAO,cAAM,WAAW,UAAU,QAAQ,SAAS;AAAA,EACvD;AACA,SAAO;AACX;AAEA,SAAS,QAAQ,UAAU,SAAS,kBAAkB,QAAQ,WAAW,gBAAgB;AACrF,QAAM,oBAAoB;AAC1B,QAAM,kBAAkB,cAAM,YAAY,OAAO;AACjD,QAAM,mBAAmB,kBAAkB,cAAM,YAAY,OAAO;AACpE,QAAM,mBAAmB,mBAAmB,cAAM,YAAY,MAAM;AAEpE,QAAM,UAAU,cAAM,WAAW,UAAU,QAAQ,SAAS;AAC5D,QAAM,UAAU,cAAM,WAAW,UAAU,SAAS,iBAAiB,SAAS;AAC9E,QAAM,WAAW,cAAM,UAAU,UAAU,SAAS,kBAAkB,SAAS;AAC/E,MAAI;AACJ,MAAI;AAEJ,MAAI,cAAM,UAAU,aAAa,UAAc,CAAC,kBAAkB,kBAAS,SAAS,aAAa,QAAY;AACzG,WAAO;AAAA,EACX;AAEA,MAAI,yBAAyB,SAAS,QAAQ,GAAG;AAC7C,qBAAiB,SAAS;AAC1B,eAAW,YAAY,UAAU,gBAAgB,SAAS,UAAU,SAAS;AAAA,EACjF,OAAO;AACH,qBAAiB,cAAM,UAAU,UAAU,SAAS,kBAAkB,SAAS;AAC/E,QAAI,uBAAuB,UAAU,kBAAkB,gBAAgB,SAAS,QAAQ,GAAG;AACvF,YAAM,gBAAgB,YAAY;AAClC,iBAAW,YAAY,UAAU,mBAAmB,gBAAgB,SAAS,UAAU,WAAW,aAAa;AAAA,IACnH,OAAO;AACH,iBAAW;AAAA,IACf;AAAA,EACJ;AAEA,MAAI,YAAY,cAAM,SAAS,UAAU;AACrC,eAAW,8BAA8B,QAAQ;AACjD,eAAW,iBAAiB,QAAQ;AAAA,EACxC;AAEA,MAAI,UAAU,aAAa;AAC3B,MAAI,iBAAiB;AAErB,MAAI,kBAAS,SAAS,aAAa,QAAW;AAC1C,QAAK,kBAAS,SAAS,SAAS,YAAY,UAAe,kBAAS,SAAS,SAAS,mBAAmB,QAAY;AACjH,gBAAU,kBAAS,SAAS,SAAS;AACrC,UAAI;AACA,yBAAiB,kBAAS,SAAS,SAAS,eAAe,QAAQ;AAAA,MACvE,SAAS,OAAP;AACE,yBAAiB,2BAA2B,QAAQ;AAAA,MACxD;AAAA,IACJ,WAAY,YAAY,cAAM,SAAS,eAAiB,YAAY,cAAM,SAAS,cAAe;AAC9F,gBAAU,kBAAS,SAAS;AAC5B,uBAAiB,KAAM,SAAS,KAAK,SAAS;AAAA,IAClD,OAAO;AACH,gBAAU,kBAAS,SAAS;AAC5B,uBAAiB,2BAA2B,QAAQ;AAAA,IACxD;AAAA,EACJ;AAEA,SAAO;AAAA,IACH,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,OAAO;AAAA,IACP,aAAa;AAAA,IACb,UAAU;AAAA,EACd;AACJ;AAEA,SAAS,yBAAyB,SAAS,UAAU;AACjD,SAAO,cAAM,UAAU,WAAW,YAAY,cAAM,YAAY,MAAM;AAC1E;AAEA,SAAS,YAAY,UAAU,QAAQ,MAAM,OAAO,WAAW,gBAAgB,OAAO;AAClF,MAAI,QAAQ,CAAC;AAEb,MAAI,eAAe;AACf,YAAQ,QAAQ,cAAM,UAAU;AAChC,WAAO,cAAM,SAAS;AAAA,EAC1B;AACA,WAAS,aAAa,GAAG,aAAa,OAAO,cAAc;AACvD,UAAM,KAAK,cAAc,MAAM,UAAU,QAAQ,SAAS,CAAC;AAC3D,cAAU,cAAM,UAAU;AAAA,EAC9B;AAEA,MAAI,SAAS,cAAM,SAAS,UAAU;AAClC,YAAQ,cAAM,cAAc,KAAK;AAAA,EACrC,WAAW,MAAM,WAAW,GAAG;AAC3B,YAAQ,MAAM;AAAA,EAClB;AAEA,SAAO;AACX;AAEA,SAAS,uBAAuB,UAAU,kBAAkB,gBAAgB,SAAS,UAAU;AAC3F,SAAO,mBAAmB,iBAAiB,cAAM,UAAU,WAAW,YAAY,SAAS;AAC/F;AAEA,SAAS,8BAA8B,QAAQ;AAC3C,QAAM,WAAW,CAAC;AAClB,MAAI,IAAI;AAER,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,QAAI,OAAO,OAAO,MAAQ;AACtB;AACA;AAAA,IACJ;AACA,QAAI,SAAS,OAAO,QAAW;AAC3B,eAAS,KAAK;AAAA,IAClB;AACA,aAAS,MAAM,OAAO;AAAA,EAC1B;AAEA,SAAO;AACX;AAEA,SAAS,iBAAiB,YAAY;AAClC,MAAI;AACA,WAAO,WAAW,IAAI,CAAC,UAAU,mBAAmB,OAAO,KAAK,CAAC,CAAC;AAAA,EACtE,SAAS,OAAP;AACE,WAAO;AAAA,EACX;AACJ;AAEA,SAAS,2BAA2B,UAAU;AAC1C,MAAI,oBAAoB,OAAO;AAC3B,WAAO,SAAS,KAAK,IAAI;AAAA,EAC7B;AACA,SAAO;AACX;;;ACvLA,IAAM,uBAAuB;AAC7B,IAAM,2BAA2B;AACjC,IAAM,mCAAmC;AAEzC,IAAO,eAAQ;AAAA,EACX;AACJ;AAEA,SAAS,KAAK,UAAU,kBAAkB,gBAAgB;AACtD,QAAM,YAAY,mBAAU,aAAa,UAAU,gBAAgB;AACnE,MAAI,OAAO,WAAW,UAAU,kBAAkB,WAAW,cAAc;AAC3E,SAAO,YAAY,MAAM,UAAU,kBAAkB,WAAW,cAAc;AAC9E,SAAO,WAAW,MAAM,UAAU,kBAAkB,WAAW,cAAc;AAC7E,SAAO,wBAAwB,MAAM,UAAU,kBAAkB,WAAW,cAAc;AAE1F,SAAO,EAAC,MAAM,UAAS;AAC3B;AAEA,SAAS,WAAW,UAAU,kBAAkB,WAAW,gBAAgB;AACvE,SAAO,QAAQ,UAAU,cAAc,kBAAkB,gBAAgB,UAAU,kBAAkB,SAAS,GAAG,WAAW,cAAc;AAC9I;AAEA,SAAS,YAAY,MAAM,UAAU,kBAAkB,WAAW,gBAAgB;AAC9E,MAAI,KAAK,0BAA0B,QAAW;AAC1C,WAAO,aAAa,MAAM,QAAQ,UAAU,eAAe,kBAAkB,mBAAmB,KAAK,sBAAsB,OAAO,WAAW,cAAc,CAAC;AAAA,EAChK;AAEA,SAAO;AACX;AAEA,SAAS,WAAW,MAAM,UAAU,kBAAkB,WAAW,gBAAgB;AAC7E,MAAI,KAAK,8BAA8B,QAAW;AAC9C,WAAO,aAAa,MAAM,QAAQ,UAAU,cAAc,kBAAkB,mBAAmB,KAAK,0BAA0B,OAAO,WAAW,cAAc,CAAC;AAAA,EACnK;AAEA,SAAO;AACX;AAEA,SAAS,wBAAwB,MAAM,UAAU,kBAAkB,WAAW,gBAAgB;AAC1F,MAAI,KAAK,sCAAsC,QAAW;AACtD,WAAO,aAAa,MAAM,QAAQ,UAAU,2BAA2B,kBAAkB,mBAAmB,KAAK,kCAAkC,OAAO,WAAW,cAAc,CAAC;AAAA,EACxL;AAEA,SAAO;AACX;;;AC3CA,IAAO,mBAAQ;AAAA,EACX,MAAAC;AACJ;AAEA,IAAM,aAAa;AAEnB,SAASA,MAAK,UAAU,YAAY,gBAAgB;AAChD,QAAM,YAAY,mBAAU,aAAa,UAAU,UAAU;AAC7D,QAAM,OAAO,QAAQ,UAAU,cAAc,YAAY,gBAAgB,UAAU,YAAY,SAAS,GAAG,WAAW,cAAc;AACpI,SAAO,aAAa,UAAU,YAAY,MAAM,SAAS;AAC7D;AAEA,SAAS,aAAa,UAAU,YAAY,MAAM,WAAW;AACzD,MAAI,CAAC,KAAK,YAAY;AAClB,WAAO;AAAA,EACX;AAEA,QAAM,SAAS,CAAC;AAChB,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK,KAAK,WAAW,MAAM,SAAS,UAAU,GAAG,KAAK;AAC3E,WAAO,KAAK,CAAC;AAEb,UAAM,aAAa,oBAAoB,KAAK,WAAW,OAAO,IAAI,YAAY,cAAM,YAAY,MAAM,GAAG,SAAS;AAClH,WAAO,GAAG,gBAAgB,cAAc,UAAU;AAClD,WAAO,GAAG,iBAAiB,eAAe,UAAU;AACpD,WAAO,GAAG,eAAe,aAAa,UAAU;AAEhD,UAAM,YAAY,oBAAoB,KAAK,WAAW,OAAO,IAAI,aAAa,GAAG,cAAM,YAAY,MAAM,GAAG,SAAS;AACrH,WAAO,GAAG,eAAe;AAAA,MACrB,OAAO;AAAA,MACP,aAAa,KAAK;AAAA,IACtB;AAEA,UAAM,cAAc,eAAe,GAAG,KAAK,YAAY,WAAW,UAAU;AAC5E,WAAO,GAAG,iBAAiB;AAAA,MACvB,OAAO;AAAA,MACP,aAAa,KAAK;AAAA,IACtB;AAEA,UAAM,6BACF,oBAAoB,KAAK,WAAW,OAAO,IAAI,aAAa,IAAI,cAAM,YAAY,OAAO,GAAG,SAAS;AACzG,WAAO,GAAG,gCAAgC;AAAA,MACtC,OAAO;AAAA,MACP,aAAa,KAAK;AAAA,IACtB;AAEA,UAAM,6BACF,oBAAoB,KAAK,WAAW,OAAO,IAAI,aAAa,IAAI,cAAM,YAAY,OAAO,GAAG,SAAS;AACzG,WAAO,GAAG,gCAAgC;AAAA,MACtC,OAAO;AAAA,MACP,aAAa,KAAK;AAAA,IACtB;AAEA,WAAO,GAAG,QAAQ,SAAS,OAAO,MAAM,aAAa,cAAc,SAAS;AAC5E,cAAU,OAAO,IAAI,UAAU,WAAY;AACvC,aAAO,eAAe,KAAK,KAAK;AAAA,IACpC,CAAC;AAAA,EACL;AAEA,OAAK,YAAY;AAEjB,SAAO;AACX;AAEA,SAAS,oBAAoB,SAAS,QAAQ,MAAM,WAAW;AAC3D,MAAI,cAAc,mBAAU,eAAe;AACvC,QAAIC,SAAQ;AACZ,aAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC3B,MAAAA,UAAS,QAAQ,SAAS,MAAO,IAAI;AAAA,IACzC;AACA,WAAOA;AAAA,EACX;AAEA,MAAI,QAAQ;AACZ,WAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC3B,aAAS,QAAQ,SAAS,MAAO,KAAK,OAAO,IAAI;AAAA,EACrD;AACA,SAAO;AACX;AAEA,SAAS,cAAc,YAAY;AAC/B,QAAM,QAAQ;AAAA,IACT,cAAc,KAAM;AAAA,IACpB,cAAc,KAAM;AAAA,IACpB,cAAc,KAAM;AAAA,EACzB;AAEA,QAAM,mBAAmB,CAAC;AAE1B,MAAI,MAAM,IAAI;AACV,qBAAiB,KAAK,wBAAwB;AAAA,EAClD;AACA,MAAI,MAAM,IAAI;AACV,qBAAiB,KAAK,uBAAuB;AAAA,EACjD;AACA,MAAI,MAAM,IAAI;AACV,qBAAiB,KAAK,sBAAsB;AAAA,EAChD;AAEA,SAAO;AAAA,IACH,OAAO;AAAA,IACP,aAAa,iBAAiB,KAAK,IAAI,KAAK;AAAA,EAChD;AACJ;AAEA,SAAS,eAAe,YAAY;AAChC,QAAM,cAAc,cAAc,KAAK;AACvC,SAAO;AAAA,IACH,OAAO;AAAA,IACP,aAAa,gBAAgB,IAAI,SAAS;AAAA,EAC9C;AACJ;AAEA,SAAS,aAAa,YAAY;AAC9B,QAAM,OAAO,aAAa;AAC1B,QAAM,eAAe;AAAA,IACjB,QAAS;AAAA,IACT,OAAS;AAAA,IACT,OAAS;AAAA,IACT,QAAS;AAAA,IACT,QAAS;AAAA,IACT,QAAS;AAAA,IACT,GAAK;AAAA,EACT;AAEA,SAAO;AAAA,IACH,OAAO;AAAA,IACP,aAAa,aAAa,SAAS;AAAA,EACvC;AACJ;AAEA,SAAS,eAAe,YAAY,SAAS,WAAW,YAAY;AAChE,MAAI,uBAAuB,UAAU,GAAG;AACpC,WAAO;AAAA,EACX;AACA,SAAO,oBAAoB,QAAQ,OAAO,aAAa,aAAa,GAAG,cAAM,YAAY,MAAM,GAAG,SAAS,IAAI;AACnH;AAEA,SAAS,uBAAuB,YAAY;AACxC,SAAO,eAAe;AAC1B;;;AC/IA,IAAO,oBAAQ;AAAA,EACX,MAAAC;AACJ;AAEA,SAASA,MAAK,UAAU,gBAAgB;AACpC,QAAM,SAAS,UAAU,UAAU,cAAc;AACjD,QAAM,0BAA0B,2BAA2B,UAAU,gBAAgB,MAAM;AAC3F,SAAO;AAAA,IACH,mBAAmB,iBAAiB,UAAU,gBAAgB,MAAM;AAAA,IACpE,gBAAgB,eAAe,UAAU,gBAAgB,MAAM;AAAA,IAC/D,eAAe,cAAc,UAAU,gBAAgB,MAAM;AAAA,IAC7D,oBAAoB;AAAA,IACpB,eAAe,2BAA2B,eAAe,UAAU,gBAAgB,wBAAwB,OAAO,MAAM;AAAA,EAC5H;AACJ;AAEA,SAAS,UAAU,UAAU,gBAAgB;AACzC,SAAO,cAAM,WAAW,UAAU,cAAc;AACpD;AAEA,SAAS,iBAAiB,UAAU,gBAAgB,QAAQ;AACxD,QAAM,SAAS;AACf,QAAM,OAAO;AAEb,MAAI,SAAS,OAAO,QAAQ;AACxB,WAAO;AAAA,EACX;AAEA,QAAM,QAAQ,cAAM,UAAU,UAAU,iBAAiB,MAAM;AAC/D,SAAO;AAAA,IACH;AAAA,IACA,aAAa,KAAK;AAAA,EACtB;AACJ;AAEA,SAAS,eAAe,UAAU,gBAAgB,QAAQ;AACtD,QAAM,SAAS;AACf,QAAM,OAAO;AAEb,MAAI,SAAS,OAAO,QAAQ;AACxB,WAAO;AAAA,EACX;AAEA,QAAM,QAAQ,cAAM,WAAW,UAAU,iBAAiB,MAAM;AAChE,SAAO;AAAA,IACH;AAAA,IACA,aAAa,GAAG;AAAA,EACpB;AACJ;AAEA,SAAS,cAAc,UAAU,gBAAgB,QAAQ;AACrD,QAAM,SAAS;AACf,QAAM,OAAO;AAEb,MAAI,SAAS,OAAO,QAAQ;AACxB,WAAO;AAAA,EACX;AAEA,QAAM,QAAQ,cAAM,WAAW,UAAU,iBAAiB,MAAM;AAChE,SAAO;AAAA,IACH;AAAA,IACA,aAAa,GAAG;AAAA,EACpB;AACJ;AAEA,SAAS,2BAA2B,UAAU,gBAAgB,QAAQ;AAClE,QAAM,SAAS;AACf,QAAM,OAAO;AAEb,MAAI,SAAS,OAAO,QAAQ;AACxB,WAAO;AAAA,EACX;AAEA,QAAM,QAAQ,cAAM,UAAU,UAAU,iBAAiB,MAAM;AAC/D,SAAO;AAAA,IACH;AAAA,IACA,aAAa,KAAK;AAAA,EACtB;AACJ;AAEA,SAAS,eAAe,UAAU,gBAAgB,yBAAyB,QAAQ;AAC/E,QAAM,SAAS;AACf,QAAM,OAAO,IAAI;AAEjB,MAAI,SAAS,OAAO,QAAQ;AACxB,WAAO;AAAA,EACX;AAEA,QAAM,aAAa,CAAC;AAEpB,WAAS,IAAI,GAAG,IAAI,yBAAyB,KAAK;AAC9C,UAAM,kBAAkB,iBAAiB,SAAS,IAAI;AACtD,eAAW,KAAK;AAAA,MACZ,cAAM,UAAU,UAAU,eAAe;AAAA,MACzC,cAAM,UAAU,UAAU,kBAAkB,CAAC;AAAA,MAC7C,cAAM,UAAU,UAAU,kBAAkB,CAAC;AAAA,IACjD,CAAC;AAAA,EACL;AAEA,SAAO;AAAA,IACH,OAAO;AAAA,IACP,aAAa,WAAW,SAAS,IAAI,gBAAgB,UAAU,IAAI,gBAAgB,UAAU,IAAI;AAAA,EACrG;AACJ;AAEA,SAAS,gBAAgB,YAAY;AACjC,QAAM,MAAM;AAAA,IACR,GAAM;AAAA,IACN,GAAM;AAAA,IACN,GAAM;AAAA,IACN,GAAM;AAAA,IACN,GAAM;AAAA,EACV;AAEA,SAAO,WAAW,IAAI,CAAC,eAAe,IAAI,WAAW,GAAG,EAAE,KAAK,EAAE;AACrE;AAEA,SAAS,gBAAgB,YAAY;AACjC,QAAM,QAAQ;AAAA,IACV,IAAM;AAAA,IACN,IAAM;AAAA,IACN,IAAM;AAAA,IACN,IAAM;AAAA,IACN,IAAM;AAAA,IACN,IAAM;AAAA,IACN,IAAM;AAAA,IACN,IAAM;AAAA,EACV;AAEA,MAAI,WAAW,WAAW,KAAK,WAAW,GAAG,OAAO,UAAa,MAAM,WAAW,GAAG,QAAQ,QAAW;AACpG,WAAO;AAAA,EACX;AAEA,SAAO,MAAM,WAAW,GAAG;AAC/B;;;ACtIA,IAAO,oBAAQ;AAAA,EACX,MAAAC;AACJ;AAEA,SAASA,MAAK,UAAU,gBAAgB;AACpC,QAAM,SAASC,WAAU,UAAU,cAAc;AACjD,QAAM,iBAAiB,kBAAkB,UAAU,gBAAgB,MAAM;AACzE,QAAM,kBAAkB,mBAAmB,UAAU,gBAAgB,MAAM;AAC3E,QAAM,OAAO;AAAA,IACT,gBAAgB,WAAW,UAAU,gBAAgB,MAAM;AAAA,IAC3D,mBAAmB,kBAAkB,UAAU,gBAAgB,MAAM;AAAA,IACrE,eAAe,eAAe,UAAU,gBAAgB,MAAM;AAAA,IAC9D,eAAe,eAAe,UAAU,gBAAgB,MAAM;AAAA,IAC9D,wBAAwB;AAAA,IACxB,yBAAyB;AAAA,EAC7B;AAEA,MAAI,mBAAmB,UAAa,oBAAoB,QAAW;AAC/D,UAAM,YAAY,aAAa,UAAU,gBAAgB,IAAI,eAAe,QAAQ,gBAAgB,OAAO,MAAM;AACjH,QAAI,WAAW;AACX,WAAK,oBAAoB;AAAA,IAC7B;AAAA,EACJ;AAEA,aAAW,WAAW,MAAM;AACxB,QAAI,KAAK,aAAa,QAAW;AAC7B,aAAO,KAAK;AAAA,IAChB;AAAA,EACJ;AAEA,SAAO;AACX;AAEA,SAASA,WAAU,UAAU,gBAAgB;AACzC,SAAO,cAAM,WAAW,UAAU,cAAc;AACpD;AAEA,SAAS,WAAW,UAAU,gBAAgB,QAAQ;AAClD,QAAM,SAAS;AACf,QAAM,OAAO;AAEb,MAAI,SAAS,OAAO,QAAQ;AACxB,WAAO;AAAA,EACX;AAEA,QAAM,eAAe,cAAM,UAAU,UAAU,iBAAiB,MAAM;AACtE,QAAM,eAAe,cAAM,UAAU,UAAU,iBAAiB,SAAS,CAAC;AAC1E,SAAO;AAAA,IACH,OAAO,eAAe,MAAQ;AAAA,IAC9B,aAAa,eAAe,MAAM;AAAA,EACtC;AACJ;AAEA,SAAS,kBAAkB,UAAU,gBAAgB,QAAQ;AACzD,QAAM,SAAS;AACf,QAAM,OAAO;AAEb,MAAI,SAAS,OAAO,QAAQ;AACxB,WAAO;AAAA,EACX;AAEA,QAAM,QAAQ,cAAM,UAAU,UAAU,iBAAiB,MAAM;AAC/D,SAAO;AAAA,IACH;AAAA,IACA,aAAa,6BAA6B,KAAK;AAAA,EACnD;AACJ;AAEA,SAAS,6BAA6B,OAAO;AACzC,MAAI,UAAU,GAAG;AACb,WAAO;AAAA,EACX;AACA,MAAI,UAAU,GAAG;AACb,WAAO;AAAA,EACX;AACA,MAAI,UAAU,GAAG;AACb,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAEA,SAAS,eAAe,UAAU,gBAAgB,QAAQ;AACtD,QAAM,SAAS;AACf,QAAM,OAAO;AAEb,MAAI,SAAS,OAAO,QAAQ;AACxB,WAAO;AAAA,EACX;AAEA,QAAM,QAAQ,cAAM,WAAW,UAAU,iBAAiB,MAAM;AAChE,SAAO;AAAA,IACH;AAAA,IACA,aAAa,KAAK;AAAA,EACtB;AACJ;AAEA,SAAS,eAAe,UAAU,gBAAgB,QAAQ;AACtD,QAAM,SAAS;AACf,QAAM,OAAO;AAEb,MAAI,SAAS,OAAO,QAAQ;AACxB,WAAO;AAAA,EACX;AAEA,QAAM,QAAQ,cAAM,WAAW,UAAU,iBAAiB,MAAM;AAChE,SAAO;AAAA,IACH;AAAA,IACA,aAAa,KAAK;AAAA,EACtB;AACJ;AAEA,SAAS,kBAAkB,UAAU,gBAAgB,QAAQ;AACzD,QAAM,SAAS;AACf,QAAM,OAAO;AAEb,MAAI,SAAS,OAAO,QAAQ;AACxB,WAAO;AAAA,EACX;AAEA,QAAM,QAAQ,cAAM,UAAU,UAAU,iBAAiB,MAAM;AAC/D,SAAO;AAAA,IACH;AAAA,IACA,aAAa,GAAG;AAAA,EACpB;AACJ;AAEA,SAAS,mBAAmB,UAAU,gBAAgB,QAAQ;AAC1D,QAAM,SAAS;AACf,QAAM,OAAO;AAEb,MAAI,SAAS,OAAO,QAAQ;AACxB,WAAO;AAAA,EACX;AAEA,QAAM,QAAQ,cAAM,UAAU,UAAU,iBAAiB,MAAM;AAC/D,SAAO;AAAA,IACH;AAAA,IACA,aAAa,GAAG;AAAA,EACpB;AACJ;AAEA,SAAS,aAAa,UAAU,gBAAgB,iBAAiB,QAAQ;AACrE,QAAM,SAAS;AAEf,MAAI,oBAAoB,KAAK,SAAS,kBAAkB,QAAQ;AAC5D,WAAO;AAAA,EACX;AAEA,QAAM,QAAQ,SAAS,OAAO,MAAM,iBAAiB,QAAQ,iBAAiB,SAAS,eAAe;AACtG,SAAO;AAAA,IACH;AAAA,IACA,aAAa;AAAA,EACjB;AACJ;;;ACzJA,IAAO,yBAAQ;AAAA,EACX,QAAQ;AAAA,IACJ,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe,CAAC,UAAU;AACtB,iBAAS,MAAM,MAAM,KAAK,MAAM,IAAI,SAAS;AAAA,MACjD;AAAA,IACJ;AAAA,IACA,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,cAAc;AAAA,IAClB;AAAA,IACA,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe,CAAC,UAAU;AACtB,iBAAS,MAAM,MAAM,KAAK,MAAM,IAAI,SAAS;AAAA,MACjD;AAAA,IACJ;AAAA,IACA,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe,CAAC,UAAU;AACtB,iBAAS,MAAM,MAAM,KAAK,MAAM,IAAI,SAAS;AAAA,MACjD;AAAA,IACJ;AAAA,IACA,KAAQ;AAAA,IACR,KAAQ;AAAA,IACR,KAAQ;AAAA,IACR,KAAQ;AAAA,IACR,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe;AAAA,IACnB;AAAA,IACA,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe;AAAA,IACnB;AAAA,IACA,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe;AAAA,MACf,iBAAiB;AAAA,IACrB;AAAA,IACA,KAAQ;AAAA,IACR,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe,CAAC,UAAU;AACtB,iBAAS,MAAM,MAAM,KAAK,MAAM,IAAI,SAAS;AAAA,MACjD;AAAA,IACJ;AAAA,IACA,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe,CAAC,UAAU;AACtB,iBAAS,MAAM,MAAM,KAAK,MAAM,IAAI,SAAS;AAAA,MACjD;AAAA,IACJ;AAAA,IACA,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe,CAAC,UAAU;AACtB,iBAAS,MAAM,MAAM,KAAK,MAAM,IAAI,SAAS;AAAA,MACjD;AAAA,IACJ;AAAA,IACA,KAAQ;AAAA,IACR,KAAQ;AAAA,IACR,KAAQ;AAAA,IACR,KAAQ;AAAA,IACR,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe,CAAC,UAAU;AACtB,YAAI,eAAe,KAAK,MAAM,MAAM;AAChC,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,IACA,KAAQ;AAAA,IACR,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,eAAe,CAAC,UAAU;AACtB,cAAM,QAAQ,eAAe,KAAK,EAAE,MAAM,GAAG;AAC7C,eAAO,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,KAAK,OAAO,MAAM,KAAK,MAAM,MAAM,KAAK;AAAA,MACtF;AAAA,IACJ;AAAA,IACA,KAAQ;AAAA,IACR,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,cAAc;AAAA,IAClB;AAAA,IACA,KAAQ;AAAA,IACR,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,cAAc;AAAA,IAClB;AAAA,IACA,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,cAAc;AAAA,IAClB;AAAA,IACA,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,cAAc;AAAA,IAClB;AAAA,IACA,KAAQ;AAAA,IACR,KAAQ;AAAA,IACR,KAAQ;AAAA,IACR,KAAQ;AAAA,IACR,KAAQ;AAAA,IACR,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe,CAAC,UAAU;AACtB,cAAM,SAAS,eAAe,KAAK;AACnC,YAAI,WAAW,MAAM;AACjB,iBAAO;AAAA,QACX,WAAW,WAAW,MAAM;AACxB,iBAAO;AAAA,QACX,WAAW,WAAW,MAAM;AACxB,iBAAO;AAAA,QACX,WAAW,WAAW,MAAM;AACxB,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,IACA,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,cAAc;AAAA,IAClB;AAAA,IACA,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,cAAc;AAAA,IAClB;AAAA,IACA,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,cAAc;AAAA,IAClB;AAAA,IACA,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe;AAAA,IACnB;AAAA,IACA,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe;AAAA,IACnB;AAAA,IACA,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe;AAAA,IACnB;AAAA,IACA,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe;AAAA,IACnB;AAAA,IACA,KAAQ;AAAA,IACR,KAAQ;AAAA,IACR,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe,CAAC,UAAU;AACtB,cAAM,SAAS,eAAe,KAAK;AACnC,YAAI,WAAW,KAAK;AAChB,iBAAO;AAAA,QACX,WAAW,WAAW,KAAK;AACvB,iBAAO;AAAA,QACX,WAAW,WAAW,KAAK;AACvB,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,IACA,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,cAAc;AAAA,IAClB;AAAA,IACA,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,cAAc;AAAA,IAClB;AAAA,IACA,KAAQ;AAAA,IACR,KAAQ;AAAA,IACR,KAAQ;AAAA,IACR,KAAQ;AAAA,IACR,KAAQ;AAAA,IACR,KAAQ;AAAA,IACR,KAAQ;AAAA,IACR,KAAQ;AAAA,IACR,KAAQ;AAAA,IACR,KAAQ;AAAA,IACR,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,cAAc;AAAA,IAClB;AAAA,IACA,KAAQ;AAAA,IACR,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,cAAc;AAAA,IAClB;AAAA,IACA,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe,CAAC,UAAU;AAAA,IAC9B;AAAA,IACA,KAAQ;AAAA,IACR,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe,CAAC,UAAU;AACtB,cAAM,SAAS,eAAe,KAAK;AACnC,YAAI,WAAW,KAAK;AAChB,iBAAO;AAAA,QACX,WAAW,WAAW,KAAK;AACvB,iBAAO;AAAA,QACX,WAAW,WAAW,KAAK;AACvB,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,IACA,KAAQ;AAAA,IACR,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe,CAAC,UAAU;AACtB,cAAM,cAAc,eAAe,KAAK;AACxC,cAAM,aAAa,YAAY,OAAO,CAAC;AACvC,cAAM,aAAa,YAAY,OAAO,CAAC;AACvC,YAAI,cAAc;AAElB,YAAI,eAAe,KAAK;AACpB,yBAAe;AAAA,QACnB,WAAW,eAAe,KAAK;AAC3B,yBAAe;AAAA,QACnB;AAEA,YAAI,eAAe,KAAK;AACpB,yBAAe;AAAA,QACnB,WAAW,eAAe,KAAK;AAC3B,yBAAe;AAAA,QACnB,WAAW,eAAe,KAAK;AAC3B,yBAAe;AAAA,QACnB,WAAW,eAAe,KAAK;AAC3B,yBAAe;AAAA,QACnB,WAAW,eAAe,KAAK;AAC3B,yBAAe;AAAA,QACnB,WAAW,eAAe,KAAK;AAC3B,yBAAe;AAAA,QACnB,WAAW,eAAe,KAAK;AAC3B,yBAAe;AAAA,QACnB,WAAW,eAAe,KAAK;AAC3B,yBAAe;AAAA,QACnB;AAEA,YAAI,gBAAgB,IAAI;AACpB,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,IACA,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe,CAAC,UAAU,SAAS,eAAe,KAAK,GAAG,EAAE,IAAI;AAAA,IACpE;AAAA,IACA,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe,CAAC,UAAU;AACtB,cAAM,OAAO,SAAS,eAAe,KAAK,GAAG,EAAE;AAC/C,eAAO,QAAQ,SAAS,IAAI,SAAS;AAAA,MACzC;AAAA,IACJ;AAAA,IACA,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe,CAAC,UAAU;AACtB,cAAM,WAAW,eAAe,KAAK;AACrC,YAAI,SAAS,UAAU,GAAG;AACtB,iBAAO,SAAS,OAAO,GAAG,CAAC,IAAI,MAAM,SAAS,OAAO,GAAG,CAAC,IAAI,MAAM,SAAS,OAAO,GAAG,CAAC;AAAA,QAC3F;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,IACA,KAAQ;AAAA,IACR,KAAQ;AAAA,IACR,KAAQ;AAAA,IACR,KAAQ;AAAA,IACR,KAAQ;AAAA,MACJ,QAAQ,CAAC,UAAU;AACf,YAAI,MAAM,WAAW,GAAG;AACpB,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX;AAAA,MACA,eAAe,CAAC,UAAU;AACtB,YAAI,MAAM,WAAW,GAAG;AACpB,gBAAM,YAAY,MAAM,MAAM,KAAK,MAAM;AACzC,cAAI,aAAa,GAAG;AAChB,mBAAO;AAAA,UACX,WAAW,aAAa,GAAG;AACvB,mBAAO;AAAA,UACX,WAAW,aAAa,GAAG;AACvB,mBAAO;AAAA,UACX,WAAW,aAAa,GAAG;AACvB,mBAAO;AAAA,UACX,WAAW,aAAa,GAAG;AACvB,mBAAO;AAAA,UACX,WAAW,aAAa,GAAG;AACvB,mBAAO;AAAA,UACX,WAAW,aAAa,GAAG;AACvB,mBAAO;AAAA,UACX,WAAW,aAAa,GAAG;AACvB,mBAAO;AAAA,UACX,WAAW,aAAa,GAAG;AACvB,mBAAO;AAAA,UACX,WAAW,aAAa,GAAG;AACvB,mBAAO;AAAA,UACX,WAAW,aAAa,IAAI;AACxB,mBAAO;AAAA,UACX,WAAW,aAAa,IAAI;AACxB,mBAAO;AAAA,UACX,WAAW,aAAa,IAAI;AACxB,mBAAO;AAAA,UACX,WAAW,aAAa,IAAI;AACxB,mBAAO;AAAA,UACX,WAAW,aAAa,IAAI;AACxB,mBAAO;AAAA,UACX,WAAW,aAAa,IAAI;AACxB,mBAAO;AAAA,UACX,WAAW,aAAa,IAAI;AACxB,mBAAO;AAAA,UACX,WAAW,aAAa,IAAI;AACxB,mBAAO;AAAA,UACX,WAAW,aAAa,IAAI;AACxB,mBAAO;AAAA,UACX,WAAW,aAAa,IAAI;AACxB,mBAAO;AAAA,UACX,WAAW,aAAa,IAAI;AACxB,mBAAO;AAAA,UACX,WAAW,aAAa,IAAI;AACxB,mBAAO;AAAA,UACX,WAAW,aAAa,IAAI;AACxB,mBAAO;AAAA,UACX,WAAW,aAAa,IAAI;AACxB,mBAAO;AAAA,UACX,WAAW,aAAa,IAAI;AACxB,mBAAO;AAAA,UACX,WAAW,aAAa,IAAI;AACxB,mBAAO;AAAA,UACX,WAAW,aAAa,IAAI;AACxB,mBAAO;AAAA,UACX,WAAW,aAAa,IAAI;AACxB,mBAAO;AAAA,UACX,WAAW,aAAa,IAAI;AACxB,mBAAO;AAAA,UACX,WAAW,aAAa,IAAI;AACxB,mBAAO;AAAA,UACX;AACA,iBAAO,kBAAkB;AAAA,QAC7B;AACA,eAAO,eAAe,KAAK;AAAA,MAC/B;AAAA,IACJ;AAAA,IACA,KAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe,CAAC,OAAO,SAAS;AAE5B,cAAM,iBAAiB;AAAA,UACnB,MAAM,EAAC,MAAM,IAAG;AAAA,UAChB,MAAM,EAAC,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,IAAG;AAAA,UACjD,MAAM,EAAC,MAAM,IAAG;AAAA,UAChB,MAAM,EAAC,MAAM,OAAO,MAAM,MAAK;AAAA,UAC/B,MAAM,EAAC,MAAM,OAAM;AAAA,UACnB,MAAM,EAAC,MAAM,IAAG;AAAA,UAChB,MAAM,EAAC,MAAM,IAAG;AAAA,UAChB,MAAM,EAAC,MAAM,OAAM;AAAA,UACnB,MAAM,EAAC,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,MAAK;AAAA,UACzD,MAAM,EAAC,MAAM,MAAK;AAAA,QACtB;AACA,cAAM,cAAc,eAAe,KAAK;AAExC,YAAI,KAAK,mCAAmC;AACxC,gBAAM,8BAA8B,eAAe,KAAK,kCAAkC,KAAK;AAC/F,cAAI,eAAe,gCACZ,eAAe,6BAA6B,cAAc;AAC7D,mBAAO,eAAe,6BAA6B;AAAA,UACvD;AAAA,QACJ;AAEA,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,IACA,KAAQ;AAAA,IACR,MAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe,CAAC,UAAU;AACtB,eAAQ,MAAM,GAAI,SAAS;AAAA,MAC/B;AAAA,IACJ;AAAA,IACA,MAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe,CAAC,UAAU;AACtB,YAAI,IAAI;AACR,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,eAAK,KAAK,KAAK,MAAM;AAAA,QACzB;AACA,eAAO,EAAE,SAAS;AAAA,MACtB;AAAA,IACJ;AAAA,IACA,MAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe,CAAC,UAAU;AACtB,YAAI,IAAI;AACR,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,eAAK,KAAK,KAAK,MAAM;AAAA,QACzB;AACA,eAAO,EAAE,SAAS;AAAA,MACtB;AAAA,IACJ;AAAA,IACA,MAAQ;AAAA,MACJ,QAAQ;AAAA,MACR,eAAe,CAAC,UAAU;AACtB,YAAI,IAAI;AACR,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,eAAK,KAAK,KAAK,MAAM;AAAA,QACzB;AACA,eAAO,EAAE,SAAS;AAAA,MACtB;AAAA,IACJ;AAAA,EACJ;AACJ;AAEA,SAAS,gBAAgB,OAAO;AAC5B,QAAM,OAAO,eAAe,KAAK;AAEjC,MAAI,KAAK,UAAU,GAAG;AAClB,WAAO,KAAK,OAAO,GAAG,CAAC,IAAI,MAAM,KAAK,OAAO,GAAG,CAAC,IAAI,MAAM,KAAK,OAAO,GAAG,CAAC;AAAA,EAC/E;AAEA,SAAO;AACX;AAEA,SAAS,gBAAgB,OAAO;AAC5B,QAAM,OAAO,eAAe,KAAK;AACjC,MAAI,aAAa;AAEjB,MAAI,KAAK,UAAU,GAAG;AAClB,iBAAa,KAAK,OAAO,GAAG,CAAC,IAAI,MAAM,KAAK,OAAO,GAAG,CAAC,IAAI,MAAM,KAAK,OAAO,GAAG,CAAC;AACjF,QAAI,KAAK,WAAW,IAAI;AACpB,oBAAc,KAAK,OAAO,GAAG,CAAC,IAAI,KAAK,OAAO,GAAG,CAAC,IAAI,MAAM,KAAK,OAAO,GAAG,CAAC;AAAA,IAChF;AAAA,EACJ;AAEA,SAAO;AACX;AAEA,SAAS,gBAAgB,OAAO;AAC5B,QAAM,SAAS,eAAe,KAAK;AACnC,MAAI,WAAW,UAAU;AACrB,WAAO;AAAA,EACX,WAAW,WAAW,UAAU;AAC5B,WAAO;AAAA,EACX,WAAW,WAAW,WAAW;AAC7B,WAAO;AAAA,EACX,WAAW,WAAW,WAAW;AAC7B,WAAO;AAAA,EACX,WAAW,WAAW,WAAW;AAC7B,WAAO;AAAA,EACX,WAAW,WAAW,UAAU;AAC5B,WAAO;AAAA,EACX,WAAW,WAAW,UAAU;AAC5B,WAAO;AAAA,EACX,WAAW,WAAW,UAAU;AAC5B,WAAO;AAAA,EACX,WAAW,WAAW,UAAU;AAC5B,WAAO;AAAA,EACX,WAAW,WAAW,UAAU;AAC5B,WAAO;AAAA,EACX,WAAW,WAAW,UAAU;AAC5B,WAAO;AAAA,EACX,WAAW,WAAW,UAAU;AAC5B,WAAO;AAAA,EACX,WAAW,WAAW,UAAU;AAC5B,WAAO;AAAA,EACX;AACA,SAAO;AACX;;;AC5dA,IAAO,uBAAQ;AAAA,EACX;AACJ;AAEA,SAAS,MAAM;AACX,MAAI,OAAO,gBAAgB,aAAa;AACpC,WAAO;AAAA,EACX;AAEA,SAAO;AACX;;;ACRA,IAAM,kBAAkB;AAExB,IAAO,sBAAQ;AAAA,EACX;AAAA,EACA;AACJ;AAEA,SAAS,OAAO,UAAU,UAAU;AAChC,QAAM,UAAU,qBAAY,IAAI;AAChC,MAAK,OAAO,YAAY,eAAiB,aAAa,QAAY;AAC9D,QAAI;AACA,aAAO,IAAI,QAAQ,QAAQ,EAAE,OAAO,oBAAoB,WAAW,SAAS,SAAS,WAAW,KAAK,QAAQ,CAAC;AAAA,IAClH,SAAS,OAAP;AAAA,IAEF;AAAA,EACJ;AAEA,QAAM,cAAc,SAAS,IAAI,CAAC,aAAa,OAAO,aAAa,QAAQ,CAAC,EAAE,KAAK,EAAE;AACrF,SAAOC,kBAAiB,WAAW;AACvC;AAEA,SAASA,kBAAiB,YAAY;AAClC,MAAI;AACA,WAAO,mBAAmB,OAAO,UAAU,CAAC;AAAA,EAChD,SAAS,OAAP;AACE,WAAO;AAAA,EACX;AACJ;;;AC1BA,IAAM,aAAa;AACnB,IAAM,kBAAkB;AACxB,IAAM,6BAA6B,kBAAkB;AACrD,IAAM,0BAA0B;AAChC,IAAMC,mBAAkB;AAExB,IAAO,oBAAQ;AAAA,EACX,MAAAC;AACJ;AAEA,SAASA,MAAK,UAAU,YAAY,gBAAgB;AAChD,MAAI;AACA,QAAI,MAAM,QAAQ,QAAQ,GAAG;AACzB,aAAO,UAAU,IAAI,SAAS,WAAW,KAAK,QAAQ,EAAE,MAAM,GAAG,EAAC,MAAM,SAAS,OAAM,GAAG,GAAG,cAAc;AAAA,IAC/G;AACA,UAAM,EAAC,UAAU,YAAY,cAAa,IAAI,oBAAoB,UAAU,UAAU;AACtF,WAAO,UAAU,UAAU,UAAU,eAAe,cAAc;AAAA,EACtE,SAAS,OAAP;AACE,WAAO,CAAC;AAAA,EACZ;AACJ;AAEA,SAAS,oBAAoB,UAAU,YAAY;AAC/C,SAAO,aAAa,8BAA8B,SAAS,YAAY;AACnE,UAAM,gBAAgB,iBAAiB,UAAU,UAAU;AAC3D,QAAI,mBAAmB,aAAa,GAAG;AACnC,aAAO,EAAC,UAAU,eAAe,YAAY,aAAa,2BAA0B;AAAA,IACxF;AACA,kBAAc,6BAA6B,cAAc,OAAO,gBAAgB,aAAa;AAAA,EACjG;AACA,QAAM,IAAI,MAAM,6BAA6B;AACjD;AAEA,SAAS,iBAAiB,UAAU,YAAY;AAC5C,QAAM,6BAA6B;AAEnC,MAAI,SAAS,UAAU,YAAY,KAAK,MAAM,YAAY;AACtD,UAAM,IAAI,MAAM,6BAA6B;AAAA,EACjD;AAEA,SAAO;AAAA,IACH,MAAM,SAAS,UAAU,aAAa,eAAe;AAAA,IACrD,MAAM,SAAS,UAAU,aAAa,0BAA0B;AAAA,EACpE;AACJ;AAEA,SAAS,mBAAmB,eAAe;AACvC,SAAO,cAAc,SAAS;AAClC;AAEA,SAAS,gBAAgB,eAAe;AACpC,MAAI,cAAc,OAAO,MAAM,GAAG;AAC9B,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAEA,SAAS,UAAU,UAAU,UAAU,YAAY,gBAAgB;AAC/D,QAAM,OAAO,CAAC;AACd,MAAI,WAAW;AAEf,QAAM,mBAAmB,aAAa,SAAS;AAE/C,SAAQ,aAAa,oBAAsB,aAAa,SAAS,YAAa;AAC1E,UAAM,EAAC,KAAK,QAAO,IAAIC,SAAQ,UAAU,YAAY,MAAM,UAAU,cAAc;AAEnF,QAAI,QAAQ,MAAM;AACd;AAAA,IACJ;AAEA,QAAI,KAAK;AACL,UAAI,cAAc,KAAK;AACnB,mBAAW,IAAI;AAAA,MACnB;AAEA,UAAK,KAAK,IAAI,UAAU,UAAe,IAAI,kBAAkB,QAAY;AACrE,aAAK,IAAI,QAAQ;AAAA,UACb,IAAI,IAAI;AAAA,UACR,OAAO,IAAI;AAAA,UACX,aAAa,IAAI;AAAA,QACrB;AAAA,MACJ,OAAO;AACH,YAAI,EAAE,KAAK,IAAI,iBAAiB,QAAQ;AACpC,eAAK,IAAI,QAAQ,CAAC;AAAA,YACd,IAAI,KAAK,IAAI,MAAM;AAAA,YACnB,OAAO,KAAK,IAAI,MAAM;AAAA,YACtB,aAAa,KAAK,IAAI,MAAM;AAAA,UAChC,CAAC;AAAA,QACL;AACA,aAAK,IAAI,MAAM,KAAK;AAAA,UAChB,IAAI,IAAI;AAAA,UACR,OAAO,IAAI;AAAA,UACX,aAAa,IAAI;AAAA,QACrB,CAAC;AAAA,MACL;AAAA,IACJ;AAEA,kBAAcF,mBAAkB;AAAA,EACpC;AAEA,SAAO;AACX;AAEA,SAASE,SAAQ,UAAU,YAAY,MAAM,UAAU,gBAAgB;AACnE,QAAM,kBAAkB;AACxB,QAAM,kBAAkB;AAExB,MAAI,kBAAkB,UAAU,UAAU,GAAG;AACzC,WAAO,EAAC,KAAK,MAAM,SAAS,EAAC;AAAA,EACjC;AAEA,QAAM,UAAU,SAAS,UAAU,aAAa,eAAe;AAC/D,QAAM,UAAU,SAAS,UAAU,aAAa,eAAe;AAE/D,MAAI,CAAC,kBAAkB,CAAC,uBAAa,QAAQ,UAAU;AACnD,WAAO,EAAC,KAAK,QAAW,QAAO;AAAA,EACnC;AAEA,QAAM,WAAWC,aAAY,UAAU,aAAaH,kBAAiB,OAAO;AAE5E,QAAM,MAAM;AAAA,IACR,IAAI;AAAA,IACJ,MAAM,WAAW,uBAAa,QAAQ,UAAU,SAAS,QAAQ;AAAA,IACjE,OAAO;AAAA,IACP,aAAa,kBAAkB,uBAAa,QAAQ,UAAU,UAAU,MAAM,QAAQ;AAAA,EAC1F;AACA,MAAI,gBAAgB,OAAO,GAAG;AAC1B,QAAI,gBAAgB;AAAA,EACxB;AACA,MAAI,oBAAoB,OAAO,GAAG;AAC9B,QAAI,cAAc,uBAAa,QAAQ,SAAS,iBAAiB,QAAQ;AAAA,EAC7E;AAEA,SAAO,EAAC,KAAK,QAAO;AACxB;AAEA,SAAS,kBAAkB,UAAU,YAAY;AAC7C,QAAM,gBAAgB;AACtB,SAAO,SAAS,SAAS,UAAU,MAAM;AAC7C;AAEA,SAASG,aAAY,UAAU,QAAQ,MAAM;AACzC,QAAM,QAAQ,CAAC;AAEf,WAAS,aAAa,GAAG,aAAa,MAAM,cAAc;AACtD,UAAM,KAAK,SAAS,SAAS,SAAS,UAAU,CAAC;AAAA,EACrD;AAEA,SAAO;AACX;AAEA,SAAS,WAAW,KAAK,SAAS,UAAU;AACxC,MAAI,CAAC,KAAK;AACN,WAAO,aAAa;AAAA,EACxB;AACA,MAAI,UAAU,GAAG,GAAG;AAChB,WAAO;AAAA,EACX;AACA,MAAI,eAAe,GAAG,GAAG;AACrB,WAAO,IAAI,QAAQ,QAAQ;AAAA,EAC/B;AACA,SAAO,IAAI;AACf;AAEA,SAAS,UAAU,KAAK;AACpB,SAAO,OAAO,QAAQ;AAC1B;AAEA,SAAS,eAAe,KAAK;AACzB,SAAO,OAAQ,IAAI,YAAa;AACpC;AAEA,SAAS,kBAAkB,KAAK,UAAU,MAAM,UAAU;AACtD,MAAI,uBAAuB,GAAG,GAAG;AAC7B,QAAI;AACA,aAAO,IAAI,eAAe,UAAU,IAAI;AAAA,IAC5C,SAAS,OAAP;AAAA,IAEF;AAAA,EACJ;AACA,MAAI,eAAe,KAAK,QAAQ,GAAG;AAC/B,WAAO,oBAAW,OAAO,UAAU,QAAQ;AAAA,EAC/C;AACA,SAAO;AACX;AAEA,SAAS,eAAe,KAAK,UAAU;AACnC,SAAO,OAAO,oBAAoB;AACtC;AAEA,SAAS,uBAAuB,KAAK;AACjC,SAAO,OAAO,IAAI,mBAAmB;AACzC;AAEA,SAAS,gBAAgB,SAAS;AAC9B,SAAO,uBAAa,QAAQ,YAAY,uBAAa,QAAQ,SAAS;AAC1E;AAEA,SAAS,oBAAoB,SAAS;AAClC,SAAO,uBAAa,QAAQ,YAAY,uBAAa,QAAQ,SAAS,qBAAqB;AAC/F;;;ACzMA,IAAO,wBAAQ;AAAA,EACX,mBAAmB,OAAO;AACtB,QAAI,UAAU,KAAK;AACf,aAAO;AAAA,IACX;AACA,QAAI,UAAU,KAAK;AACf,aAAO;AAAA,IACX;AACA,QAAI,UAAU,KAAK;AACf,aAAO;AAAA,IACX;AACA,QAAI,UAAU,KAAK;AACf,aAAO;AAAA,IACX;AACA,QAAI,UAAU,KAAK;AACf,aAAO;AAAA,IACX;AACA,QAAI,UAAU,KAAK;AACf,aAAO;AAAA,IACX;AACA,QAAI,UAAU,KAAK;AACf,aAAO;AAAA,IACX;AACA,QAAI,UAAU,KAAK;AACf,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,uBAAuB,CAAC,UAAU,yBAAe,eAAe,SAAS,OAAO,EAAE,CAAC;AAAA,EACnF,oBAAoB,CAAC,UAAU,SAAS,yBAAe,aAAa,KAAK;AAAA,EACzE,oBAAoB,CAAC,UAAU,SAAS,yBAAe,aAAa,KAAK;AAAA,EACzE,sBAAsB,CAAC,UAAU,SAAS,yBAAe,eAAe,KAAK;AAAA,EAC7E,oBAAoB;AAAA,EACpB,qBAAqB;AAAA,EACrB,gBAAgB,CAAC,UAAU,SAAS,yBAAe,SAAS,KAAK;AAAA,EACjE,oBAAoB,CAAC,UAAU,SAAS,yBAAe,aAAa,KAAK;AAAA,EACzE,iCAAiC,CAAC,UAAU,yBAAe,yBAAyB,SAAS,OAAO,EAAE,CAAC;AAAA,EACvG,mBAAmB,CAAC,UAAU,yBAAe,WAAW,YAAY,KAAK,CAAC;AAAA,EAC1E,+BAA+B,OAAO,aAAa;AAC/C,QAAI,mBAAmB,KAAK,WAAW,GAAG;AACtC,YAAM,UAAU,YAAY,MAAM,IAAI,EAAE,IAAI,CAAC,WAAW,OAAO,WAAW,CAAC,CAAC;AAC5E,aAAO,yBAAe,wBAAwB,OAAO;AAAA,IACzD;AACA,WAAO;AAAA,EACX;AAAA,EACA,iBAAiB,CAAC,UAAU,yBAAe,SAAS,SAAS,OAAO,EAAE,CAAC;AAAA,EACvE,uBAAuB,CAAC,UAAU,yBAAe,eAAe,SAAS,OAAO,EAAE,CAAC;AAAA,EACnF,qBAAqB,CAAC,UAAU,yBAAe,aAAa,SAAS,OAAO,EAAE,CAAC;AAAA,EAC/E,wBAAwB,CAAC,UAAU,yBAAe,gBAAgB,SAAS,OAAO,EAAE,CAAC;AAAA,EACrF,oBAAoB,OAAO;AACvB,QAAI,WAAW,KAAK,GAAG;AACnB,aAAO,yBAAe,aAAa,MAAM,MAAM,GAAG,EAAE,IAAI,CAAC,WAAW,SAAS,QAAQ,EAAE,CAAC,CAAC;AAAA,IAC7F;AACA,WAAO;AAAA,EACX;AAAA,EACA,qBAAqB,CAAC,UAAU,yBAAe,aAAa,SAAS,OAAO,EAAE,CAAC;AAAA,EAC/E,mBAAmB,CAAC,UAAU,yBAAe,WAAW,SAAS,OAAO,EAAE,CAAC;AAAA,EAC3E,yBAAyB,CAAC,UAAU,yBAAe,iBAAiB,SAAS,OAAO,EAAE,CAAC;AAAA,EACvF,kBAAkB,CAAC,UAAU,yBAAe,UAAU,SAAS,OAAO,EAAE,CAAC;AAAA,EACzE,0BAA0B,CAAC,UAAU,SAAS,yBAAe,mBAAmB,KAAK;AAAA,EACrF,qBAAqB,CAAC,UAAU,yBAAe,aAAa,SAAS,OAAO,EAAE,CAAC;AACnF;AAEA,SAAS,SAAS,MAAM,OAAO;AAC3B,MAAI,WAAW,KAAK,GAAG;AACnB,WAAO,KAAK,MAAM,MAAM,GAAG,CAAC;AAAA,EAChC;AACA,SAAO;AACX;AAEA,SAAS,YAAY,OAAO;AACxB,MAAI,MAAM,UAAU,GAAG,CAAC,MAAM,MAAM;AAChC,WAAO,SAAS,MAAM,UAAU,CAAC,GAAG,EAAE;AAAA,EAC1C;AACA,SAAO,SAAS,OAAO,EAAE;AAC7B;AAEA,SAAS,WAAW,OAAO;AACvB,SAAO,iBAAiB,KAAK,KAAK;AACtC;AAEA,SAAS,kBAAkB,OAAO;AAC9B,QAAM,CAAC,eAAe,aAAa,IAAI,MAAM,MAAM,GAAG;AACtD,MAAK,kBAAkB,UAAe,kBAAkB,QAAY;AAChE,UAAM,UAAU,WAAW,aAAa;AACxC,UAAM,UAAU,WAAW,aAAa;AACxC,UAAM,MAAM,cAAc,OAAO,cAAc,SAAS,CAAC;AACzD,QAAK,CAAC,OAAO,MAAM,OAAO,KAAO,CAAC,OAAO,MAAM,OAAO,GAAI;AACtD,aAAO,MAAM,UAAU,UAAU,MAAM;AAAA,IAC3C;AAAA,EACJ;AACA,SAAO;AACX;;;AC9FA,IAAO,qBAAQ;AAAA,EACX,KAAAC;AACJ;AAEA,SAASA,OAAM;AACX,MAAI,OAAO,cAAc,aAAa;AAClC,WAAO,IAAI,UAAU;AAAA,EACzB;AACA,MAAI;AAEA,UAAM,EAAC,WAAAC,YAAW,mBAAkB,IAAI,wBAAwB,gBAAgB;AAChF,WAAO,IAAIA,WAAU,EAAC,SAAS,mBAAkB,CAAC;AAAA,EACtD,SAAS,OAAP;AACE,WAAO;AAAA,EACX;AACJ;;;ACXA,IAAO,mBAAQ;AAAA,EACX,MAAAC;AACJ;AAEA,SAASA,MAAK,UAAU,QAAQ;AAC5B,QAAM,OAAO,CAAC;AAEd,MAAI,OAAO,aAAa,UAAU;AAC9B,aAAS,MAAM,QAAQ;AACvB,WAAO;AAAA,EACX;AAEA,QAAM,CAAC,aAAa,WAAW,IAAI,sBAAsB,UAAU,MAAM;AAEzE,QAAM,kBAAkB,SAAS,MAAM,WAAW;AAElD,MAAI,aAAa;AACb,UAAM,kBAAkB,SAAS,MAAM,WAAW;AAElD,QAAI,CAAC,mBAAmB,CAAC,iBAAiB;AAKtC,aAAO,KAAK;AACZ,eAAS,MAAM,cAAc,UAAU,MAAM,CAAC;AAAA,IAClD;AAAA,EACJ;AAEA,SAAO;AACX;AAKA,SAAS,sBAAsB,UAAU,QAAQ;AAC7C,MAAI,OAAO,WAAW,GAAG;AACrB,WAAO,CAAC;AAAA,EACZ;AAEA,QAAM,iBAAiB,CAAC,cAAc,UAAU,OAAO,MAAM,GAAG,CAAC,CAAC,CAAC;AACnE,MAAI,OAAO,SAAS,GAAG;AACnB,mBAAe,KAAK,cAAc,UAAU,OAAO,MAAM,CAAC,CAAC,CAAC;AAAA,EAChE;AAEA,SAAO;AACX;AAEA,SAAS,cAAc,UAAU,QAAQ;AACrC,QAAM,cAAc,OAAO,OAAO,CAAC,MAAM,UAAU,OAAO,MAAM,QAAQ,CAAC;AACzE,QAAM,iBAAiB,IAAI,WAAW,WAAW;AACjD,MAAI,SAAS;AAEb,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,UAAM,QAAQ,OAAO;AACrB,UAAM,QAAQ,SAAS,OAAO,MAAM,MAAM,YAAY,MAAM,aAAa,MAAM,MAAM;AACrF,mBAAe,IAAI,IAAI,WAAW,KAAK,GAAG,MAAM;AAChD,cAAU,MAAM;AAAA,EACpB;AAEA,SAAO,IAAI,SAAS,eAAe,MAAM;AAC7C;AAEA,SAAS,SAAS,MAAM,eAAe;AACnC,MAAI;AACA,UAAM,EAAC,KAAK,IAAG,IAAI,YAAY,aAAa;AAC5C,SAAK,QAAQ,KAAK,QAAQ,MAAM;AAChC,UAAM,MAAM,OAAO,GAAG;AAEtB,iBAAa,MAAM,eAAe,gBAAgB,KAAK,IAAI,CAAC,CAAC;AAC7D,WAAO;AAAA,EACX,SAAS,OAAP;AACE,WAAO;AAAA,EACX;AACJ;AAEA,SAAS,YAAY,eAAe;AAChC,QAAM,YAAY,mBAAU,IAAI;AAChC,MAAI,CAAC,WAAW;AACZ,YAAQ,KAAK,iFAAiF;AAC9F,UAAM,IAAI,MAAM;AAAA,EACpB;AAEA,QAAM,YAAY,OAAO,kBAAkB,WAAW,gBAAgB,sBAAsB,eAAe,GAAG,cAAc,UAAU;AACtI,QAAM,MAAM,UAAU,gBAAgB,cAAc,SAAS,GAAG,iBAAiB;AAEjF,MAAI,IAAI,gBAAgB,aAAa,eAAe;AAChD,UAAM,IAAI,MAAM,IAAI,gBAAgB,WAAW;AAAA,EACnD;AAEA,SAAO;AAAA,IACH;AAAA,IACA,KAAK;AAAA,EACT;AACJ;AAEA,SAAS,cAAc,WAAW;AAC9B,SAAO,UAAU,QAAQ,yBAAyB,IAAI,EAAE,QAAQ,+BAA+B,IAAI;AACvG;AAEA,SAAS,OAAO,MAAM;AAClB,WAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AAC7C,QAAI,KAAK,WAAW,GAAG,YAAY,aAAa;AAC5C,aAAO,OAAO,KAAK,WAAW,EAAE;AAAA,IACpC;AACA,QAAI,KAAK,WAAW,GAAG,YAAY,WAAW;AAC1C,aAAO,KAAK,WAAW;AAAA,IAC3B;AAAA,EACJ;AAEA,QAAM,IAAI,MAAM;AACpB;AAEA,SAAS,gBAAgB,MAAM,YAAY,OAAO;AAC9C,QAAM,aAAa,cAAc,IAAI;AAErC,MAAI,mBAAmB,UAAU,GAAG;AAChC,QAAI,WAAW;AACX,aAAO,CAAC;AAAA,IACZ;AACA,WAAO,aAAa,WAAW,EAAE;AAAA,EACrC;AAEA,SAAO,qBAAqB,UAAU;AAC1C;AAEA,SAAS,cAAc,MAAM;AACzB,QAAM,WAAW,CAAC;AAElB,WAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AAC7C,aAAS,KAAK,KAAK,WAAW,EAAE;AAAA,EACpC;AAEA,SAAO;AACX;AAEA,SAAS,mBAAmB,OAAO;AAC/B,SAAQ,MAAM,WAAW,KAAO,MAAM,GAAG,aAAa;AAC1D;AAEA,SAAS,aAAa,MAAM;AACxB,SAAO,KAAK;AAChB;AAEA,SAAS,qBAAqB,OAAO;AACjC,QAAM,WAAW,CAAC;AAElB,QAAM,QAAQ,CAAC,SAAS;AACpB,QAAI,UAAU,IAAI,GAAG;AACjB,YAAM,cAAc,mBAAmB,IAAI;AAE3C,UAAI,SAAS,KAAK,cAAc,QAAW;AACvC,YAAI,CAAC,MAAM,QAAQ,SAAS,KAAK,SAAS,GAAG;AACzC,mBAAS,KAAK,YAAY,CAAC,SAAS,KAAK,SAAS;AAAA,QACtD;AACA,iBAAS,KAAK,UAAU,KAAK,WAAW;AAAA,MAC5C,OAAO;AACH,iBAAS,KAAK,YAAY;AAAA,MAC9B;AAAA,IACJ;AAAA,EACJ,CAAC;AAED,SAAO;AACX;AAEA,SAAS,UAAU,MAAM;AACrB,SAAQ,KAAK,YAAc,KAAK,aAAa;AACjD;AAEA,SAAS,mBAAmB,MAAM;AAC9B,SAAO;AAAA,IACH,YAAY,cAAc,IAAI;AAAA,IAC9B,OAAO,gBAAgB,IAAI;AAAA,EAC/B;AACJ;AAEA,SAAS,cAAc,SAAS;AAC5B,QAAM,aAAa,CAAC;AAEpB,WAAS,IAAI,GAAG,IAAI,QAAQ,WAAW,QAAQ,KAAK;AAChD,eAAW,QAAQ,WAAW,GAAG,YAAY,mBAAmB,OAAO,QAAQ,WAAW,GAAG,KAAK,CAAC;AAAA,EACvG;AAEA,SAAO;AACX;AAEA,SAAS,eAAe,WAAW;AAC/B,QAAM,OAAO,CAAC;AAEd,MAAI,OAAO,cAAc,UAAU;AAC/B,WAAO;AAAA,EACX;AAEA,aAAW,YAAY,WAAW;AAC9B,QAAI,QAAQ,UAAU;AAEtB,QAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACvB,cAAQ,CAAC,KAAK;AAAA,IAClB;AAEA,UAAM,QAAQ,CAAC,SAAS;AACpB,mBAAa,MAAM,0BAA0B,KAAK,UAAU,CAAC;AAC7D,UAAI,OAAO,KAAK,UAAU,UAAU;AAChC,qBAAa,MAAM,wBAAwB,KAAK,KAAK,CAAC;AAAA,MAC1D;AAAA,IACJ,CAAC;AAAA,EACL;AAEA,SAAO;AACX;AAEA,SAAS,0BAA0B,YAAY;AAC3C,QAAM,OAAO,CAAC;AAEd,aAAW,QAAQ,YAAY;AAC3B,QAAI;AACA,UAAI,eAAe,IAAI,GAAG;AACtB,aAAK,aAAa,IAAI,KAAK;AAAA,UACvB,OAAO,WAAW;AAAA,UAClB,YAAY,CAAC;AAAA,UACb,aAAa,eAAe,WAAW,OAAO,IAAI;AAAA,QACtD;AAAA,MACJ;AAAA,IACJ,SAAS,OAAP;AAAA,IAEF;AAAA,EACJ;AAEA,SAAO;AACX;AAEA,SAAS,eAAe,MAAM;AAC1B,SAAQ,SAAS,mBAAqB,CAAC,sBAAsB,IAAI;AACrE;AAEA,SAAS,sBAAsB,MAAM;AACjC,SAAO,KAAK,MAAM,GAAG,EAAE,OAAO;AAClC;AAEA,SAAS,aAAa,MAAM;AACxB,MAAI,mCAAmC,KAAK,IAAI,GAAG;AAC/C,WAAO;AAAA,EACX;AACA,SAAO,KAAK,MAAM,GAAG,EAAE;AAC3B;AAEA,SAAS,eAAe,OAAO,OAAO,QAAW;AAC7C,MAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,UAAM,mBAAmB,sBAAsB,KAAK;AACpD,QAAK,QAAU,OAAO,sBAAY,UAAU,YAAa;AACrD,aAAO,sBAAY,MAAM,OAAO,gBAAgB;AAAA,IACpD;AACA,WAAO;AAAA,EACX;AACA,MAAI,OAAO,UAAU,UAAU;AAC3B,WAAO,uBAAuB,KAAK;AAAA,EACvC;AAEA,MAAI;AACA,QAAK,QAAU,OAAO,sBAAY,UAAU,YAAa;AACrD,aAAO,sBAAY,MAAM,KAAK;AAAA,IAClC;AACA,WAAO,mBAAmB,OAAO,KAAK,CAAC;AAAA,EAC3C,SAAS,OAAP;AACE,WAAO;AAAA,EACX;AACJ;AAEA,SAAS,sBAAsB,OAAO;AAClC,SAAO,MAAM,IAAI,CAAC,SAAS;AACvB,QAAI,KAAK,UAAU,QAAW;AAC1B,aAAO,eAAe,KAAK,KAAK;AAAA,IACpC;AACA,WAAO,eAAe,IAAI;AAAA,EAC9B,CAAC,EAAE,KAAK,IAAI;AAChB;AAEA,SAAS,uBAAuB,OAAO;AACnC,QAAM,eAAe,CAAC;AAEtB,aAAW,OAAO,OAAO;AACrB,iBAAa,KAAK,GAAG,gBAAgB,GAAG,MAAM,eAAe,MAAM,KAAK,KAAK,GAAG;AAAA,EACpF;AAEA,SAAO,aAAa,KAAK,IAAI;AACjC;AAEA,SAAS,gBAAgB,KAAK;AAC1B,MAAI,QAAQ,aAAa;AACrB,WAAO;AAAA,EACX;AACA,MAAI,QAAQ,aAAa;AACrB,WAAO;AAAA,EACX;AACA,MAAI,QAAQ,eAAe;AACvB,WAAO;AAAA,EACX;AACA,MAAI,QAAQ,cAAc;AACtB,WAAO;AAAA,EACX;AACA,MAAI,QAAQ,eAAe;AACvB,WAAO;AAAA,EACX;AACA,MAAI,QAAQ,eAAe;AACvB,WAAO;AAAA,EACX;AACA,MAAI,QAAQ,aAAa;AACrB,WAAO;AAAA,EACX;AACA,MAAI,QAAQ,aAAa;AACrB,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAEA,SAAS,wBAAwB,UAAU;AACvC,QAAM,OAAO,CAAC;AAEd,aAAW,QAAQ,UAAU;AACzB,QAAI;AACA,UAAI,CAAC,sBAAsB,IAAI,GAAG;AAC9B,aAAK,aAAa,IAAI,KAAK,eAAe,SAAS,OAAO,IAAI;AAAA,MAClE;AAAA,IACJ,SAAS,OAAP;AAAA,IAEF;AAAA,EACJ;AAEA,SAAO;AACX;AAEA,SAAS,eAAe,MAAM,MAAM;AAChC,MAAI,eAAe,IAAI,GAAG;AACtB,WAAO,wBAAwB,MAAM,IAAI;AAAA,EAC7C;AACA,MAAI,mBAAmB,IAAI,GAAG;AAC1B,WAAO,EAAC,OAAO,IAAI,YAAY,CAAC,GAAG,aAAa,GAAE;AAAA,EACtD;AACA,MAAI,8BAA8B,IAAI,GAAG;AACrC,WAAO,gCAAgC,MAAM,IAAI;AAAA,EACrD;AACA,MAAI,iCAAiC,IAAI,GAAG;AACxC,WAAO,mCAAmC,MAAM,IAAI;AAAA,EACxD;AACA,MAAI,mBAAmB,IAAI,GAAG;AAC1B,WAAO,4BAA4B,MAAM,IAAI;AAAA,EACjD;AACA,MAAI,QAAQ,IAAI,GAAG;AACf,WAAO,iBAAiB,MAAM,IAAI;AAAA,EACtC;AACA,SAAO,uBAAuB,MAAM,IAAI;AAC5C;AAEA,SAAS,mBAAmB,MAAM;AAC9B,SAAQ,KAAK,WAAW,qBAAqB,cACrC,OAAO,KAAK,UAAU,YACtB,KAAK,MAAM,KAAK,MAAM;AAClC;AAEA,SAAS,eAAe,MAAM;AAC1B,SAAO,MAAM,QAAQ,IAAI;AAC7B;AAEA,SAAS,wBAAwB,MAAM,MAAM;AACzC,SAAO,uBAAuB,KAAK,KAAK,SAAS,IAAI,IAAI;AAC7D;AAEA,SAAS,8BAA8B,MAAM;AACzC,SAAS,KAAK,WAAW,qBAAqB,cAAgB,KAAK,MAAM,iBAAiB,UACjF,KAAK,MAAM,uBAAuB,UAAe,KAAK,MAAM,mBAAmB,MAAM,iBAAiB;AACnH;AAEA,SAAS,gCAAgC,MAAM,MAAM;AACjD,QAAM,aAAa,oBAAoB,IAAI;AAE3C,MAAI,KAAK,MAAM,uBAAuB,QAAW;AAC7C,WAAO,KAAK,MAAM;AAAA,EACtB;AAEA,eAAa,YAAY,oBAAoB,IAAI,GAAG,8BAA8B,IAAI,CAAC;AAEvF,QAAM,QAAQ,cAAc,IAAI;AAEhC,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA,aAAa,eAAe,OAAO,IAAI;AAAA,EAC3C;AACJ;AAEA,SAAS,oBAAoB,MAAM;AAC/B,QAAM,aAAa,CAAC;AAEpB,aAAW,QAAQ,KAAK,YAAY;AAChC,QAAK,SAAS,mBAAqB,SAAS,kBAAoB,CAAC,sBAAsB,IAAI,GAAI;AAC3F,iBAAW,aAAa,IAAI,KAAK,KAAK,WAAW;AAAA,IACrD;AAAA,EACJ;AAEA,SAAO;AACX;AAEA,SAAS,8BAA8B,MAAM;AACzC,QAAM,aAAa,CAAC;AAEpB,aAAW,QAAQ,KAAK,OAAO;AAC3B,QAAK,SAAS,eAAiB,CAAC,sBAAsB,IAAI,GAAI;AAC1D,iBAAW,aAAa,IAAI,KAAK,KAAK,MAAM,MAAM;AAAA,IACtD;AAAA,EACJ;AAEA,SAAO;AACX;AAEA,SAAS,cAAc,MAAM;AACzB,SAAO,YAAY,KAAK,MAAM,YAAY,KAAK,KAAK,MAAM,aAAa;AAC3E;AAEA,SAAS,iCAAiC,MAAM;AAC5C,SAAQ,KAAK,WAAW,qBAAqB,cACpC,KAAK,MAAM,uBAAuB,UAAe,KAAK,MAAM,mBAAmB,MAAM,iBAAiB;AACnH;AAEA,SAAS,mCAAmC,MAAM,MAAM;AACpD,QAAM,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,IACR,YAAY,CAAC;AAAA,EACjB;AAEA,MAAI,KAAK,MAAM,uBAAuB,QAAW;AAC7C,iBAAa,IAAI,OAAO,0BAA0B,KAAK,MAAM,mBAAmB,UAAU,CAAC;AAC3F,iBAAa,IAAI,YAAY,oBAAoB,IAAI,CAAC;AACtD,WAAO,KAAK,MAAM;AAAA,EACtB;AAEA,eAAa,IAAI,OAAO,wBAAwB,KAAK,KAAK,CAAC;AAE3D,MAAI,cAAc,eAAe,IAAI,OAAO,IAAI;AAEhD,SAAO;AACX;AAEA,SAAS,mBAAmB,MAAM;AAC9B,SAAQ,OAAO,KAAK,KAAK,KAAK,EAAE,WAAW,KACnC,KAAK,WAAW,gBAAgB,UAChC,KAAK,WAAW,oBAAoB;AAChD;AAEA,SAAS,4BAA4B,MAAM,MAAM;AAC7C,QAAM,QAAQ,0BAA0B,KAAK,UAAU;AAEvD,SAAO;AAAA,IACH;AAAA,IACA,YAAY,CAAC;AAAA,IACb,aAAa,eAAe,OAAO,IAAI;AAAA,EAC3C;AACJ;AAEA,SAAS,QAAQ,MAAM;AACnB,SAAO,cAAc,KAAK,KAAK,MAAM;AACzC;AAEA,SAAS,cAAc,OAAO;AAC1B,SAAO,MAAM,cAAc,MAAM,cAAc,MAAM;AACzD;AAEA,SAAS,iBAAiB,MAAM,MAAM;AAClC,MAAI,QAAQ,cAAc,KAAK,KAAK,EAAE,MAAM;AAC5C,QAAM,aAAa,oBAAoB,IAAI;AAC3C,QAAM,QAAQ,CAAC;AAEf,MAAI,UAAU,QAAW;AACrB,YAAQ,CAAC;AAAA,EACb,WAAW,CAAC,MAAM,QAAQ,KAAK,GAAG;AAC9B,YAAQ,CAAC,KAAK;AAAA,EAClB;AAEA,QAAM,QAAQ,CAAC,SAAS;AACpB,UAAM,KAAK,gBAAgB,IAAI,CAAC;AAAA,EACpC,CAAC;AAED,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA,aAAa,eAAe,OAAO,IAAI;AAAA,EAC3C;AACJ;AAEA,SAAS,gBAAgB,MAAM;AAC3B,MAAI,8BAA8B,IAAI,GAAG;AACrC,WAAO,gCAAgC,IAAI;AAAA,EAC/C;AACA,MAAI,iCAAiC,IAAI,GAAG;AACxC,WAAO,mCAAmC,IAAI,EAAE;AAAA,EACpD;AACA,MAAI,mBAAmB,IAAI,GAAG;AAC1B,WAAO,4BAA4B,IAAI,EAAE;AAAA,EAC7C;AAEA,SAAO,uBAAuB,IAAI;AACtC;AAEA,SAAS,uBAAuB,MAAM,MAAM;AACxC,QAAM,QAAQ,YAAY,IAAI,KAAK,eAAe,KAAK,KAAK;AAE5D,SAAO;AAAA,IACH;AAAA,IACA,YAAY,oBAAoB,IAAI;AAAA,IACpC,aAAa,eAAe,OAAO,IAAI;AAAA,EAC3C;AACJ;AAEA,SAAS,YAAY,MAAM;AACvB,SAAO,KAAK,cAAc,KAAK,WAAW;AAC9C;;;ACpfO,IAAM,kBAAkB;AAAA,EAC3B,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,qBAAqB;AAAA,EACrB,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,WAAW;AAAA,EACX,WAAW;AAAA,EACX,mBAAmB;AACvB;AAEA,IAAM,mBAAmB;AAEzB,IAAO,8BAAQ;AAAA,EAqBX,KAAQ;AAAA,IACJ,MAAM;AAAA,IACN,aAAa;AAAA,EACjB;AAAA,EACA,MAAQ;AAAA,IACJ,MAAM;AAAA,IACN,YAAY,UAAU;AAClB,YAAM,CAAC,EAAE,MAAM,IAAI,4BAA4B,UAAU,CAAC;AAC1D,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AA4GA,SAAS,aAAa,UAAU;AAC5B,QAAM,YAAY;AAClB,QAAM,QAAQ,CAAC;AACf,QAAM,QAAQ,CAAC;AAEf,WAAS,SAAS,GAAG,SAAS,SAAS,YAAY,UAAU,YAAY,kBAAkB;AACvF,UAAM,OAAO,cAAM,WAAW,UAAU,MAAM;AAC9C,QAAI,kBAAkB,OAAO;AACzB,UAAI,CAAC,MAAM,OAAO;AACd,cAAM,QAAQ,kBAAkB,MAAM;AAAA,MAC1C;AACA,YAAM,KAAK;AAAA,QACP;AAAA,QACA,MAAM,kBAAkB,MAAM,KAAK,UAAU,SAAS,SAAS;AAAA,MACnE,CAAC;AAAA,IACL;AAAA,EACJ;AACA,SAAO,KAAK,UAAU,EAAC,OAAO,MAAK,CAAC;AACxC;AAEA,IAAM,oBAAoB;AAAA,EACtB,CAAC,gBAAgB,wBAAwB;AAAA,IACrC,aAAa;AAAA,IACb,MAAM,CAAC,UAAU,WAAW,CAAC,cAAM,WAAW,UAAU,MAAM,CAAC;AAAA,EACnE;AAAA,EACA,CAAC,gBAAgB,+BAA+B;AAAA,IAC5C,aAAa;AAAA,IACb,MAAM;AAAA,EACV;AAAA,EACA,CAAC,gBAAgB,iCAAiC;AAAA,IAC9C,aAAa;AAAA,IACb,MAAM;AAAA,EACV;AAAA,EACA,CAAC,gBAAgB,sBAAsB;AAAA,IACnC,aAAa;AAAA,IACb,MAAM,CAAC,UAAU,WAAW,CAAC,cAAM,WAAW,UAAU,MAAM,CAAC;AAAA,EACnE;AAAA,EACA,CAAC,gBAAgB,6BAA6B;AAAA,IAC1C,aAAa;AAAA,IACb,MAAM;AAAA,EACV;AAAA,EACA,CAAC,gBAAgB,+BAA+B;AAAA,IAC5C,aAAa;AAAA,IACb,MAAM;AAAA,EACV;AAAA,EACA,CAAC,gBAAgB,YAAY;AAAA,IACzB,aAAa;AAAA,IACb,MAAM,MAAM,CAAC;AAAA,EACjB;AAAA,EACA,CAAC,gBAAgB,oBAAoB;AAAA,IACjC,aAAa;AAAA,IACb,MAAM,CAAC,UAAU,WAAW,CAAC,cAAM,WAAW,UAAU,MAAM,CAAC;AAAA,EACnE;AAAA,EACA,CAAC,gBAAgB,YAAY;AAAA,IACzB,aAAa;AAAA,IACb,MAAM;AAAA,EACV;AACJ;AAEA,SAAS,gBAAgB,UAAU,QAAQ;AACvC,QAAM,kBAAkB;AACxB,QAAM,OAAO,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,kBAAkB,KAAK,iBAAiB;AACxD,SAAK,KAAK,eAAe,UAAU,SAAS,CAAC,CAAC;AAAA,EAClD;AACA,SAAO;AACX;AAEA,SAAS,eAAe,UAAU,QAAQ;AACtC,QAAM,WAAW,oBAAoB,UAAU,QAAQ,CAAC;AACxD,QAAM,aAAa,oBAAoB,UAAU,SAAS,GAAG,CAAC;AAC9D,SAAO,CAAC,YAAY,QAAQ;AAChC;AAEA,SAAS,eAAe,UAAU,QAAQ;AACtC,SAAO;AAAA,IACH;AAAA,MACI,oBAAoB,UAAU,QAAQ,CAAC;AAAA,MACvC,oBAAoB,UAAU,SAAS,GAAG,CAAC;AAAA,MAC3C,oBAAoB,UAAU,SAAS,GAAG,CAAC;AAAA,MAC3C,oBAAoB,UAAU,SAAS,IAAI,CAAC;AAAA,IAChD;AAAA,IACA,oBAAoB,UAAU,SAAS,IAAI,CAAC;AAAA,EAChD;AACJ;AAEA,SAAS,oBAAoB,UAAU,QAAQ,aAAa;AACxD,QAAM,SAAS,cAAM,UAAU,UAAU,MAAM;AAE/C,QAAM,OAAQ,WAAW,OAAQ,IAAI,IAAI;AACzC,QAAM,WAAW,SAAS,gBAAiB,KAAK;AAChD,QAAMC,YAAW,SAAS,SAAS,UAAU,KAAK,KAAK,WAAW,GAAG,CAAC;AAEtE,SAAO,OAAO,gBAAgB,QAAQ,SAAS,CAAC,IAAI,MAAM,SAASA,UAAS,SAAS,CAAC,GAAG,KAAK,aAAa,GAAG,GAAG,CAAC;AACtH;;;ACpQA,IAAO,yBAAQ;AAAA,EACX,MAAAC;AACJ;AAEA,IAAM,YAAY;AAClB,IAAM,cAAc;AACpB,IAAM,uBAAuB;AAE7B,IAAM,iBAAiB,UAAU;AAEjC,SAASA,MAAK,OAAO,gBAAgB;AACjC,QAAM,WAAW,YAAY,IAAI,WAAW,KAAK,EAAE,MAAM;AACzD,QAAM,OAAO,CAAC;AACd,MAAI,SAAS;AAEb,SAAO,SAAS,MAAM,QAAQ;AAC1B,UAAM,YAAY,sBAAsB,UAAU,QAAQ,cAAc;AACxE,cAAU;AACV,UAAM,QAAQ,cAAM,WAAW,UAAU,MAAM;AAC/C,cAAU;AACV,UAAM,EAAC,SAAS,YAAW,IAAIC,YAAW,UAAU,MAAM;AAC1D,cAAU;AACV,UAAM,eAAe,cAAM,UAAU,UAAU,MAAM;AACrD,cAAU;AACV,QAAI,cAAc,WAAW;AACzB,YAAM,gBAAgB,YAAY,SAAS,QAAQ,QAAQ,YAAY;AACvE,YAAM,MAAM;AAAA,QACR,IAAI;AAAA,QACJ,OAAO,sBAAsB,eAAe,GAAG,YAAY;AAAA,MAC/D;AACA,UAAI,4BAAS,QAAQ;AACjB,YAAI;AACA,cAAI,cAAc,4BAAS,OAAO,YAAY,aAAa;AAAA,QAC/D,SAAS,OAAP;AACE,cAAI,cAAc;AAAA,QACtB;AACA,aAAK,UAAU,UAAU,4BAAS,OAAO,QAAQ;AAAA,MACrD,WAAW,gBAAgB;AACvB,aAAK,aAAa,WAAW;AAAA,MACjC;AAAA,IACJ;AACA,cAAU,eAAgB,eAAe;AAAA,EAC7C;AAEA,SAAO;AACX;AAEA,SAASA,YAAW,UAAU,QAAQ;AAIlC,QAAM,CAAC,YAAY,MAAM,IAAI,4BAA4B,UAAU,MAAM;AACzE,SAAO;AAAA,IACH,SAAS;AAAA,IACT,aAAa,IAAI,cAAc,aAAa,MAAM,IAAI,IAAI;AAAA,EAC9D;AACJ;;;AC5DO,IAAM,UAAU;AAAA,EACnB,QAAQ;AAAA,IACJ,QAAQ;AAAA,EACZ;AAAA,EACA,QAAQ;AAAA,IACJ,QAAQ;AAAA,EACZ;AAAA,EACA,QAAQ;AAAA,IACJ,QAAQ;AAAA,EACZ;AAAA,EACA,QAAQ;AAAA,IACJ,QAAQ;AAAA,EACZ;AAAA,EACA,QAAQ;AAAA,IACJ,QAAQ;AAAA,EACZ;AAAA,EACA,QAAQ;AAAA,IACJ,QAAQ;AAAA,EACZ;AACJ;AAEO,IAAM,aAAa;AAAA,EACtB,GAAG;AAAA,IACC,QAAQ;AAAA,IACR,SAAS,CAAC,UAAU,WAAW,sBAAsB,UAAU,QAAQ,CAAC;AAAA,IACxE,eAAe,CAAC,UAAU,UAAU,OAAO,UAAU,KAAK,IAAI;AAAA,EAClE;AAAA,EACA,GAAG;AAAA,IACC,QAAQ;AAAA,IACR,SAAS,CAAC,UAAU,WAAW;AAC3B,aAAQ,SAAS,SAAS,MAAM,EAAG,SAAS,EAAE,IAAI,OAC/C,SAAS,SAAS,SAAS,CAAC,KAAK,GAAG,SAAS,EAAE,IAAI,OACnD,SAAS,SAAS,SAAS,CAAC,IAAI,IAAI,SAAS,EAAE;AAAA,IACtD;AAAA,EACJ;AAAA,EACA,IAAI;AAAA,IACA,QAAQ;AAAA,IACR,SAAS,CAAC,UAAU,WAAW,sBAAsB,UAAU,QAAQ,CAAC;AAAA,IACxE,eAAe,CAAC,UAAU;AACtB,cAAQ,MAAM,YAAY,GAAG;AAAA,QACzB,KAAK;AAAQ,iBAAO;AAAA,QACpB,KAAK;AAAQ,iBAAO;AAAA,QACpB,KAAK;AAAQ,iBAAO;AAAA,QACpB,KAAK;AAAQ,iBAAO;AAAA,QACpB,KAAK;AAAQ,iBAAO;AAAA,QACpB,KAAK;AAAQ,iBAAO;AAAA,QACpB,KAAK;AAAQ,iBAAO;AAAA,QACpB,KAAK;AAAQ,iBAAO;AAAA,QACpB,KAAK;AAAQ,iBAAO;AAAA,QACpB,KAAK;AAAQ,iBAAO;AAAA,QACpB,KAAK;AAAQ,iBAAO;AAAA,QACpB;AAAS,iBAAO;AAAA,MACpB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,IAAI;AAAA,IACA,QAAQ;AAAA,IACR,SAAS,CAAC,UAAU,WAAW,sBAAsB,UAAU,QAAQ,CAAC;AAAA,EAC5E;AAAA,EACA,IAAI;AAAA,IACA,QAAQ;AAAA,IACR,SAAS,CAAC,UAAU,WAAW,sBAAsB,UAAU,QAAQ,CAAC;AAAA,EAC5E;AAAA,EACA,IAAI;AAAA,IACA,QAAQ;AAAA,IACR,SAAS,CAAC,UAAU,WAAW,UAAU,UAAU,MAAM,EAAE,YAAY;AAAA,EAC3E;AAAA,EACA,IAAI;AAAA,IACA,QAAQ;AAAA,IACR,SAAS,CAAC,UAAU,WAAW,cAAc,SAAS,OAAO,MAAM,QAAQ,SAAS,CAAC,CAAC;AAAA,EAC1F;AAAA,EACA,IAAI;AAAA,IACA,QAAQ;AAAA,IACR,SAAS,CAAC,UAAU,WAAW,sBAAsB,UAAU,QAAQ,CAAC;AAAA,IACxE,eAAe,CAAC,UAAU,UAAU,KAAK;AAAA,EAC7C;AAAA,EACA,IAAI;AAAA,IACA,QAAQ;AAAA,IACR,SAAS,CAAC,UAAU,WAAW,sBAAsB,UAAU,QAAQ,CAAC;AAAA,IACxE,eAAe,CAAC,UAAU,UAAU,KAAK;AAAA,EAC7C;AAAA,EACA,IAAI;AAAA,IACA,QAAQ;AAAA,IACR,SAAS,CAAC,UAAU,WAAW,sBAAsB,UAAU,QAAQ,CAAC;AAAA,EAC5E;AAAA,EACA,IAAI;AAAA,IACA,QAAQ;AAAA,IACR,SAAS,CAAC,UAAU,WAAW,SAAS,UAAU,MAAM;AAAA,IACxD,eAAe,CAAC,UAAU;AACtB,cAAQ,OAAO;AAAA,QACX,KAAK;AAAG,iBAAO;AAAA,QACf,KAAK;AAAG,iBAAO;AAAA,QACf,KAAK;AAAG,iBAAO;AAAA,QACf,KAAK;AAAG,iBAAO;AAAA,QACf;AAAS,iBAAO;AAAA,MACpB;AAAA,IACJ;AAAA,EACJ;AAAA,EAEA,IAAI;AAAA,IACA,QAAQ;AAAA,IACR,SAAS,CAAC,UAAU,WAAW,sBAAsB,UAAU,QAAQ,CAAC;AAAA,EAC5E;AACJ;AAEA,SAAS,UAAU,UAAU,QAAQ;AACjC,QAAM,OAAO,SAAS,UAAU,MAAM;AACtC,QAAM,QAAQ,SAAS,UAAU,SAAS,CAAC,IAAI;AAC/C,QAAM,MAAM,SAAS,UAAU,SAAS,CAAC;AACzC,QAAM,QAAQ,SAAS,UAAU,SAAS,CAAC;AAC3C,QAAM,UAAU,SAAS,UAAU,SAAS,CAAC;AAC7C,QAAM,UAAU,SAAS,UAAU,SAAS,EAAE;AAC9C,SAAO,IAAI,KAAK,KAAK,IAAI,MAAM,OAAO,KAAK,OAAO,SAAS,OAAO,CAAC;AACvE;AAEA,SAAS,cAAc,OAAO;AAC1B,SAAO,OAAO,aAAa,MAAM,MAAM,IAAI,WAAW,KAAK,CAAC;AAChE;AAEA,SAAS,UAAU,OAAO;AACtB,UAAQ,MAAM,YAAY,GAAG;AAAA,IACzB,KAAK;AAAQ,aAAO;AAAA,IACpB,KAAK;AAAQ,aAAO;AAAA,IACpB,KAAK;AAAQ,aAAO;AAAA,IACpB,KAAK;AAAQ,aAAO;AAAA,IACpB,KAAK;AAAO,aAAO;AAAA,IACnB,KAAK;AAAQ,aAAO;AAAA,IACpB;AAAS,aAAO;AAAA,EACpB;AACJ;;;ACjIA,IAAO,mBAAQ;AAAA,EACX,MAAAC;AACJ;AAEA,IAAM,wBAAwB;AAC9B,IAAM,uBAAuB;AAC7B,IAAM,gBAAgB;AACtB,IAAM,gBAAgB;AACtB,IAAM,wCAAwC;AAC9C,IAAM,gBAAgB;AACtB,IAAM,qBAAqB;AAC3B,IAAM,4BAA4B;AAOlC,SAASA,MAAK,UAAU,SAAS,OAAO;AACpC,MAAI,SAAS,QAAQ,GAAG,sBAAsB,yBAAyB;AACnE,WAAO,kBAAkB,UAAU,OAAO;AAAA,EAC9C;AAEA,SAAO,QAAQ,UAAU,OAAO;AACpC;AAEA,SAAS,kBAAkB,UAAU,SAAS;AAC1C,MAAI,CAAC,6BAA6B,QAAQ,GAAG,iBAAiB,GAAG;AAC7D,WAAO,CAAC;AAAA,EACZ;AACA,QAAM,qBAAqB,IAAI,SAAS,SAAS,OAAO,MAAM,QAAQ,GAAG,QAAQ,QAAQ,GAAG,SAAS,QAAQ,GAAG,MAAM,CAAC;AACvH,SAAO,WAAW,oBAAoB,QAAQ,GAAG,mBAAmB,SAAS,UAAU,EAClF,KAAKC,UAAS,EACd,MAAM,OAAO,CAAC,EAAE;AACzB;AAEA,SAAS,6BAA6B,mBAAmB;AACrD,SAAO,sBAAsB;AACjC;AAEA,SAAS,QAAQ,UAAU,SAAS;AAChC,MAAI;AACA,UAAM,wBAAwB,QAAQ,OAAO,CAAC,KAAK,QAAQ,MAAM,IAAI,QAAQ,CAAC;AAE9E,UAAM,gBAAgB,IAAI,WAAW,qBAAqB;AAC1D,QAAI,SAAS;AACb,UAAM,SAAS,UAAU,QAAQ;AAEjC,aAAS,cAAc,GAAG,eAAe,QAAQ,QAAQ,eAAe;AACpE,YAAM,eAAe,QAAQ,KAAK,CAAC,MAAM,EAAE,gBAAgB,WAAW;AACtE,UAAI,CAAC,cAAc;AACf,cAAM,IAAI,MAAM,aAAa,uBAAuB;AAAA,MACxD;AAEA,YAAM,OAAO,OAAO,MAAM,aAAa,QAAQ,aAAa,SAAS,aAAa,MAAM;AACxF,YAAM,YAAY,IAAI,WAAW,IAAI;AAErC,oBAAc,IAAI,WAAW,MAAM;AACnC,gBAAU,UAAU;AAAA,IACxB;AAEA,WAAOA,WAAU,IAAI,SAAS,cAAc,MAAM,CAAC;AAAA,EACvD,SAAS,OAAP;AACE,WAAO,CAAC;AAAA,EACZ;AACJ;AAEA,SAAS,UAAU,UAAU;AACzB,MAAI,MAAM,QAAQ,QAAQ,GAAG;AACzB,WAAQ,IAAI,SAAS,WAAW,KAAK,QAAQ,EAAE,MAAM,EAAG;AAAA,EAC5D;AACA,SAAO,SAAS;AACpB;AAEA,SAAS,uBAAuB,QAAQ;AACpC,SAAO,OAAO,SAAU,uBAAuB;AACnD;AAEA,SAAS,YAAY,QAAQ,iBAAiB;AAC1C,SAAO,OAAO,SAAS,kBAAkB;AAC7C;AAEO,SAASA,WAAU,UAAU;AAChC,QAAM,SAAS,SAAS;AAExB,QAAM,SAAS,SAAS,UAAU;AAClC,MAAI,SAAS,eAAe,QAAQ;AAChC,UAAM,IAAI,MAAM,iCAAiC;AAAA,EACrD;AAEA,MAAI,SAAS,SAAS,uBAAuB;AACzC,UAAM,IAAI,MAAM,uBAAuB;AAAA,EAC3C;AAEA,QAAM,OAAO,CAAC;AAEd,QAAM,iBAAiB,OAAO,KAAK,UAAU;AAC7C,WAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC5C,UAAM,SAAS,eAAe;AAC9B,UAAM,eAAe,WAAW;AAChC,UAAM,QAAQ,aAAa,MAAM,UAAU,SAAS,QAAQ,EAAE,CAAC;AAC/D,QAAI,cAAc;AAClB,QAAI,aAAa,aAAa;AAC1B,oBAAc,aAAa,YAAY,KAAK;AAAA,IAChD;AAEA,SAAK,aAAa,QAAQ;AAAA,MACtB;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAEA,QAAM,YAAYC,eAAc,OAAO,MAAM,IAAI,EAAE,CAAC;AACpD,MAAI,cAAc,eAAe;AAC7B,UAAM,IAAI,MAAM,gCAAgC;AAAA,EACpD;AAGA,MAAI,uBAAuB,MAAM,GAAG;AAChC,WAAO;AAAA,EACX;AAEA,QAAM,WAAW,SAAS,UAAU,GAAG;AACvC,MAAI,kBAAkB;AAEtB,WAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AAC/B,QAAI,YAAY,QAAQ,eAAe,GAAG;AAEtC,aAAO;AAAA,IACX;AACA,UAAM,eAAe,sBAAsB,UAAU,iBAAiB,CAAC;AACvE,UAAM,YAAY,SAAS,UAAU,kBAAkB,CAAC;AACxD,UAAM,UAAU,SAAS,UAAU,kBAAkB,CAAC;AAEtD,QAAI,YAAY,OAAO,QAAQ;AAE3B,aAAO;AAAA,IACX;AACA,UAAM,UAAU,sBAAsB,UAAU,WAAW,CAAC;AAE5D,QAAI,YAAY,eAAe;AAC3B,YAAM,eAAe,SAAS,UAAU,YAAY,CAAC;AACrD,UAAI,eAAe,SAAS;AAExB,eAAO;AAAA,MACX;AAEA,YAAM,MAAMA,eAAc,OAAO,MAAM,YAAY,IAAI,YAAY,eAAe,EAAE,CAAC;AACrF,aAAO,MAAM,cAAc,GAAG;AAAA,IAClC,WAAW,YAAY,uCAAuC;AAC1D,YAAM,aAAa,SAAS,UAAU,YAAY,CAAC;AACnD,YAAM,aAAa,SAAS,UAAU,YAAY,EAAE;AACpD,UAAI,SAAS,YAAY;AACzB,YAAM,MAAM,CAAC;AACb,eAAS,YAAY,GAAG,YAAY,YAAY,aAAa;AACzD,cAAM,eAAe,sBAAsB,UAAU,SAAS,GAAG,CAAC;AAClE,cAAM,cAAc,sBAAsB,UAAU,SAAS,GAAG,CAAC;AACjE,cAAM,aAAa,SAAS,UAAU,SAAS,CAAC;AAChD,cAAM,aAAa,SAAS,UAAU,SAAS,CAAC;AAEhD,cAAM,OAAO,6BAA6B,UAAU,YAAY,YAAY,UAAU;AACtF,YAAI,KAAK,EAAC,cAAc,aAAa,KAAI,CAAC;AAC1C,kBAAU;AAAA,MACd;AACA,UAAI,eAAe,GAAG;AAClB,eAAO,MAAM,cAAc,IAAI,GAAG,IAAI;AAAA,MAC1C,OAAO;AACH,cAAM,SAAS,CAAC;AAChB,iBAAS,WAAW,GAAG,WAAW,IAAI,QAAQ,YAAY;AACtD,iBAAO,GAAG,IAAI,UAAU,gBAAgB,IAAI,UAAU,iBAAiB,IAAI,UAAU;AAAA,QACzF;AACA,eAAO,MAAM,cAAc,MAAM;AAAA,MACrC;AAAA,IACJ,WAAW,YAAY,eAAe;AAClC,YAAM,MAAMA,eAAc,OAAO,MAAM,YAAY,GAAG,YAAY,UAAU,CAAC,CAAC;AAC9E,aAAO,MAAM,cAAc,GAAG;AAAA,IAClC,WAAW,YAAY,oBAAoB;AACvC,YAAM,MAAMA,eAAc,OAAO,MAAM,YAAY,GAAG,YAAY,EAAE,CAAC;AACrE,aAAO,MAAM,cAAc,GAAG;AAAA,IAClC;AACA,sBAAkB,kBAAkB;AAAA,EACxC;AAEA,SAAO;AACX;AAEA,SAASA,eAAc,OAAO;AAC1B,SAAO,OAAO,aAAa,MAAM,MAAM,IAAI,WAAW,KAAK,CAAC;AAChE;AAEA,SAAS,OAAO,MAAM,cAAc,OAAO;AACvC,MAAI,QAAQ,eAAe;AACvB,SAAK,QAAQ,cAAc,QAAQ,EAAC,OAAO,aAAa,MAAK;AAAA,EACjE,OAAO;AACH,SAAK,gBAAgB,EAAC,OAAO,aAAa,MAAK;AAAA,EACnD;AACJ;;;AC7LA,IAAM,wBAAwB;AAE9B,IAAO,qBAAQ;AAAA,EACX,MAAAC;AAAA,EACA;AACJ;AAEA,SAASA,MAAK,UAAU,kBAAkB,QAAQ,WAAW,gBAAgB;AACzE,MAAI,OAAO,QAAQ,UAAU,gBAAgB,kBAAkB,mBAAmB,QAAQ,WAAW,cAAc;AAEnH,MAAI,KAAK,aAAa;AAClB,WAAO,aAAa,CAAC,GAAG,MAAM,cAAc,KAAK,YAAY,KAAK,CAAC;AACnE,WAAO,KAAK;AAAA,EAChB;AAEA,SAAO;AACX;AAEA,SAAS,cAAc,cAAc;AACjC,QAAM,OAAO,CAAC;AAEd,MAAI,aAAa,2BAA2B,QAAW;AACnD,SAAK,gBAAgB;AAAA,MACjB,OAAO,aAAa;AAAA,MACpB,aAAa,yBAAyB,aAAa,sBAAsB;AAAA,IAC7E;AAAA,EACJ;AAEA,SAAO;AACX;AAEA,SAAS,yBAAyB,YAAY;AAC1C,MAAI,eAAe,GAAG;AAClB,WAAO;AAAA,EACX;AACA,MAAI,eAAe,GAAG;AAClB,WAAO;AAAA,EACX;AACA,MAAI,eAAe,GAAG;AAClB,WAAO;AAAA,EACX;AACA,MAAI,eAAe,GAAG;AAClB,WAAO;AAAA,EACX;AACA,SAAO;AACX;;;ACpDA,IAAO,wBAAQ;AAAA,EACX,MAAAC;AACJ;AAEA,SAASA,OAAK,UAAU,gBAAgB;AACpC,SAAO;AAAA,IACH,eAAeC,eAAc,UAAU,cAAc;AAAA,IACrD,gBAAgBC,gBAAe,UAAU,cAAc;AAAA,IACvD,aAAa,YAAY,UAAU,cAAc;AAAA,IACjD,cAAc,aAAa,UAAU,cAAc;AAAA,IACnD,eAAe,eAAe,UAAU,cAAc;AAAA,IACtD,UAAU,UAAU,UAAU,cAAc;AAAA,IAC5C,aAAa,aAAa,UAAU,cAAc;AAAA,EACtD;AACJ;AAEA,SAASD,eAAc,UAAU,gBAAgB;AAC7C,QAAM,SAAS;AACf,QAAM,OAAO;AAEb,MAAI,iBAAiB,SAAS,OAAO,SAAS,YAAY;AACtD,WAAO;AAAA,EACX;AAEA,QAAM,QAAQ,cAAM,UAAU,UAAU,cAAc;AACtD,SAAO;AAAA,IACH;AAAA,IACA,aAAa,GAAG;AAAA,EACpB;AACJ;AAEA,SAASC,gBAAe,UAAU,gBAAgB;AAC9C,QAAM,SAAS;AACf,QAAM,OAAO;AAEb,MAAI,iBAAiB,SAAS,OAAO,SAAS,YAAY;AACtD,WAAO;AAAA,EACX;AAEA,QAAM,QAAQ,cAAM,UAAU,UAAU,iBAAiB,MAAM;AAC/D,SAAO;AAAA,IACH;AAAA,IACA,aAAa,GAAG;AAAA,EACpB;AACJ;AAEA,SAAS,YAAY,UAAU,gBAAgB;AAC3C,QAAM,SAAS;AACf,QAAM,OAAO;AAEb,MAAI,iBAAiB,SAAS,OAAO,SAAS,YAAY;AACtD,WAAO;AAAA,EACX;AAEA,QAAM,QAAQ,cAAM,UAAU,UAAU,iBAAiB,MAAM;AAC/D,SAAO;AAAA,IACH;AAAA,IACA,aAAa,GAAG;AAAA,EACpB;AACJ;AAEA,SAAS,aAAa,UAAU,gBAAgB;AAC5C,QAAM,SAAS;AACf,QAAM,OAAO;AACb,QAAM,cAAc;AAAA,IAChB,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACP;AAEA,MAAI,iBAAiB,SAAS,OAAO,SAAS,YAAY;AACtD,WAAO;AAAA,EACX;AAEA,QAAM,QAAQ,cAAM,UAAU,UAAU,iBAAiB,MAAM;AAC/D,SAAO;AAAA,IACH;AAAA,IACA,aAAa,YAAY,UAAU;AAAA,EACvC;AACJ;AAEA,SAAS,eAAe,UAAU,gBAAgB;AAC9C,QAAM,SAAS;AACf,QAAM,OAAO;AAEb,MAAI,iBAAiB,SAAS,OAAO,SAAS,YAAY;AACtD,WAAO;AAAA,EACX;AAEA,QAAM,QAAQ,cAAM,UAAU,UAAU,iBAAiB,MAAM;AAC/D,SAAO;AAAA,IACH;AAAA,IACA,aAAa,UAAU,IAAI,oBAAoB;AAAA,EACnD;AACJ;AAEA,SAAS,UAAU,UAAU,gBAAgB;AACzC,QAAM,SAAS;AACf,QAAM,OAAO;AAEb,MAAI,iBAAiB,SAAS,OAAO,SAAS,YAAY;AACtD,WAAO;AAAA,EACX;AAEA,QAAM,QAAQ,cAAM,UAAU,UAAU,iBAAiB,MAAM;AAC/D,SAAO;AAAA,IACH;AAAA,IACA,aAAa,UAAU,IAAI,aAAa;AAAA,EAC5C;AACJ;AAEA,SAAS,aAAa,UAAU,gBAAgB;AAC5C,QAAM,SAAS;AACf,QAAM,OAAO;AACb,QAAM,kBAAkB;AAAA,IACpB,GAAG;AAAA,IACH,GAAG;AAAA,EACP;AAEA,MAAI,iBAAiB,SAAS,OAAO,SAAS,YAAY;AACtD,WAAO;AAAA,EACX;AAEA,QAAM,QAAQ,cAAM,UAAU,UAAU,iBAAiB,MAAM;AAC/D,SAAO;AAAA,IACH;AAAA,IACA,aAAa,gBAAgB,UAAU;AAAA,EAC3C;AACJ;;;AC3HA,IAAO,wBAAQ;AAAA,EACX,MAAAC;AACJ;AAEA,IAAM,gBAAgB;AACtB,IAAM,oBAAoB;AAC1B,IAAM,aAAa;AACnB,IAAM,2BAA2B;AACjC,IAAM,aAAa;AACnB,IAAM,sCAAsC;AAC5C,IAAM,8BAA8B;AACpC,IAAM,cAAc;AAEpB,SAASA,OAAK,UAAU,eAAe,OAAO,gBAAgB;AAC1D,QAAM,OAAO,CAAC;AACd,QAAM,eAAe,CAAC;AACtB,WAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC3C,UAAM,EAAC,QAAQ,QAAQ,KAAI,IAAI,cAAc;AAC7C,UAAM,eAAe,gBAAgB,UAAU,QAAQ,QAAQ,MAAM,KAAK;AAC1E,QAAI,wBAAwB,SAAS;AACjC,mBAAa,KAAK,aAAa,KAAK,CAAC,EAAC,MAAM,OAAO,YAAW,MAAM;AAChE,YAAI;AACA,cAAI,kBAAU,YAAY,eAAe,MAAM,KAAK,GAAG;AACnD,mBAAO;AAAA,cACH,QAAQ,aAAK,KAAK,cAAc,KAAK,GAAG,aAAa,cAAc,EAAE;AAAA,YACzE;AAAA,UACJ,WAAW,kBAAU,YAAY,eAAe,MAAM,KAAK,GAAG;AAC1D,mBAAO;AAAA,cACH,QAAQ,kBAAS,KAAK,cAAc,KAAK,GAAG,GAAG,cAAc;AAAA,YACjE;AAAA,UACJ,WAAW,QAAQ,CAAC,eAAe,MAAM,KAAK,KAAK,CAAC,eAAe,MAAM,KAAK,GAAG;AAC7E,mBAAO;AAAA,cACH,CAAC,OAAO;AAAA,gBACJ;AAAA,gBACA;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ,SAAS,OAAP;AAAA,QAEF;AACA,eAAO,CAAC;AAAA,MACZ,CAAC,CAAC;AAAA,IACN,OAAO;AACH,YAAM,EAAC,MAAM,OAAO,YAAW,IAAI;AACnC,UAAI,MAAM;AACN,aAAK,QAAQ;AAAA,UACT;AAAA,UACA;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAEA,SAAO;AAAA,IACH,UAAU;AAAA,IACV,iBAAiB,aAAa,SAAS,IAAI,QAAQ,IAAI,YAAY,IAAI;AAAA,EAC3E;AACJ;AAEA,SAAS,gBAAgB,UAAU,QAAQ,QAAQ,MAAM,OAAO;AAC5D,QAAM,eAAe,CAAC;AACtB,QAAM,YAAY,CAAC;AACnB,QAAM,yBAAyB,CAAC;AAChC,MAAI;AACJ,MAAI,eAAe;AACnB,MAAI,oBAAoB;AAExB,WAAS,IAAI,GAAG,IAAI,UAAU,SAAS,IAAI,SAAS,YAAY,KAAK;AACjE,QAAI,iBAAiB,mBAAmB;AACpC,0BAAoB,qBAAqB,EAAC,MAAM,UAAU,QAAQ,SAAS,EAAC,CAAC;AAC7E,UAAI,SAAS,WAAW;AACpB,aAAK;AAAA,MACT;AACA,qBAAe,gBAAgB,MAAM,YAAY;AACjD;AAAA,IACJ,WAAW,iBAAiB,YAAY;AACpC,mBAAa,IAAI,SAAS,SAAS,OAAO,MAAM,SAAS,GAAG,SAAS,MAAM,CAAC;AAC5E;AAAA,IACJ;AACA,UAAM,OAAO,SAAS,SAAS,SAAS,CAAC;AACzC,QAAI,SAAS,GAAG;AACZ,qBAAe,gBAAgB,MAAM,YAAY;AAAA,IACrD,WAAW,iBAAiB,eAAe;AACvC,mBAAa,KAAK,IAAI;AAAA,IAC1B,WAAW,iBAAiB,YAAY;AACpC,gBAAU,KAAK,IAAI;AAAA,IACvB,WAAW,iBAAiB,0BAA0B;AAClD,6BAAuB,KAAK,IAAI;AAAA,IACpC;AAAA,EACJ;AAEA,MAAI,sBAAsB,2BAA2B,CAAC,OAAO;AACzD,WAAO,CAAC;AAAA,EACZ;AACA,QAAM,yBAAyB,WAAW,YAAY,mBAAmB,oBAAoB,IAAI,CAAC;AAClG,MAAI,kCAAkC,SAAS;AAC3C,WAAO,uBACF,KAAK,CAAC,4BAA4B,aAAa,yBAAyB,MAAM,WAAW,YAAY,CAAC,EACtG,MAAM,MAAM,aAAa,mCAAmC,MAAM,EAAE,GAAG,MAAM,WAAW,YAAY,CAAC;AAAA,EAC9G;AACA,SAAO,aAAa,wBAAwB,MAAM,WAAW,YAAY;AAC7E;AAEA,SAAS,qBAAqB,EAAC,MAAM,UAAU,OAAM,GAAG;AACpD,MAAI,SAAS,WAAW;AACpB,QAAI,SAAS,SAAS,MAAM,MAAM,6BAA6B;AAC3D,aAAO,SAAS,SAAS,SAAS,CAAC;AAAA,IACvC;AAAA,EACJ,WAAW,SAAS,WAAW;AAC3B,WAAO,SAAS,SAAS,MAAM;AAAA,EACnC;AACA,SAAO;AACX;AAEA,SAAS,gBAAgB,MAAM,cAAc;AACzC,MAAI,iBAAiB,iBAAiB,CAAC,WAAW,SAAS,EAAE,SAAS,IAAI,GAAG;AACzE,WAAO;AAAA,EACX;AACA,MAAI,iBAAiB,mBAAmB;AACpC,QAAI,SAAS,WAAW;AACpB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACA,MAAI,iBAAiB,YAAY;AAC7B,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAEA,SAAS,oBAAoB,MAAM;AAC/B,MAAI,SAAS,aAAa,SAAS,WAAW;AAC1C,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAEA,SAAS,aAAa,YAAY,MAAM,WAAW,cAAc;AAC7D,QAAM,QAAQ,SAAS,UAAU;AACjC,SAAO;AAAA,IACH,MAAM,QAAQ,MAAM,WAAW,YAAY;AAAA,IAC3C;AAAA,IACA,aAAa,SAAS,YAAYC,gBAAe,UAAU,IAAI;AAAA,EACnE;AACJ;AAEA,SAAS,QAAQ,MAAM,WAAW,cAAc;AAC5C,QAAM,OAAO,wBAAwB,YAAY;AACjD,MAAI,SAAS,aAAa,UAAU,WAAW,GAAG;AAC9C,WAAO;AAAA,EACX;AACA,QAAM,OAAO,wBAAwB,SAAS;AAC9C,SAAO,GAAG,SAAS;AACvB;AAEA,SAAS,SAAS,YAAY;AAC1B,MAAI,sBAAsB,UAAU;AAChC,WAAO,sBAAsB,YAAY,GAAG,WAAW,UAAU;AAAA,EACrE;AACA,SAAO;AACX;AAEA,SAASA,gBAAe,YAAY;AAChC,SAAO,oBAAW,OAAO,SAAS,UAAU;AAChD;AAEA,SAAS,eAAe,MAAM,OAAO;AACjC,SAAO,KAAK,YAAY,MAAM,2BAA2B,MAAM,UAAU,GAAG,CAAC,MAAM;AACvF;AAEA,SAAS,eAAe,MAAM,OAAO;AACjC,SAAO,KAAK,YAAY,MAAM,2BAA2B,MAAM,UAAU,GAAG,CAAC,MAAM;AACvF;AAEA,SAAS,cAAc,OAAO;AAC1B,QAAM,QAAQ,MAAM,MAAM,mCAAmC;AAC7D,SAAO,cAAc,MAAM,GAAG,QAAQ,OAAO,EAAE,CAAC;AACpD;AAEA,SAAS,cAAc,KAAK;AACxB,QAAM,WAAW,IAAI,SAAS,IAAI,YAAY,IAAI,SAAS,CAAC,CAAC;AAC7D,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,GAAG;AACpC,aAAS,SAAS,IAAI,GAAG,SAAS,IAAI,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AAAA,EAClE;AACA,SAAO;AACX;;;AC/LA,IAAO,mBAAQ;AAAA,EACX,MAAAC;AACJ;AAEA,SAASA,OAAK,UAAU,cAAc;AAClC,QAAM,OAAO,CAAC;AAEd,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,UAAM,cAAc,cAAM,UAAU,UAAU,aAAa,KAAK,uBAAuB;AACvF,UAAM,YAAY,sBAAsB,UAAU,aAAa,KAAK,uBAAuB,mBAAmB;AAE9G,QAAI,cAAc,WAAW;AACzB,WAAK,uBAAuB,kBAAkB,UAAU,aAAa,IAAI,WAAW;AACpF,WAAK,uBAAuB,kBAAkB,UAAU,aAAa,IAAI,WAAW;AACpF,WAAK,iBAAiB,cAAc,UAAU,aAAa,IAAI,WAAW;AAAA,IAC9E,WAAW,cAAc,WAAW;AAChC,WAAK,iBAAiB,cAAc,UAAU,aAAa,IAAI,WAAW;AAAA,IAC9E;AAAA,EACJ;AAEA,SAAO;AACX;AAEA,SAAS,kBAAkB,UAAU,aAAa,aAAa;AAC3D,QAAM,aAAa;AACnB,QAAM,WAAW;AAEjB,MAAI,CAAC,gBAAgB,UAAU,aAAa,aAAa,YAAY,QAAQ,GAAG;AAC5E,WAAO;AAAA,EACX;AAEA,QAAM,QAAQ,cAAM,UAAU,UAAU,cAAc,wBAAwB,UAAU;AAExF,SAAO;AAAA,IACH;AAAA,IACA,aAAa,KAAK;AAAA,EACtB;AACJ;AAEA,SAAS,kBAAkB,UAAU,aAAa,aAAa;AAC3D,QAAM,aAAa;AACnB,QAAM,WAAW;AAEjB,MAAI,CAAC,gBAAgB,UAAU,aAAa,aAAa,YAAY,QAAQ,GAAG;AAC5E,WAAO;AAAA,EACX;AAEA,QAAM,QAAQ,cAAM,UAAU,UAAU,cAAc,wBAAwB,UAAU;AAExF,SAAO;AAAA,IACH;AAAA,IACA,aAAa,KAAK;AAAA,EACtB;AACJ;AAEA,SAAS,cAAc,UAAU,aAAa,aAAa;AACvD,QAAM,aAAa;AACnB,QAAM,WAAW;AAEjB,MAAI,CAAC,gBAAgB,UAAU,aAAa,aAAa,YAAY,QAAQ,GAAG;AAC5E,WAAO;AAAA,EACX;AAEA,QAAM,QAAQ,cAAM,UAAU,UAAU,cAAc,wBAAwB,UAAU;AAExF,SAAO;AAAA,IACH;AAAA,IACA,aAAa,UAAU,IAAI,WAAW;AAAA,EAC1C;AACJ;AAEA,SAAS,cAAc,UAAU,aAAa,aAAa;AACvD,QAAM,gBAAgB;AAEtB,MAAI,CAAC,gBAAgB,UAAU,aAAa,aAAa,GAAG,aAAa,GAAG;AACxE,WAAO;AAAA,EACX;AAEA,QAAM,OAAO,cAAM,WAAW,UAAU,cAAc,qBAAqB;AAC3E,QAAM,QAAQ,cAAM,UAAU,UAAU,cAAc,wBAAwB,CAAC;AAC/E,QAAM,MAAM,cAAM,UAAU,UAAU,cAAc,wBAAwB,CAAC;AAC7E,QAAM,QAAQ,cAAM,UAAU,UAAU,cAAc,wBAAwB,CAAC;AAC/E,QAAM,UAAU,cAAM,UAAU,UAAU,cAAc,wBAAwB,CAAC;AACjF,QAAM,UAAU,cAAM,UAAU,UAAU,cAAc,wBAAwB,CAAC;AAEjF,SAAO;AAAA,IACH,OAAO,CAAC,MAAM,OAAO,KAAK,OAAO,SAAS,OAAO;AAAA,IACjD,aAAa,GAAG,IAAI,MAAM,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK,IAAI,SAAS,CAAC,KAAK,IAAI,SAAS,CAAC;AAAA,EACtH;AACJ;AAEA,SAAS,gBAAgB,UAAU,aAAa,aAAa,WAAW,SAAS;AAC7E,SAAO,YAAY,WAAW,eAAe,cAAc,wBAAwB,YAAY,WAAW,SAAS;AACvH;AAEA,SAAS,IAAI,QAAQ,MAAM;AACvB,SAAO,GAAG,IAAI,OAAO,QAAQ,KAAK,QAAQ,MAAM,IAAI;AACxD;;;ACnGA,IAAO,oBAAQ;AAAA,EACX,MAAAC;AACJ;AAEA,IAAM,qBAAqB;AAC3B,IAAM,sBAAsB;AAG5B,SAASA,OAAK,UAAU,aAAa;AACjC,QAAM,OAAO,CAAC;AAEd,QAAM,QAAQ,cAAM,UAAU,UAAU,WAAW;AAEnD,OAAK,WAAW,SAAS,KAAK;AAC9B,OAAK,eAAe,aAAa,KAAK;AACtC,OAAK,gBAAgB,kBAAkB,UAAU,cAAc,kBAAkB;AACjF,OAAK,iBAAiB,kBAAkB,UAAU,cAAc,mBAAmB;AAEnF,SAAO;AACX;AAEA,SAAS,SAAS,OAAO;AACrB,QAAM,QAAQ,QAAQ;AACtB,SAAO;AAAA,IACH,OAAO,QAAQ,IAAI;AAAA,IACnB,aAAa,QAAQ,QAAQ;AAAA,EACjC;AACJ;AAEA,SAAS,aAAa,OAAO;AACzB,QAAM,QAAQ,QAAQ;AACtB,SAAO;AAAA,IACH,OAAO,QAAQ,IAAI;AAAA,IACnB,aAAa,QAAQ,QAAQ;AAAA,EACjC;AACJ;AAEA,SAAS,kBAAkB,UAAU,QAAQ;AAEzC,QAAM,QAAQ,cAAM,UAAU,UAAU,MAAM,IACxC,MAAM,cAAM,UAAU,UAAU,SAAS,CAAC,IAC1C,MAAM,MAAM,cAAM,UAAU,UAAU,SAAS,CAAC,IAChD;AAEN,SAAO;AAAA,IACH;AAAA,IACA,aAAa,QAAQ;AAAA,EACzB;AACJ;;;AC7CA,IAAO,wBAAQ;AAAA,EACX,MAAAC;AACJ;AAEA,SAASA,OAAK,UAAU;AACpB,SAAO;AAAA,IACH,eAAe,cAAc,QAAQ;AAAA,IACrC,eAAeC,eAAc,QAAQ;AAAA,IACrC,gBAAgBC,gBAAe,QAAQ;AAAA,IACvC,oBAAoB,kBAAkB,QAAQ;AAAA,IAC9C,kBAAkBC,aAAY,QAAQ;AAAA,IACtC,0BAA0B,mBAAmB,QAAQ;AAAA,EACzD;AACJ;AAEA,SAAS,cAAc,UAAU;AAC7B,QAAM,SAAS;AACf,QAAM,OAAO;AAEb,MAAI,SAAS,OAAO,SAAS,YAAY;AACrC,WAAO;AAAA,EACX;AAEA,QAAM,QAAQ,sBAAsB,UAAU,QAAQ,IAAI;AAC1D,SAAO;AAAA,IACH;AAAA,IACA,aAAa;AAAA,EACjB;AACJ;AAEA,SAASF,eAAc,UAAU;AAC7B,QAAM,SAAS;AACf,QAAM,OAAO;AAEb,MAAI,SAAS,OAAO,SAAS,YAAY;AACrC,WAAO;AAAA,EACX;AAEA,QAAM,QAAQ,SAAS,UAAU,QAAQ,IAAI;AAC7C,SAAO;AAAA,IACH;AAAA,IACA,aAAa,GAAG;AAAA,EACpB;AACJ;AAEA,SAASC,gBAAe,UAAU;AAC9B,QAAM,SAAS;AACf,QAAM,OAAO;AAEb,MAAI,SAAS,OAAO,SAAS,YAAY;AACrC,WAAO;AAAA,EACX;AAEA,QAAM,QAAQ,SAAS,UAAU,QAAQ,IAAI;AAC7C,SAAO;AAAA,IACH;AAAA,IACA,aAAa,GAAG;AAAA,EACpB;AACJ;AAEA,SAAS,kBAAkB,UAAU;AACjC,QAAM,SAAS;AACf,QAAM,OAAO;AAEb,MAAI,SAAS,OAAO,SAAS,YAAY;AACrC,WAAO;AAAA,EACX;AAEA,QAAM,YAAY,SAAS,SAAS,MAAM;AAC1C,QAAM,SAAS,YAAY,SAAgB;AAC3C,SAAO;AAAA,IACH;AAAA,IACA,aAAa,UAAU,IAAI,QAAQ;AAAA,EACvC;AACJ;AAEA,SAAS,mBAAmB,UAAU;AAClC,QAAM,SAAS;AACf,QAAM,OAAO;AAEb,MAAI,SAAS,OAAO,SAAS,YAAY;AACrC,WAAO;AAAA,EACX;AAEA,QAAM,YAAY,SAAS,SAAS,MAAM;AAC1C,QAAM,UAAU,YAAY,SAAgB,KAAK;AACjD,SAAO;AAAA,IACH;AAAA,IACA,aAAa,GAAG,SAAS,UAAU,IAAI,QAAQ;AAAA,EACnD;AACJ;AAEA,SAASC,aAAY,UAAU;AAC3B,QAAM,SAAS;AACf,QAAM,OAAO;AAEb,MAAI,SAAS,OAAO,SAAS,YAAY;AACrC,WAAO;AAAA,EACX;AAEA,QAAM,YAAY,SAAS,SAAS,MAAM;AAC1C,QAAM,SAAS,YAAY,KAAc;AACzC,SAAO;AAAA,IACH;AAAA,IACA,aAAa,GAAG,SAAS,UAAU,IAAI,QAAQ;AAAA,EACnD;AACJ;;;AC5GA,IAAM,mBAAmB,CAAC,GAAG,GAAG,EAAE;AAElC,IAAO,oBAAQ;AAAA,EACX,KAAAC;AACJ;AAEA,SAASA,KAAI,UAAU,eAAe,kBAAkB;AACpD,MAAI,iBAAiB,aAAa,GAAG;AACjC,kBAAc,OAAO;AACrB,UAAM,SAAS,mBAAmB,cAAc,sBAAsB;AACtE,kBAAc,QAAQ,SAAS,OAAO,MAAM,QAAQ,SAAS,cAAc,4BAA4B,KAAK;AAC5G,cAAU,eAAe,UAAU,WAAY;AAC3C,aAAO,eAAe,KAAK,KAAK;AAAA,IACpC,CAAC;AAAA,EACL;AAOA,SAAO;AACX;AAEA,SAAS,iBAAiB,MAAM;AAC5B,SAAO,SAAU,KAAK,gBAAgB,UAAe,iBAAiB,SAAS,KAAK,YAAY,KAAK,MAC9F,KAAK,yBAAyB,KAAK,sBAAsB,SACzD,KAAK,+BAA+B,KAAK,4BAA4B;AAChF;;;AC1BA,SAAS,qBAAqB,SAAS;AACnC,OAAK,OAAO;AACZ,OAAK,UAAU,WAAW;AAC1B,OAAK,QAAS,IAAI,MAAM,EAAG;AAC/B;AAEA,qBAAqB,YAAY,IAAI;AAErC,IAAO,iBAAQ;AAAA,EACX;AACJ;;;ACcA,IAAO,sBAAQ;AAAA,EACX;AAAA,EACA;AAAA,EACA,QAAQ;AACZ;AAEO,IAAM,SAAS;AAEf,SAAS,KAAK,MAAM,UAAU,CAAC,GAAG;AACrC,MAAI,gBAAgB,IAAI,GAAG;AACvB,YAAQ,QAAQ;AAChB,WAAO,SAAS,MAAM,OAAO,EAAE,KAAK,CAAC,iBAAiB,aAAa,cAAc,OAAO,CAAC;AAAA,EAC7F;AACA,MAAI,oBAAoB,IAAI,GAAG;AAC3B,YAAQ,QAAQ;AAChB,WAAO,eAAe,IAAI,EAAE,KAAK,CAAC,iBAAiB,aAAa,cAAc,OAAO,CAAC;AAAA,EAC1F;AACA,SAAO,aAAa,MAAM,OAAO;AACrC;AAEA,SAAS,gBAAgB,MAAM;AAC3B,SAAO,OAAO,SAAS;AAC3B;AAEA,SAAS,SAAS,UAAU,SAAS;AACjC,MAAI,YAAY,KAAK,QAAQ,GAAG;AAC5B,QAAI,OAAO,UAAU,aAAa;AAC9B,aAAO,gBAAgB,UAAU,OAAO;AAAA,IAC5C;AAEA,WAAO,kBAAkB,UAAU,OAAO;AAAA,EAC9C;AAEA,MAAI,UAAU,QAAQ,GAAG;AACrB,WAAO,QAAQ,QAAQ,gBAAgB,QAAQ,CAAC;AAAA,EACpD;AAEA,SAAO,cAAc,UAAU,OAAO;AAC1C;AAEA,SAAS,gBAAgB,KAAK,EAAC,OAAM,IAAI,CAAC,GAAG;AACzC,QAAM,UAAU,EAAC,QAAQ,MAAK;AAC9B,MAAI,OAAO,UAAU,MAAM,KAAK,UAAU,GAAG;AACzC,YAAQ,UAAU;AAAA,MACd,OAAO,WAAW,SAAS;AAAA,IAC/B;AAAA,EACJ;AACA,SAAO,MAAM,KAAK,OAAO,EAAE,KAAK,CAAC,aAAa,SAAS,YAAY,CAAC;AACxE;AAEA,SAAS,kBAAkB,KAAK,EAAC,OAAM,IAAI,CAAC,GAAG;AAC3C,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,UAAM,UAAU,CAAC;AACjB,QAAI,OAAO,UAAU,MAAM,KAAK,UAAU,GAAG;AACzC,cAAQ,UAAU;AAAA,QACd,OAAO,WAAW,SAAS;AAAA,MAC/B;AAAA,IACJ;AAEA,UAAMC,OAAM,eAAe,GAAG;AAC9B,IAAAA,KAAI,KAAK,SAAS,CAAC,aAAa;AAC5B,UAAK,SAAS,cAAc,OAAS,SAAS,cAAc,KAAM;AAC9D,cAAM,OAAO,CAAC;AACd,iBAAS,GAAG,QAAQ,CAAC,UAAU,KAAK,KAAK,OAAO,KAAK,KAAK,CAAC,CAAC;AAC5D,iBAAS,GAAG,SAAS,CAAC,UAAU,OAAO,KAAK,CAAC;AAC7C,iBAAS,GAAG,OAAO,MAAM,QAAQ,OAAO,OAAO,IAAI,CAAC,CAAC;AAAA,MACzD,OAAO;AACH,eAAO,yBAAyB,SAAS,cAAc,SAAS,eAAe;AAC/E,iBAAS,OAAO;AAAA,MACpB;AAAA,IACJ,CAAC,EAAE,GAAG,SAAS,CAAC,UAAU,OAAO,KAAK,CAAC;AAAA,EAC3C,CAAC;AACL;AAEA,SAAS,eAAe,KAAK;AACzB,MAAI,cAAc,KAAK,GAAG,GAAG;AACzB,WAAO,wBAAwB,OAAO,EAAE;AAAA,EAC5C;AACA,SAAO,wBAAwB,MAAM,EAAE;AAC3C;AAEA,SAAS,UAAU,UAAU;AACzB,SAAO,0BAA0B,KAAK,QAAQ;AAClD;AAEA,SAAS,cAAc,UAAU,EAAC,OAAM,IAAI,CAAC,GAAG;AAC5C,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,UAAM,KAAK,cAAc;AACzB,OAAG,KAAK,UAAU,CAAC,OAAO,OAAO;AAC7B,UAAI,OAAO;AACP,eAAO,KAAK;AAAA,MAChB,OAAO;AACH,WAAG,KAAK,UAAU,CAACC,QAAO,SAAS;AAC/B,cAAIA,QAAO;AACP,mBAAOA,MAAK;AAAA,UAChB,OAAO;AACH,kBAAM,OAAO,KAAK,IAAI,KAAK,MAAM,WAAW,SAAY,SAAS,KAAK,IAAI;AAC1E,kBAAM,SAAS,OAAO,MAAM,IAAI;AAChC,kBAAM,UAAU;AAAA,cACZ;AAAA,cACA,QAAQ;AAAA,YACZ;AACA,eAAG,KAAK,IAAI,SAAS,CAACA,WAAU;AAC5B,kBAAIA,QAAO;AACP,uBAAOA,MAAK;AAAA,cAChB,OAAO;AACH,mBAAG,MAAM,IAAI,CAACA,WAAU;AACpB,sBAAIA,QAAO;AACP,4BAAQ,KAAK,wBAAwB,aAAaA,MAAK;AAAA,kBAC3D;AACA,0BAAQ,MAAM;AAAA,gBAClB,CAAC;AAAA,cACL;AAAA,YACJ,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACL;AAEA,SAAS,gBAAgB;AACrB,MAAI;AACA,WAAO,wBAAwB,IAAI;AAAA,EACvC,SAAS,OAAP;AACE,WAAO;AAAA,EACX;AACJ;AAEA,SAAS,oBAAoB,MAAM;AAC/B,SAAQ,OAAO,WAAW,eAAiB,OAAO,SAAS,eAAiB,gBAAgB;AAChG;AAEA,SAAS,eAAe,MAAM;AAC1B,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,UAAM,SAAS,IAAI,WAAW;AAC9B,WAAO,SAAS,CAAC,gBAAgB,QAAQ,YAAY,OAAO,MAAM;AAClE,WAAO,UAAU,MAAM,OAAO,OAAO,KAAK;AAC1C,WAAO,kBAAkB,IAAI;AAAA,EACjC,CAAC;AACL;AAEA,SAAS,aAAa,MAAM,SAAS;AACjC,MAAI,aAAa,IAAI,GAAG;AAGpB,WAAQ,IAAI,WAAW,IAAI,EAAG;AAAA,EAClC;AACA,SAAO,SAASC,aAAY,IAAI,GAAG,OAAO;AAC9C;AAEA,SAAS,aAAa,MAAM;AACxB,MAAI;AACA,WAAO,OAAO,SAAS,IAAI;AAAA,EAC/B,SAAS,OAAP;AACE,WAAO;AAAA,EACX;AACJ;AAEA,SAASA,aAAY,MAAM;AACvB,MAAI;AACA,WAAO,IAAI,SAAS,IAAI;AAAA,EAC5B,SAAS,OAAP;AACE,WAAO,IAAIC,UAAgB,IAAI;AAAA,EACnC;AACJ;AAEO,SAAS,SACZ,UACA,EAAC,WAAW,OAAO,QAAQ,OAAO,iBAAiB,MAAK,IAAI,EAAC,UAAU,OAAO,OAAO,OAAO,gBAAgB,MAAK,GACnH;AACE,MAAI,gBAAgB;AACpB,MAAI,OAAO,CAAC;AACZ,QAAM,eAAe,CAAC;AAEtB,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,IAAI,qBAAY,gBAAgB,UAAU,KAAK;AAE/C,MAAI,kBAAU,YAAY,kBAAU,YAAY,YAAY,cAAc,GAAG;AACzE,oBAAgB;AAChB,UAAMC,YAAW,kBAAS,KAAK,UAAU,cAAc;AACvD,QAAI,UAAU;AACV,WAAK,OAAOA;AAAA,IAChB,OAAO;AACH,aAAO,aAAa,CAAC,GAAG,MAAMA,SAAQ;AAAA,IAC1C;AAAA,EACJ;AAEA,MAAI,kBAAU,YAAY,kBAAU,YAAY,YAAY,cAAc,GAAG;AACzE,oBAAgB;AAChB,UAAMA,YAAW,kBAAS,KAAK,UAAU,cAAc;AACvD,QAAI,UAAU;AACV,WAAK,OAAOA;AAAA,IAChB,OAAO;AACH,aAAO,aAAa,CAAC,GAAG,MAAMA,SAAQ;AAAA,IAC1C;AAAA,EACJ;AAEA,MAAI,kBAAU,YAAY,YAAY,gBAAgB,GAAG;AACrD,oBAAgB;AAChB,UAAM,EAAC,MAAMA,WAAU,UAAS,IAAI,aAAK,KAAK,UAAU,kBAAkB,cAAc;AACxF,QAAIA,UAAS,WAAW;AACpB,WAAK,YAAYA,UAAS;AAC1B,aAAOA,UAAS;AAAA,IACpB;AAEA,QAAI,UAAU;AACV,WAAK,OAAOA;AACZ,kBAAY,IAAI;AAAA,IACpB,OAAO;AACH,aAAO,aAAa,CAAC,GAAG,MAAMA,SAAQ;AAAA,IAC1C;AAEA,QAAI,kBAAU,YAAY,kBAAU,YAAYA,UAAS,eAAe,CAAC,YAAY,cAAc,GAAG;AAClG,YAAM,eAAe,kBAAS,KAAKA,UAAS,YAAY,OAAO,GAAG,cAAc;AAChF,UAAI,UAAU;AACV,aAAK,OAAO;AAAA,MAChB,OAAO;AACH,eAAO,aAAa,CAAC,GAAG,MAAM,YAAY;AAAA,MAC9C;AAAA,IACJ;AAEA,QAAI,kBAAU,YAAY,kBAAU,WAAWA,UAAS,uBAAuB,CAAC,WAAW,SAAS,GAAG;AACnG,YAAM,cAAc,iBAAQ,KAAK,wBAAwBA,UAAS,oBAAoB,KAAK,CAAC;AAC5F,UAAI,UAAU;AACV,aAAK,MAAM;AAAA,MACf,OAAO;AACH,eAAO,YAAY;AACnB,eAAO,aAAa,CAAC,GAAG,MAAM,WAAW;AAAA,MAC7C;AAAA,IACJ;AAEA,QAAI,kBAAU,iBAAiBA,UAAS,oBAAoB;AACxD,YAAM,oBAAoB,uBAAc,KAAKA,UAAS,qBAAqB,OAAO,cAAc;AAChG,UAAI,UAAU;AACV,aAAK,YAAY;AAAA,MACrB,OAAO;AACH,eAAO,aAAa,CAAC,GAAG,MAAM,iBAAiB;AAAA,MACnD;AAAA,IACJ;AAEA,QAAI,kBAAU,YAAY,kBAAU,WAAWA,UAAS,kBAAkB,CAAC,WAAW,SAAS,GAAG;AAC9F,YAAM,cAAc,iBAAQ;AAAA,QACxBA,UAAS,eAAe;AAAA,QACxB,CAAC;AAAA,UACG,QAAQ;AAAA,UACR,QAAQA,UAAS,eAAe,MAAM;AAAA,UACtC,aAAa;AAAA,UACb,aAAa;AAAA,QACjB,CAAC;AAAA,MACL;AACA,UAAI,UAAU;AACV,aAAK,MAAM;AAAA,MACf,OAAO;AACH,eAAO,aAAa,CAAC,GAAG,MAAM,WAAW;AAAA,MAC7C;AAAA,IACJ;AAEA,QAAI,kBAAU,iBAAiB;AAC3B,UAAI,aAAaA,SAAQ,GAAG;AACxB,cAAM,gBAAgB,mBAAU,KAAK,UAAU,kBAAkBA,UAAS,aAAa,UAAU,WAAW,cAAc;AAC1H,YAAI,UAAU;AACV,eAAK,aAAa;AAAA,QACtB,OAAO;AACH,iBAAO,aAAa,CAAC,GAAG,MAAM,aAAa;AAAA,QAC/C;AAAA,MACJ;AAAA,IACJ;AAEA,QAAIA,UAAS,cAAc;AACvB,aAAOA,UAAS,aAAa;AAAA,IACjC;AAAA,EACJ;AAEA,MAAI,kBAAU,YAAY,kBAAU,YAAY,YAAY,cAAc,GAAG;AACzE,oBAAgB;AAChB,UAAMA,YAAW,kBAAS,KAAK,UAAU,gBAAgB,cAAc;AACvE,QAAI,UAAU;AACV,WAAK,OAAOA;AAAA,IAChB,OAAO;AACH,aAAO,aAAa,CAAC,GAAG,MAAMA,SAAQ;AAAA,IAC1C;AAAA,EACJ;AAEA,MAAI,kBAAU,WAAW,WAAW,SAAS,GAAG;AAC5C,oBAAgB;AAChB,UAAMA,YAAW,iBAAQ,KAAK,UAAU,SAAS;AACjD,QAAI,UAAU;AACV,WAAK,MAAMA;AAAA,IACf,OAAO;AACH,aAAOA,UAAS;AAChB,aAAO,aAAa,CAAC,GAAG,MAAMA,SAAQ;AAAA,IAC1C;AAAA,EACJ;AAEA,OAAK,kBAAU,YAAY,kBAAU,aAAa,kBAAU,WAAW,WAAW,SAAS,GAAG;AAC1F,oBAAgB;AAChB,UAAMA,YAAW,iBAAQ,KAAK,UAAU,WAAW,KAAK;AACxD,QAAIA,qBAAoB,SAAS;AAC7B,mBAAa,KAAKA,UAAS,KAAK,UAAU,CAAC;AAAA,IAC/C,OAAO;AACH,iBAAWA,SAAQ;AAAA,IACvB;AAAA,EACJ;AAEA,MAAI,kBAAU,WAAW,WAAW,aAAa,GAAG;AAChD,oBAAgB;AAChB,UAAM,cAAc,iBAAQ,KAAK,UAAU,eAAe,cAAc;AACxE,QAAI,UAAU;AACV,WAAK,MAAM;AAAA,IACf,OAAO;AACH,aAAO,aAAa,CAAC,GAAG,MAAM,WAAW;AAAA,IAC7C;AAAA,EACJ;AAEA,MAAI,kBAAU,WAAW,kBAAU,gBAAgB,eAAe,eAAe,GAAG;AAChF,oBAAgB;AAChB,UAAMA,YAAW,sBAAY,KAAK,UAAU,eAAe;AAC3D,QAAI,UAAU;AACV,WAAK,MAAM,CAAC,KAAK,MAAMA,YAAW,aAAa,CAAC,GAAG,KAAK,KAAKA,SAAQ;AACrE,WAAK,UAAUA;AAAA,IACnB,OAAO;AACH,aAAO,aAAa,CAAC,GAAG,MAAMA,SAAQ;AAAA,IAC1C;AAAA,EACJ;AAEA,MAAI,kBAAU,WAAW,eAAe,aAAa,GAAG;AACpD,oBAAgB;AAChB,UAAM,EAAC,UAAAA,WAAU,gBAAe,IAAI,sBAAY,KAAK,UAAU,eAAe,OAAO,cAAc;AACnG,mBAAeA,SAAQ;AACvB,QAAI,iBAAiB;AACjB,mBAAa,KAAK,gBAAgB,KAAK,CAAC,YAAY,QAAQ,QAAQ,cAAc,CAAC,CAAC;AAAA,IACxF;AAAA,EACJ;AAEA,MAAI,kBAAU,WAAW,WAAW,eAAe,GAAG;AAClD,oBAAgB;AAChB,UAAMA,YAAW,iBAAQ,KAAK,UAAU,eAAe;AACvD,QAAI,UAAU;AACV,WAAK,MAAM,CAAC,KAAK,MAAMA,YAAW,aAAa,CAAC,GAAG,KAAK,KAAKA,SAAQ;AAAA,IACzE,OAAO;AACH,aAAO,aAAa,CAAC,GAAG,MAAMA,SAAQ;AAAA,IAC1C;AAAA,EACJ;AAEA,MAAI,kBAAU,YAAY,YAAY,eAAe,GAAG;AACpD,oBAAgB;AAChB,UAAMA,YAAW,kBAAS,KAAK,UAAU,eAAe;AACxD,QAAI,UAAU;AACV,WAAK,OAAO,CAAC,KAAK,OAAOA,YAAW,aAAa,CAAC,GAAG,KAAK,MAAMA,SAAQ;AAAA,IAC5E,OAAO;AACH,aAAO,aAAa,CAAC,GAAG,MAAMA,SAAQ;AAAA,IAC1C;AAAA,EACJ;AAEA,MAAI,kBAAU,WAAW,eAAe,eAAe,GAAG;AACtD,oBAAgB;AAChB,UAAMA,YAAW,sBAAY,KAAK,UAAU,eAAe;AAC3D,QAAI,UAAU;AACV,WAAK,MAAM,CAAC,KAAK,MAAMA,YAAW,aAAa,CAAC,GAAG,KAAK,KAAKA,SAAQ;AAAA,IACzE,OAAO;AACH,aAAO,aAAa,CAAC,GAAG,MAAMA,SAAQ;AAAA,IAC1C;AAAA,EACJ;AAEA,QAAM,aAAa,kBAAU,YAAY,kBAAU,aAC5C,kBAAU,YACV,kBAAU,iBACV,kBAAU,IAAI,UAAU,KAAK,WAAW,gBAAgB;AAC/D,MAAI,WAAW;AACX,oBAAgB;AAChB,SAAK,YAAY;AAAA,EACrB,OAAO;AACH,WAAO,KAAK;AAAA,EAChB;AAEA,MAAI,UAAU;AACV,QAAI,UAAU;AACV,UAAI,CAAC,KAAK,MAAM;AACZ,aAAK,OAAO,CAAC;AAAA,MACjB;AACA,WAAK,KAAK,WAAW;AAAA,IACzB,OAAO;AACH,WAAK,WAAW;AAAA,IACpB;AACA,oBAAgB;AAAA,EACpB;AAEA,MAAI,CAAC,eAAe;AAChB,UAAM,IAAI,eAAW,qBAAqB;AAAA,EAC9C;AAEA,MAAI,OAAO;AACP,WAAO,QAAQ,IAAI,YAAY,EAAE,KAAK,MAAM,IAAI;AAAA,EACpD;AACA,SAAO;AAEP,WAAS,WAAWA,WAAU;AAC1B,QAAI,UAAU;AACV,WAAK,MAAMA;AAAA,IACf,OAAO;AACH,aAAO,aAAa,CAAC,GAAG,MAAMA,SAAQ;AAAA,IAC1C;AAAA,EACJ;AAEA,WAAS,eAAeA,WAAU;AAC9B,QAAI,UAAU;AACV,iBAAW,SAAS,CAAC,QAAQ,MAAM,GAAG;AAClC,cAAM,WAAW,KAAK;AACtB,YAAIA,UAAS,WAAW;AACpB,eAAK,SAAS,CAAC,KAAK,SAASA,UAAS,YAAY,aAAa,CAAC,GAAG,KAAK,MAAMA,UAAS,SAAS;AAChG,iBAAOA,UAAS;AAAA,QACpB;AAAA,MACJ;AACA,WAAK,MAAM,CAAC,KAAK,MAAMA,YAAW,aAAa,CAAC,GAAG,KAAK,KAAKA,SAAQ;AACrE,WAAK,UAAU,CAAC,KAAK,UAAUA,YAAW,aAAa,CAAC,GAAG,KAAK,KAAKA,SAAQ;AAAA,IACjF,OAAO;AACH,aAAO;AAAA,QACH,CAAC;AAAA,QACD;AAAA,QACAA,UAAS,SAASA,UAAS,SAAS,CAAC;AAAA,QACrCA,UAAS,SAASA,UAAS,SAAS,CAAC;AAAA,QACrCA;AAAA,MACJ;AACA,aAAO,KAAK;AACZ,aAAO,KAAK;AAAA,IAChB;AAAA,EACJ;AACJ;AAEA,SAAS,YAAY,gBAAgB;AACjC,SAAO,mBAAmB;AAC9B;AAEA,SAAS,YAAY,gBAAgB;AACjC,SAAO,mBAAmB;AAC9B;AAEA,SAAS,YAAY,kBAAkB;AACnC,SAAO,qBAAqB;AAChC;AAEA,SAAS,YAAY,MAAM;AACvB,MAAI,KAAK,MAAM;AACX,QAAI,KAAK,KAAK,eAAe,KAAK,KAAK,gBAAgB;AACnD,UAAI;AACA,aAAK,MAAM,KAAK,OAAO,CAAC;AACxB,aAAK,IAAI,WAAW,sBAAsB,KAAK,KAAK,YAAY,KAAK;AACrE,YAAI,KAAK,KAAK,eAAe,MAAM,KAAK,EAAE,MAAM,KAAK;AACjD,eAAK,IAAI,WAAW,CAAC,KAAK,IAAI;AAAA,QAClC;AAAA,MACJ,SAAS,OAAP;AAAA,MAEF;AAAA,IACJ;AAEA,QAAI,KAAK,KAAK,gBAAgB,KAAK,KAAK,iBAAiB;AACrD,UAAI;AACA,aAAK,MAAM,KAAK,OAAO,CAAC;AACxB,aAAK,IAAI,YAAY,sBAAsB,KAAK,KAAK,aAAa,KAAK;AACvE,YAAI,KAAK,KAAK,gBAAgB,MAAM,KAAK,EAAE,MAAM,KAAK;AAClD,eAAK,IAAI,YAAY,CAAC,KAAK,IAAI;AAAA,QACnC;AAAA,MACJ,SAAS,OAAP;AAAA,MAEF;AAAA,IACJ;AAEA,QAAI,KAAK,KAAK,eAAe,KAAK,KAAK,gBAAgB;AACnD,UAAI;AACA,aAAK,MAAM,KAAK,OAAO,CAAC;AACxB,aAAK,IAAI,WAAW,KAAK,KAAK,YAAY,MAAM,KAAK,KAAK,KAAK,YAAY,MAAM;AACjF,YAAI,KAAK,KAAK,eAAe,UAAU,GAAG;AACtC,eAAK,IAAI,WAAW,CAAC,KAAK,IAAI;AAAA,QAClC;AAAA,MACJ,SAAS,OAAP;AAAA,MAEF;AAAA,IACJ;AAAA,EACJ;AACJ;AAEA,SAAS,YAAY,gBAAgB;AACjC,SAAO,mBAAmB;AAC9B;AAEA,SAAS,WAAW,WAAW;AAC3B,SAAO,MAAM,QAAQ,SAAS,KAAK,UAAU,SAAS;AAC1D;AAEA,SAAS,WAAW,gBAAgB;AAChC,SAAO,MAAM,QAAQ,cAAc,KAAK,eAAe,SAAS;AACpE;AAEA,SAAS,aAAa,MAAM;AACxB,SAAO,KAAK,WAAW,KAAK,QAAQ,SAAS,MAAM,QAAQ,KAAK,QAAQ,KAAK,KAAK,KAAK,QAAQ,MAAM,OAAO,WACrG,KAAK,gBAAgB,KAAK,aAAa;AAClD;AAEA,SAAS,WAAW,eAAe;AAC/B,SAAO,kBAAkB;AAC7B;AAEA,SAAS,eAAe,mBAAmB;AACvC,SAAO,sBAAsB;AACjC;AAEA,SAAS,eAAe,eAAe;AACnC,SAAO,kBAAkB;AAC7B;AAEA,SAAS,WAAW,iBAAiB;AACjC,SAAO,oBAAoB;AAC/B;AAEA,SAAS,YAAY,iBAAiB;AAClC,SAAO,oBAAoB;AAC/B;AAEA,SAAS,eAAe,iBAAiB;AACrC,SAAO,oBAAoB;AAC/B;", "names": ["DataView", "DataView", "findOffsets", "findOffsets", "findOffsets", "read", "value", "read", "read", "<PERSON><PERSON><PERSON><PERSON>", "decodeAsciiValue", "TAG_HEADER_SIZE", "read", "readTag", "getTagValue", "get", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "read", "fraction", "read", "getTagName", "read", "parseTags", "sliceToString", "read", "read", "getImageWidth", "getImageHeight", "read", "getDescription", "read", "read", "read", "getImageWidth", "getImageHeight", "getBitDepth", "get", "get", "error", "getDataView", "DataView", "readTags"]}