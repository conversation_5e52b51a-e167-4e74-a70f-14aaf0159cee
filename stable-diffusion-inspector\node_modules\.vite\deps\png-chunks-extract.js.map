{"version": 3, "sources": ["../../crc-32/crc32.js", "../../png-chunks-extract/index.js"], "sourcesContent": ["/* crc32.js (C) 2014-2015 SheetJS -- http://sheetjs.com */\n/* vim: set ts=2: */\nvar CRC32;\n(function (factory) {\n\tif(typeof DO_NOT_EXPORT_CRC === 'undefined') {\n\t\tif('object' === typeof exports) {\n\t\t\tfactory(exports);\n\t\t} else if ('function' === typeof define && define.amd) {\n\t\t\tdefine(function () {\n\t\t\t\tvar module = {};\n\t\t\t\tfactory(module);\n\t\t\t\treturn module;\n\t\t\t});\n\t\t} else {\n\t\t  factory(CRC32 = {});\n\t\t}\n\t} else {\n\t\tfactory(CRC32 = {});\n\t}\n}(function(CRC32) {\nCRC32.version = '0.3.0';\n/* see perf/crc32table.js */\nfunction signed_crc_table() {\n\tvar c = 0, table = new Array(256);\n\n\tfor(var n =0; n != 256; ++n){\n\t\tc = n;\n\t\tc = ((c&1) ? (-306674912 ^ (c >>> 1)) : (c >>> 1));\n\t\tc = ((c&1) ? (-306674912 ^ (c >>> 1)) : (c >>> 1));\n\t\tc = ((c&1) ? (-306674912 ^ (c >>> 1)) : (c >>> 1));\n\t\tc = ((c&1) ? (-306674912 ^ (c >>> 1)) : (c >>> 1));\n\t\tc = ((c&1) ? (-306674912 ^ (c >>> 1)) : (c >>> 1));\n\t\tc = ((c&1) ? (-306674912 ^ (c >>> 1)) : (c >>> 1));\n\t\tc = ((c&1) ? (-306674912 ^ (c >>> 1)) : (c >>> 1));\n\t\tc = ((c&1) ? (-306674912 ^ (c >>> 1)) : (c >>> 1));\n\t\ttable[n] = c;\n\t}\n\n\treturn typeof Int32Array !== 'undefined' ? new Int32Array(table) : table;\n}\n\nvar table = signed_crc_table();\n/* charCodeAt is the best approach for binary strings */\nvar use_buffer = typeof Buffer !== 'undefined';\nfunction crc32_bstr(bstr) {\n\tif(bstr.length > 32768) if(use_buffer) return crc32_buf_8(new Buffer(bstr));\n\tvar crc = -1, L = bstr.length - 1;\n\tfor(var i = 0; i < L;) {\n\t\tcrc =  table[(crc ^ bstr.charCodeAt(i++)) & 0xFF] ^ (crc >>> 8);\n\t\tcrc =  table[(crc ^ bstr.charCodeAt(i++)) & 0xFF] ^ (crc >>> 8);\n\t}\n\tif(i === L) crc = (crc >>> 8) ^ table[(crc ^ bstr.charCodeAt(i)) & 0xFF];\n\treturn crc ^ -1;\n}\n\nfunction crc32_buf(buf) {\n\tif(buf.length > 10000) return crc32_buf_8(buf);\n\tfor(var crc = -1, i = 0, L=buf.length-3; i < L;) {\n\t\tcrc = (crc >>> 8) ^ table[(crc^buf[i++])&0xFF];\n\t\tcrc = (crc >>> 8) ^ table[(crc^buf[i++])&0xFF];\n\t\tcrc = (crc >>> 8) ^ table[(crc^buf[i++])&0xFF];\n\t\tcrc = (crc >>> 8) ^ table[(crc^buf[i++])&0xFF];\n\t}\n\twhile(i < L+3) crc = (crc >>> 8) ^ table[(crc^buf[i++])&0xFF];\n\treturn crc ^ -1;\n}\n\nfunction crc32_buf_8(buf) {\n\tfor(var crc = -1, i = 0, L=buf.length-7; i < L;) {\n\t\tcrc = (crc >>> 8) ^ table[(crc^buf[i++])&0xFF];\n\t\tcrc = (crc >>> 8) ^ table[(crc^buf[i++])&0xFF];\n\t\tcrc = (crc >>> 8) ^ table[(crc^buf[i++])&0xFF];\n\t\tcrc = (crc >>> 8) ^ table[(crc^buf[i++])&0xFF];\n\t\tcrc = (crc >>> 8) ^ table[(crc^buf[i++])&0xFF];\n\t\tcrc = (crc >>> 8) ^ table[(crc^buf[i++])&0xFF];\n\t\tcrc = (crc >>> 8) ^ table[(crc^buf[i++])&0xFF];\n\t\tcrc = (crc >>> 8) ^ table[(crc^buf[i++])&0xFF];\n\t}\n\twhile(i < L+7) crc = (crc >>> 8) ^ table[(crc^buf[i++])&0xFF];\n\treturn crc ^ -1;\n}\n\n/* much much faster to intertwine utf8 and crc */\nfunction crc32_str(str) {\n\tfor(var crc = -1, i = 0, L=str.length, c, d; i < L;) {\n\t\tc = str.charCodeAt(i++);\n\t\tif(c < 0x80) {\n\t\t\tcrc = (crc >>> 8) ^ table[(crc ^ c) & 0xFF];\n\t\t} else if(c < 0x800) {\n\t\t\tcrc = (crc >>> 8) ^ table[(crc ^ (192|((c>>6)&31))) & 0xFF];\n\t\t\tcrc = (crc >>> 8) ^ table[(crc ^ (128|(c&63))) & 0xFF];\n\t\t} else if(c >= 0xD800 && c < 0xE000) {\n\t\t\tc = (c&1023)+64; d = str.charCodeAt(i++) & 1023;\n\t\t\tcrc = (crc >>> 8) ^ table[(crc ^ (240|((c>>8)&7))) & 0xFF];\n\t\t\tcrc = (crc >>> 8) ^ table[(crc ^ (128|((c>>2)&63))) & 0xFF];\n\t\t\tcrc = (crc >>> 8) ^ table[(crc ^ (128|((d>>6)&15)|(c&3))) & 0xFF];\n\t\t\tcrc = (crc >>> 8) ^ table[(crc ^ (128|(d&63))) & 0xFF];\n\t\t} else {\n\t\t\tcrc = (crc >>> 8) ^ table[(crc ^ (224|((c>>12)&15))) & 0xFF];\n\t\t\tcrc = (crc >>> 8) ^ table[(crc ^ (128|((c>>6)&63))) & 0xFF];\n\t\t\tcrc = (crc >>> 8) ^ table[(crc ^ (128|(c&63))) & 0xFF];\n\t\t}\n\t}\n\treturn crc ^ -1;\n}\nCRC32.table = table;\nCRC32.bstr = crc32_bstr;\nCRC32.buf = crc32_buf;\nCRC32.str = crc32_str;\n}));\n", "var crc32 = require('crc-32')\n\nmodule.exports = extractChunks\n\n// Used for fast-ish conversion between uint8s and uint32s/int32s.\n// Also required in order to remain agnostic for both Node Buffers and\n// Uint8Arrays.\nvar uint8 = new Uint8Array(4)\nvar int32 = new Int32Array(uint8.buffer)\nvar uint32 = new Uint32Array(uint8.buffer)\n\nfunction extractChunks (data) {\n  if (data[0] !== 0x89) throw new Error('Invalid .png file header')\n  if (data[1] !== 0x50) throw new Error('Invalid .png file header')\n  if (data[2] !== 0x4E) throw new Error('Invalid .png file header')\n  if (data[3] !== 0x47) throw new Error('Invalid .png file header')\n  if (data[4] !== 0x0D) throw new Error('Invalid .png file header: possibly caused by DOS-Unix line ending conversion?')\n  if (data[5] !== 0x0A) throw new Error('Invalid .png file header: possibly caused by DOS-Unix line ending conversion?')\n  if (data[6] !== 0x1A) throw new Error('Invalid .png file header')\n  if (data[7] !== 0x0A) throw new Error('Invalid .png file header: possibly caused by DOS-Unix line ending conversion?')\n\n  var ended = false\n  var chunks = []\n  var idx = 8\n\n  while (idx < data.length) {\n    // Read the length of the current chunk,\n    // which is stored as a Uint32.\n    uint8[3] = data[idx++]\n    uint8[2] = data[idx++]\n    uint8[1] = data[idx++]\n    uint8[0] = data[idx++]\n\n    // Chunk includes name/type for CRC check (see below).\n    var length = uint32[0] + 4\n    var chunk = new Uint8Array(length)\n    chunk[0] = data[idx++]\n    chunk[1] = data[idx++]\n    chunk[2] = data[idx++]\n    chunk[3] = data[idx++]\n\n    // Get the name in ASCII for identification.\n    var name = (\n      String.fromCharCode(chunk[0]) +\n      String.fromCharCode(chunk[1]) +\n      String.fromCharCode(chunk[2]) +\n      String.fromCharCode(chunk[3])\n    )\n\n    // The IHDR header MUST come first.\n    if (!chunks.length && name !== 'IHDR') {\n      throw new Error('IHDR header missing')\n    }\n\n    // The IEND header marks the end of the file,\n    // so on discovering it break out of the loop.\n    if (name === 'IEND') {\n      ended = true\n      chunks.push({\n        name: name,\n        data: new Uint8Array(0)\n      })\n\n      break\n    }\n\n    // Read the contents of the chunk out of the main buffer.\n    for (var i = 4; i < length; i++) {\n      chunk[i] = data[idx++]\n    }\n\n    // Read out the CRC value for comparison.\n    // It's stored as an Int32.\n    uint8[3] = data[idx++]\n    uint8[2] = data[idx++]\n    uint8[1] = data[idx++]\n    uint8[0] = data[idx++]\n\n    var crcActual = int32[0]\n    var crcExpect = crc32.buf(chunk)\n    if (crcExpect !== crcActual) {\n      throw new Error(\n        'CRC values for ' + name + ' header do not match, PNG file is likely corrupted'\n      )\n    }\n\n    // The chunk data is now copied to remove the 4 preceding\n    // bytes used for the chunk name/type.\n    var chunkData = new Uint8Array(chunk.buffer.slice(4))\n\n    chunks.push({\n      name: name,\n      data: chunkData\n    })\n  }\n\n  if (!ended) {\n    throw new Error('.png file ended prematurely: no IEND header was found')\n  }\n\n  return chunks\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,QAAI;AACJ,KAAC,SAAU,SAAS;AACnB,UAAG,OAAO,sBAAsB,aAAa;AAC5C,YAAG,aAAa,OAAO,SAAS;AAC/B,kBAAQ,OAAO;AAAA,QAChB,WAAW,eAAe,OAAO,UAAU,OAAO,KAAK;AACtD,iBAAO,WAAY;AAClB,gBAAIA,UAAS,CAAC;AACd,oBAAQA,OAAM;AACd,mBAAOA;AAAA,UACR,CAAC;AAAA,QACF,OAAO;AACL,kBAAQ,QAAQ,CAAC,CAAC;AAAA,QACpB;AAAA,MACD,OAAO;AACN,gBAAQ,QAAQ,CAAC,CAAC;AAAA,MACnB;AAAA,IACD,GAAE,SAASC,QAAO;AAClB,MAAAA,OAAM,UAAU;AAEhB,eAAS,mBAAmB;AAC3B,YAAI,IAAI,GAAGC,SAAQ,IAAI,MAAM,GAAG;AAEhC,iBAAQ,IAAG,GAAG,KAAK,KAAK,EAAE,GAAE;AAC3B,cAAI;AACJ,cAAM,IAAE,IAAM,aAAc,MAAM,IAAO,MAAM;AAC/C,cAAM,IAAE,IAAM,aAAc,MAAM,IAAO,MAAM;AAC/C,cAAM,IAAE,IAAM,aAAc,MAAM,IAAO,MAAM;AAC/C,cAAM,IAAE,IAAM,aAAc,MAAM,IAAO,MAAM;AAC/C,cAAM,IAAE,IAAM,aAAc,MAAM,IAAO,MAAM;AAC/C,cAAM,IAAE,IAAM,aAAc,MAAM,IAAO,MAAM;AAC/C,cAAM,IAAE,IAAM,aAAc,MAAM,IAAO,MAAM;AAC/C,cAAM,IAAE,IAAM,aAAc,MAAM,IAAO,MAAM;AAC/C,UAAAA,OAAM,KAAK;AAAA,QACZ;AAEA,eAAO,OAAO,eAAe,cAAc,IAAI,WAAWA,MAAK,IAAIA;AAAA,MACpE;AAEA,UAAI,QAAQ,iBAAiB;AAE7B,UAAI,aAAa,OAAO,WAAW;AACnC,eAAS,WAAW,MAAM;AACzB,YAAG,KAAK,SAAS;AAAO,cAAG;AAAY,mBAAO,YAAY,IAAI,OAAO,IAAI,CAAC;AAAA;AAC1E,YAAI,MAAM,IAAI,IAAI,KAAK,SAAS;AAChC,iBAAQ,IAAI,GAAG,IAAI,KAAI;AACtB,gBAAO,OAAO,MAAM,KAAK,WAAW,GAAG,KAAK,OAAS,QAAQ;AAC7D,gBAAO,OAAO,MAAM,KAAK,WAAW,GAAG,KAAK,OAAS,QAAQ;AAAA,QAC9D;AACA,YAAG,MAAM;AAAG,gBAAO,QAAQ,IAAK,OAAO,MAAM,KAAK,WAAW,CAAC,KAAK;AACnE,eAAO,MAAM;AAAA,MACd;AAEA,eAAS,UAAU,KAAK;AACvB,YAAG,IAAI,SAAS;AAAO,iBAAO,YAAY,GAAG;AAC7C,iBAAQ,MAAM,IAAI,IAAI,GAAG,IAAE,IAAI,SAAO,GAAG,IAAI,KAAI;AAChD,gBAAO,QAAQ,IAAK,OAAO,MAAI,IAAI,QAAM;AACzC,gBAAO,QAAQ,IAAK,OAAO,MAAI,IAAI,QAAM;AACzC,gBAAO,QAAQ,IAAK,OAAO,MAAI,IAAI,QAAM;AACzC,gBAAO,QAAQ,IAAK,OAAO,MAAI,IAAI,QAAM;AAAA,QAC1C;AACA,eAAM,IAAI,IAAE;AAAG,gBAAO,QAAQ,IAAK,OAAO,MAAI,IAAI,QAAM;AACxD,eAAO,MAAM;AAAA,MACd;AAEA,eAAS,YAAY,KAAK;AACzB,iBAAQ,MAAM,IAAI,IAAI,GAAG,IAAE,IAAI,SAAO,GAAG,IAAI,KAAI;AAChD,gBAAO,QAAQ,IAAK,OAAO,MAAI,IAAI,QAAM;AACzC,gBAAO,QAAQ,IAAK,OAAO,MAAI,IAAI,QAAM;AACzC,gBAAO,QAAQ,IAAK,OAAO,MAAI,IAAI,QAAM;AACzC,gBAAO,QAAQ,IAAK,OAAO,MAAI,IAAI,QAAM;AACzC,gBAAO,QAAQ,IAAK,OAAO,MAAI,IAAI,QAAM;AACzC,gBAAO,QAAQ,IAAK,OAAO,MAAI,IAAI,QAAM;AACzC,gBAAO,QAAQ,IAAK,OAAO,MAAI,IAAI,QAAM;AACzC,gBAAO,QAAQ,IAAK,OAAO,MAAI,IAAI,QAAM;AAAA,QAC1C;AACA,eAAM,IAAI,IAAE;AAAG,gBAAO,QAAQ,IAAK,OAAO,MAAI,IAAI,QAAM;AACxD,eAAO,MAAM;AAAA,MACd;AAGA,eAAS,UAAU,KAAK;AACvB,iBAAQ,MAAM,IAAI,IAAI,GAAG,IAAE,IAAI,QAAQ,GAAG,GAAG,IAAI,KAAI;AACpD,cAAI,IAAI,WAAW,GAAG;AACtB,cAAG,IAAI,KAAM;AACZ,kBAAO,QAAQ,IAAK,OAAO,MAAM,KAAK;AAAA,UACvC,WAAU,IAAI,MAAO;AACpB,kBAAO,QAAQ,IAAK,OAAO,OAAO,MAAM,KAAG,IAAG,OAAQ;AACtD,kBAAO,QAAQ,IAAK,OAAO,OAAO,MAAK,IAAE,OAAQ;AAAA,UAClD,WAAU,KAAK,SAAU,IAAI,OAAQ;AACpC,iBAAK,IAAE,QAAM;AAAI,gBAAI,IAAI,WAAW,GAAG,IAAI;AAC3C,kBAAO,QAAQ,IAAK,OAAO,OAAO,MAAM,KAAG,IAAG,MAAO;AACrD,kBAAO,QAAQ,IAAK,OAAO,OAAO,MAAM,KAAG,IAAG,OAAQ;AACtD,kBAAO,QAAQ,IAAK,OAAO,OAAO,MAAM,KAAG,IAAG,KAAK,IAAE,MAAO;AAC5D,kBAAO,QAAQ,IAAK,OAAO,OAAO,MAAK,IAAE,OAAQ;AAAA,UAClD,OAAO;AACN,kBAAO,QAAQ,IAAK,OAAO,OAAO,MAAM,KAAG,KAAI,OAAQ;AACvD,kBAAO,QAAQ,IAAK,OAAO,OAAO,MAAM,KAAG,IAAG,OAAQ;AACtD,kBAAO,QAAQ,IAAK,OAAO,OAAO,MAAK,IAAE,OAAQ;AAAA,UAClD;AAAA,QACD;AACA,eAAO,MAAM;AAAA,MACd;AACA,MAAAD,OAAM,QAAQ;AACd,MAAAA,OAAM,OAAO;AACb,MAAAA,OAAM,MAAM;AACZ,MAAAA,OAAM,MAAM;AAAA,IACZ,CAAC;AAAA;AAAA;;;AC7GD;AAAA;AAAA,QAAI,QAAQ;AAEZ,WAAO,UAAU;AAKjB,QAAI,QAAQ,IAAI,WAAW,CAAC;AAC5B,QAAI,QAAQ,IAAI,WAAW,MAAM,MAAM;AACvC,QAAI,SAAS,IAAI,YAAY,MAAM,MAAM;AAEzC,aAAS,cAAe,MAAM;AAC5B,UAAI,KAAK,OAAO;AAAM,cAAM,IAAI,MAAM,0BAA0B;AAChE,UAAI,KAAK,OAAO;AAAM,cAAM,IAAI,MAAM,0BAA0B;AAChE,UAAI,KAAK,OAAO;AAAM,cAAM,IAAI,MAAM,0BAA0B;AAChE,UAAI,KAAK,OAAO;AAAM,cAAM,IAAI,MAAM,0BAA0B;AAChE,UAAI,KAAK,OAAO;AAAM,cAAM,IAAI,MAAM,+EAA+E;AACrH,UAAI,KAAK,OAAO;AAAM,cAAM,IAAI,MAAM,+EAA+E;AACrH,UAAI,KAAK,OAAO;AAAM,cAAM,IAAI,MAAM,0BAA0B;AAChE,UAAI,KAAK,OAAO;AAAM,cAAM,IAAI,MAAM,+EAA+E;AAErH,UAAI,QAAQ;AACZ,UAAI,SAAS,CAAC;AACd,UAAI,MAAM;AAEV,aAAO,MAAM,KAAK,QAAQ;AAGxB,cAAM,KAAK,KAAK;AAChB,cAAM,KAAK,KAAK;AAChB,cAAM,KAAK,KAAK;AAChB,cAAM,KAAK,KAAK;AAGhB,YAAI,SAAS,OAAO,KAAK;AACzB,YAAI,QAAQ,IAAI,WAAW,MAAM;AACjC,cAAM,KAAK,KAAK;AAChB,cAAM,KAAK,KAAK;AAChB,cAAM,KAAK,KAAK;AAChB,cAAM,KAAK,KAAK;AAGhB,YAAI,OACF,OAAO,aAAa,MAAM,EAAE,IAC5B,OAAO,aAAa,MAAM,EAAE,IAC5B,OAAO,aAAa,MAAM,EAAE,IAC5B,OAAO,aAAa,MAAM,EAAE;AAI9B,YAAI,CAAC,OAAO,UAAU,SAAS,QAAQ;AACrC,gBAAM,IAAI,MAAM,qBAAqB;AAAA,QACvC;AAIA,YAAI,SAAS,QAAQ;AACnB,kBAAQ;AACR,iBAAO,KAAK;AAAA,YACV;AAAA,YACA,MAAM,IAAI,WAAW,CAAC;AAAA,UACxB,CAAC;AAED;AAAA,QACF;AAGA,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,gBAAM,KAAK,KAAK;AAAA,QAClB;AAIA,cAAM,KAAK,KAAK;AAChB,cAAM,KAAK,KAAK;AAChB,cAAM,KAAK,KAAK;AAChB,cAAM,KAAK,KAAK;AAEhB,YAAI,YAAY,MAAM;AACtB,YAAI,YAAY,MAAM,IAAI,KAAK;AAC/B,YAAI,cAAc,WAAW;AAC3B,gBAAM,IAAI;AAAA,YACR,oBAAoB,OAAO;AAAA,UAC7B;AAAA,QACF;AAIA,YAAI,YAAY,IAAI,WAAW,MAAM,OAAO,MAAM,CAAC,CAAC;AAEpD,eAAO,KAAK;AAAA,UACV;AAAA,UACA,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAEA,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,uDAAuD;AAAA,MACzE;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;", "names": ["module", "CRC32", "table"]}