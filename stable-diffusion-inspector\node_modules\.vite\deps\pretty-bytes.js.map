{"version": 3, "sources": ["../../pretty-bytes/index.js"], "sourcesContent": ["const BYTE_UNITS = [\n\t'B',\n\t'kB',\n\t'MB',\n\t'GB',\n\t'TB',\n\t'PB',\n\t'EB',\n\t'ZB',\n\t'YB',\n];\n\nconst BIBYTE_UNITS = [\n\t'B',\n\t'KiB',\n\t'MiB',\n\t'GiB',\n\t'TiB',\n\t'PiB',\n\t'EiB',\n\t'ZiB',\n\t'YiB',\n];\n\nconst BIT_UNITS = [\n\t'b',\n\t'kbit',\n\t'Mbit',\n\t'Gbit',\n\t'Tbit',\n\t'Pbit',\n\t'Ebit',\n\t'Zbit',\n\t'Ybit',\n];\n\nconst BIBIT_UNITS = [\n\t'b',\n\t'kibit',\n\t'Mibit',\n\t'Gibit',\n\t'Tibit',\n\t'Pibit',\n\t'Eibit',\n\t'Zibit',\n\t'Yibit',\n];\n\n/*\nFormats the given number using `Number#toLocaleString`.\n- If locale is a string, the value is expected to be a locale-key (for example: `de`).\n- If locale is true, the system default locale is used for translation.\n- If no value for locale is specified, the number is returned unmodified.\n*/\nconst toLocaleString = (number, locale, options) => {\n\tlet result = number;\n\tif (typeof locale === 'string' || Array.isArray(locale)) {\n\t\tresult = number.toLocaleString(locale, options);\n\t} else if (locale === true || options !== undefined) {\n\t\tresult = number.toLocaleString(undefined, options);\n\t}\n\n\treturn result;\n};\n\nexport default function prettyBytes(number, options) {\n\tif (!Number.isFinite(number)) {\n\t\tthrow new TypeError(`Expected a finite number, got ${typeof number}: ${number}`);\n\t}\n\n\toptions = {\n\t\tbits: false,\n\t\tbinary: false,\n\t\tspace: true,\n\t\t...options,\n\t};\n\n\tconst UNITS = options.bits\n\t\t? (options.binary ? BIBIT_UNITS : BIT_UNITS)\n\t\t: (options.binary ? BIBYTE_UNITS : BYTE_UNITS);\n\n\tconst separator = options.space ? ' ' : '';\n\n\tif (options.signed && number === 0) {\n\t\treturn ` 0${separator}${UNITS[0]}`;\n\t}\n\n\tconst isNegative = number < 0;\n\tconst prefix = isNegative ? '-' : (options.signed ? '+' : '');\n\n\tif (isNegative) {\n\t\tnumber = -number;\n\t}\n\n\tlet localeOptions;\n\n\tif (options.minimumFractionDigits !== undefined) {\n\t\tlocaleOptions = {minimumFractionDigits: options.minimumFractionDigits};\n\t}\n\n\tif (options.maximumFractionDigits !== undefined) {\n\t\tlocaleOptions = {maximumFractionDigits: options.maximumFractionDigits, ...localeOptions};\n\t}\n\n\tif (number < 1) {\n\t\tconst numberString = toLocaleString(number, options.locale, localeOptions);\n\t\treturn prefix + numberString + separator + UNITS[0];\n\t}\n\n\tconst exponent = Math.min(Math.floor(options.binary ? Math.log(number) / Math.log(1024) : Math.log10(number) / 3), UNITS.length - 1);\n\tnumber /= (options.binary ? 1024 : 1000) ** exponent;\n\n\tif (!localeOptions) {\n\t\tnumber = number.toPrecision(3);\n\t}\n\n\tconst numberString = toLocaleString(Number(number), options.locale, localeOptions);\n\n\tconst unit = UNITS[exponent];\n\n\treturn prefix + numberString + separator + unit;\n}\n"], "mappings": ";;;AAAA,IAAM,aAAa;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAEA,IAAM,eAAe;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAEA,IAAM,YAAY;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAEA,IAAM,cAAc;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAQA,IAAM,iBAAiB,CAAC,QAAQ,QAAQ,YAAY;AACnD,MAAI,SAAS;AACb,MAAI,OAAO,WAAW,YAAY,MAAM,QAAQ,MAAM,GAAG;AACxD,aAAS,OAAO,eAAe,QAAQ,OAAO;AAAA,EAC/C,WAAW,WAAW,QAAQ,YAAY,QAAW;AACpD,aAAS,OAAO,eAAe,QAAW,OAAO;AAAA,EAClD;AAEA,SAAO;AACR;AAEe,SAAR,YAA6B,QAAQ,SAAS;AACpD,MAAI,CAAC,OAAO,SAAS,MAAM,GAAG;AAC7B,UAAM,IAAI,UAAU,iCAAiC,OAAO,WAAW,QAAQ;AAAA,EAChF;AAEA,YAAU;AAAA,IACT,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,GAAG;AAAA,EACJ;AAEA,QAAM,QAAQ,QAAQ,OAClB,QAAQ,SAAS,cAAc,YAC/B,QAAQ,SAAS,eAAe;AAEpC,QAAM,YAAY,QAAQ,QAAQ,MAAM;AAExC,MAAI,QAAQ,UAAU,WAAW,GAAG;AACnC,WAAO,KAAK,YAAY,MAAM;AAAA,EAC/B;AAEA,QAAM,aAAa,SAAS;AAC5B,QAAM,SAAS,aAAa,MAAO,QAAQ,SAAS,MAAM;AAE1D,MAAI,YAAY;AACf,aAAS,CAAC;AAAA,EACX;AAEA,MAAI;AAEJ,MAAI,QAAQ,0BAA0B,QAAW;AAChD,oBAAgB,EAAC,uBAAuB,QAAQ,sBAAqB;AAAA,EACtE;AAEA,MAAI,QAAQ,0BAA0B,QAAW;AAChD,oBAAgB,EAAC,uBAAuB,QAAQ,uBAAuB,GAAG,cAAa;AAAA,EACxF;AAEA,MAAI,SAAS,GAAG;AACf,UAAMA,gBAAe,eAAe,QAAQ,QAAQ,QAAQ,aAAa;AACzE,WAAO,SAASA,gBAAe,YAAY,MAAM;AAAA,EAClD;AAEA,QAAM,WAAW,KAAK,IAAI,KAAK,MAAM,QAAQ,SAAS,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,MAAM,MAAM,IAAI,CAAC,GAAG,MAAM,SAAS,CAAC;AACnI,aAAW,QAAQ,SAAS,OAAO,QAAS;AAE5C,MAAI,CAAC,eAAe;AACnB,aAAS,OAAO,YAAY,CAAC;AAAA,EAC9B;AAEA,QAAM,eAAe,eAAe,OAAO,MAAM,GAAG,QAAQ,QAAQ,aAAa;AAEjF,QAAM,OAAO,MAAM;AAEnB,SAAO,SAAS,eAAe,YAAY;AAC5C;", "names": ["numberString"]}