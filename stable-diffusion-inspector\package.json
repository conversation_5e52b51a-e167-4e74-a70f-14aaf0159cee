{"name": "stable-diffusion-inspector", "version": "1.0.0", "type": "module", "license": "GPL-v3", "scripts": {"dev": "vite", "build": "cross-env VITE_COMMIT_HASH=$(git rev-parse --short HEAD) vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.0.10", "@vueuse/core": "^13.3.0", "element-plus": "^2.2.18", "exifreader": "^4.5.0", "file-saver": "^2.0.5", "file-type": "^17.1.4", "lucide-vue-next": "^0.517.0", "mime-types": "^2.1.35", "pako": "^2.1.0", "png-chunk-text": "^1.0.0", "png-chunks-extract": "^1.0.0", "pretty-bytes": "^6.0.0", "vue": "^3.2.37", "vue-clipboard3": "^2.0.0", "vue-json-viewer": "^3.0.4"}, "devDependencies": {"@types/file-saver": "^2.0.5", "@types/mime-types": "^2.1.1", "@vitejs/plugin-vue": "^3.0.0", "cross-env": "^7.0.3", "unocss": "^0.44.7", "vite": "^3.1.0", "vite-plugin-pwa": "^0.13.3"}}