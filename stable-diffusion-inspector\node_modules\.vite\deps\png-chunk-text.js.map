{"version": 3, "sources": ["../../png-chunk-text/encode.js", "../../png-chunk-text/decode.js", "../../png-chunk-text/index.js"], "sourcesContent": ["module.exports = encode\n\nfunction encode (keyword, content) {\n  keyword = String(keyword)\n  content = String(content)\n\n  if (!/^[\\x00-\\xFF]+$/.test(keyword) || !/^[\\x00-\\xFF]+$/.test(content)) {\n    throw new Error('Only Latin-1 characters are permitted in PNG tEXt chunks. You might want to consider base64 encoding and/or zEXt compression')\n  }\n\n  if (keyword.length >= 80) {\n    throw new Error('Keyword \"' + keyword + '\" is longer than the 79-character limit imposed by the PNG specification')\n  }\n\n  var totalSize = keyword.length + content.length + 1\n  var output = new Uint8Array(totalSize)\n  var idx = 0\n  var code\n\n  for (var i = 0; i < keyword.length; i++) {\n    if (!(code = keyword.charCodeAt(i))) {\n      throw new Error('0x00 character is not permitted in tEXt keywords')\n    }\n\n    output[idx++] = code\n  }\n\n  output[idx++] = 0\n\n  for (var j = 0; j < content.length; j++) {\n    if (!(code = content.charCodeAt(j))) {\n      throw new Error('0x00 character is not permitted in tEXt content')\n    }\n\n    output[idx++] = code\n  }\n\n  return {\n    name: 'tEXt',\n    data: output\n  }\n}\n", "module.exports = decode\n\nfunction decode (data) {\n  if (data.data && data.name) {\n    data = data.data\n  }\n\n  var naming = true\n  var text = ''\n  var name = ''\n\n  for (var i = 0; i < data.length; i++) {\n    var code = data[i]\n\n    if (naming) {\n      if (code) {\n        name += String.fromCharCode(code)\n      } else {\n        naming = false\n      }\n    } else {\n      if (code) {\n        text += String.fromCharCode(code)\n      } else {\n        throw new Error('Invalid NULL character found. 0x00 character is not permitted in tEXt content')\n      }\n    }\n  }\n\n  return {\n    keyword: name,\n    text: text\n  }\n}\n", "exports.encode = require('./encode')\nexports.decode = require('./decode')\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,WAAO,UAAU;AAEjB,aAAS,OAAQ,SAAS,SAAS;AACjC,gBAAU,OAAO,OAAO;AACxB,gBAAU,OAAO,OAAO;AAExB,UAAI,CAAC,iBAAiB,KAAK,OAAO,KAAK,CAAC,iBAAiB,KAAK,OAAO,GAAG;AACtE,cAAM,IAAI,MAAM,8HAA8H;AAAA,MAChJ;AAEA,UAAI,QAAQ,UAAU,IAAI;AACxB,cAAM,IAAI,MAAM,cAAc,UAAU,0EAA0E;AAAA,MACpH;AAEA,UAAI,YAAY,QAAQ,SAAS,QAAQ,SAAS;AAClD,UAAI,SAAS,IAAI,WAAW,SAAS;AACrC,UAAI,MAAM;AACV,UAAI;AAEJ,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,YAAI,EAAE,OAAO,QAAQ,WAAW,CAAC,IAAI;AACnC,gBAAM,IAAI,MAAM,kDAAkD;AAAA,QACpE;AAEA,eAAO,SAAS;AAAA,MAClB;AAEA,aAAO,SAAS;AAEhB,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,YAAI,EAAE,OAAO,QAAQ,WAAW,CAAC,IAAI;AACnC,gBAAM,IAAI,MAAM,iDAAiD;AAAA,QACnE;AAEA,eAAO,SAAS;AAAA,MAClB;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;;;ACzCA;AAAA;AAAA,WAAO,UAAU;AAEjB,aAAS,OAAQ,MAAM;AACrB,UAAI,KAAK,QAAQ,KAAK,MAAM;AAC1B,eAAO,KAAK;AAAA,MACd;AAEA,UAAI,SAAS;AACb,UAAI,OAAO;AACX,UAAI,OAAO;AAEX,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,YAAI,OAAO,KAAK;AAEhB,YAAI,QAAQ;AACV,cAAI,MAAM;AACR,oBAAQ,OAAO,aAAa,IAAI;AAAA,UAClC,OAAO;AACL,qBAAS;AAAA,UACX;AAAA,QACF,OAAO;AACL,cAAI,MAAM;AACR,oBAAQ,OAAO,aAAa,IAAI;AAAA,UAClC,OAAO;AACL,kBAAM,IAAI,MAAM,+EAA+E;AAAA,UACjG;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,SAAS;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;ACjCA;AAAA;AAAA,YAAQ,SAAS;AACjB,YAAQ,SAAS;AAAA;AAAA;", "names": []}